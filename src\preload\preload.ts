import { electronAPI } from '@electron-toolkit/preload'
import { contextBridge, webUtils } from 'electron'

// Custom APIs for renderer
const api = {
  // 添加获取文件路径的方法
  getFilePathFromFile: (file) => {
    try {
      return webUtils.getPathForFile(file)
    }
    catch (error) {
      console.error('获取文件路径失败:', error)
      return null
    }
  },
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  }
  catch (error) {
    console.error(error)
  }
}
else {
  window.electron = electronAPI
  window.api = api
  window.platform = process.platform
}
