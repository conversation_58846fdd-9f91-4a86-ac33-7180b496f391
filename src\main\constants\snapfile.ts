import { errorStatusEnum } from './status'

/**
 * Snapfile事件类型常量
 * 定义了与snapfile进程通信时使用的事件类型
 */
export const SnapfileEventType = {
  /** 开始任务 */
  START_TASK: 'start-task',
  /** 删除任务 */
  DELETE_TASK: 'delete-task',
  /** 更新最大下载任务数 */
  UPDATE_MAX_DOWNLOAD_TASK: 'update-max-download-task',
  /** 停止录制直播 */
  STOP_RECORDING_LIVE: 'stop-recording-live',
} as const

/**
 * Snapfile回调事件类型常量
 * 定义了项目内部使用的事件名称，用于EventEmitter
 */
export const SnapfileCallbackEvent = {
  /** 通用响应事件 */
  RESPONSE: 'response',
  /** 状态变更事件 */
  STATUS_CHANGE: 'status-change',
  /** 进度更新事件 */
  PROGRESS: 'progress',
  /** 任务完成事件 */
  COMPLETE: 'complete',
  /** 错误事件 */
  ERROR: 'error',
} as const

/**
 * Snapfile回调类型常量
 * 定义了处理器中使用的回调函数类型名称
 */
export const SnapfileCallbackType = {
  /** 状态变更回调 */
  ON_STATUS_CHANGE: 'onStatusChange',
  /** 进度更新回调 */
  ON_PROGRESS: 'onProgress',
  /** 任务完成回调 */
  ON_COMPLETE: 'onComplete',
  /** 通用响应回调 */
  ON_RESPONSE: 'onResponse',
} as const

/**
 * Snapfile状态码常量
 * 定义了snapfile进程返回的各种状态码及其含义
 */
export const SnapfileStatusCode = {
  // ==================== 任务状态码 ====================

  /** 任务完成 */
  task_complete: 'task_complete',
  /** 更新任务下载进度 */
  task_download_progress: 'task_download_progress',
  /** 更新任务转换进度 */
  task_conversion_progress: 'task_conversion_progress',
  /** 任务开始 */
  task_started: 'task_started',
  /** 任务已删除 */
  task_deleted: 'task_deleted',
  /** 任务开始预处理/预处理中 */
  task_start_prepare: 'task_start_prepare',
  /** 任务预处理完成 */
  task_prepared: 'task_prepared',
  /** 任务开始下载/下载中 */
  task_start_download: 'task_start_download',
  /** 任务下载完成 */
  task_downloaded: 'task_downloaded',
  /** 任务等待转换 */
  task_pending_conversion: 'task_pending_conversion',
  /** 任务开始转换/转换中 */
  task_start_conversion: 'task_start_conversion',
  /** 任务转换完成 */
  task_converted: 'task_converted',
  /** 任务开始移动/移动中 */
  task_start_move: 'task_start_move',
  /** 任务移动完成 */
  task_moved: 'task_moved',
  /** 停止下载直播，进度转换阶段 */
  stop_recording_live: 'stop_recording_live',
  /** 等待下载 */
  task_pending_download: 'task_pending_download',
  /** 检测到任务是直播 */
  task_live_detected: 'task_live_detected',

  // ==================== 客户端错误 ====================

  /** 未知事件 */
  unknown_event: 'unknown_event',
  /** 任务已开始 */
  task_already_started: 'task_already_started',

  // ==================== 服务端错误 ====================

  /** 未知错误 */
  unknown_error: 'unknown_error',
  /** 准备阶段错误 */
  prepare_error: 'prepare_error',
  /** 准备阶段 m3u8 解析错误 */
  parse_m3u8_error: 'parse_m3u8_error',
  /** 下载阶段错误 */
  download_error: 'download_error',
  /** 转换阶段错误 */
  convert_error: 'convert_error',
  /** 移动阶段错误 */
  move_error: 'move_error',
  /** HTTP 403 错误 */
  http_status_forbidden_error: 'http_status_forbidden_error',
  /** 磁盘已满 */
  disk_full: 'disk_full',
  /** 文件权限不足 */
  os_permission_denied: 'os_permission_denied',
  /** 某个文件下载失败 */
  file_download_error: 'file_download_error',
} as const

/**
 * Snapfile错误状态码类型
 */
type SnapfileErrorStatusCode =
  | 'unknown_event'
  | 'task_already_started'
  | 'unknown_error'
  | 'prepare_error'
  | 'parse_m3u8_error'
  | 'download_error'
  | 'convert_error'
  | 'move_error'
  | 'http_status_forbidden_error'
  | 'disk_full'
  | 'os_permission_denied'
  | 'file_download_error'

/**
 * Snapfile错误状态码集合
 * 使用Set提供O(1)时间复杂度的错误状态码查找
 */
export const SnapfileErrorStatusCodes = new Set<SnapfileErrorStatusCode>([
  // ==================== 客户端错误 ====================
  SnapfileStatusCode.unknown_event,
  SnapfileStatusCode.task_already_started,

  // ==================== 服务端错误 ====================
  SnapfileStatusCode.unknown_error,
  SnapfileStatusCode.prepare_error,
  SnapfileStatusCode.parse_m3u8_error,
  SnapfileStatusCode.download_error,
  SnapfileStatusCode.convert_error,
  SnapfileStatusCode.move_error,
  SnapfileStatusCode.http_status_forbidden_error,
  SnapfileStatusCode.disk_full,
  SnapfileStatusCode.os_permission_denied,
  SnapfileStatusCode.file_download_error,
])

/**
 * Snapfile状态到项目状态的映射关系
 * 定义了snapfile状态如何映射到项目的TaskStatus
 */
export const SnapfileStatusMapping = {
  /** 任务开始和预处理阶段 → 准备下载 */
  READY_DOWNLOAD_STATUSES: [
    SnapfileStatusCode.task_started,
    SnapfileStatusCode.task_start_prepare,
    SnapfileStatusCode.task_prepared,
    SnapfileStatusCode.task_pending_download,
  ],
  /** 下载阶段 → 下载中 */
  DOWNLOADING_STATUSES: [
    SnapfileStatusCode.task_start_download,
    SnapfileStatusCode.task_downloaded,
  ],
  /** 等待转换阶段 → 等待转换 */
  PENDING_CONVERSION_STATUSES: [
    SnapfileStatusCode.task_pending_conversion,
  ],
  /** 转换和移动阶段 → 转换中 */
  CONVERTING_STATUSES: [
    SnapfileStatusCode.task_start_conversion,
    SnapfileStatusCode.task_converted,
    SnapfileStatusCode.task_start_move,
    SnapfileStatusCode.task_moved,
  ],
} as const

/**
 * Snapfile错误状态码到项目错误状态的映射关系
 * 定义了snapfile错误如何映射到项目的ErrorStatus
 */
export const SnapfileErrorMapping = {
  // 客户端错误映射
  [SnapfileStatusCode.unknown_event]: errorStatusEnum.downloadError,
  [SnapfileStatusCode.task_already_started]: errorStatusEnum.downloadError,

  // 服务端错误映射
  [SnapfileStatusCode.unknown_error]: errorStatusEnum.downloadError,
  [SnapfileStatusCode.prepare_error]: errorStatusEnum.downloadError,
  [SnapfileStatusCode.parse_m3u8_error]: errorStatusEnum.downloadError,
  [SnapfileStatusCode.download_error]: errorStatusEnum.downloadError,
  [SnapfileStatusCode.convert_error]: errorStatusEnum.convertError,
  [SnapfileStatusCode.move_error]: errorStatusEnum.moveError,
  [SnapfileStatusCode.http_status_forbidden_error]: errorStatusEnum.downloadError,
  [SnapfileStatusCode.disk_full]: errorStatusEnum.downloadError,
  [SnapfileStatusCode.os_permission_denied]: errorStatusEnum.downloadError,
  [SnapfileStatusCode.file_download_error]: errorStatusEnum.downloadError,
} as const

/**
 * 状态码处理器映射表
 * 定义了每个状态码对应的事件名称和回调类型
 */
export const SnapfileStatusHandlers = {
  // 状态变更处理器
  [SnapfileStatusCode.task_started]: {
    event: SnapfileCallbackEvent.STATUS_CHANGE,
    callbackType: SnapfileCallbackType.ON_STATUS_CHANGE,
  },
  [SnapfileStatusCode.task_start_prepare]: {
    event: SnapfileCallbackEvent.STATUS_CHANGE,
    callbackType: SnapfileCallbackType.ON_STATUS_CHANGE,
  },
  [SnapfileStatusCode.task_prepared]: {
    event: SnapfileCallbackEvent.STATUS_CHANGE,
    callbackType: SnapfileCallbackType.ON_STATUS_CHANGE,
  },
  [SnapfileStatusCode.task_pending_download]: {
    event: SnapfileCallbackEvent.STATUS_CHANGE,
    callbackType: SnapfileCallbackType.ON_STATUS_CHANGE,
  },
  [SnapfileStatusCode.task_start_download]: {
    event: SnapfileCallbackEvent.STATUS_CHANGE,
    callbackType: SnapfileCallbackType.ON_STATUS_CHANGE,
  },
  [SnapfileStatusCode.task_downloaded]: {
    event: SnapfileCallbackEvent.STATUS_CHANGE,
    callbackType: SnapfileCallbackType.ON_STATUS_CHANGE,
  },
  [SnapfileStatusCode.task_pending_conversion]: {
    event: SnapfileCallbackEvent.STATUS_CHANGE,
    callbackType: SnapfileCallbackType.ON_STATUS_CHANGE,
  },
  [SnapfileStatusCode.task_start_conversion]: {
    event: SnapfileCallbackEvent.STATUS_CHANGE,
    callbackType: SnapfileCallbackType.ON_STATUS_CHANGE,
  },
  [SnapfileStatusCode.task_converted]: {
    event: SnapfileCallbackEvent.STATUS_CHANGE,
    callbackType: SnapfileCallbackType.ON_STATUS_CHANGE,
  },
  [SnapfileStatusCode.task_start_move]: {
    event: SnapfileCallbackEvent.STATUS_CHANGE,
    callbackType: SnapfileCallbackType.ON_STATUS_CHANGE,
  },
  [SnapfileStatusCode.task_moved]: {
    event: SnapfileCallbackEvent.STATUS_CHANGE,
    callbackType: SnapfileCallbackType.ON_STATUS_CHANGE,
  },
  [SnapfileStatusCode.task_live_detected]: {
    event: SnapfileCallbackEvent.STATUS_CHANGE,
    callbackType: SnapfileCallbackType.ON_STATUS_CHANGE,
  },
  // 进度处理器
  [SnapfileStatusCode.task_download_progress]: {
    event: SnapfileCallbackEvent.PROGRESS,
    callbackType: SnapfileCallbackType.ON_PROGRESS,
  },
  [SnapfileStatusCode.task_conversion_progress]: {
    event: SnapfileCallbackEvent.PROGRESS,
    callbackType: SnapfileCallbackType.ON_PROGRESS,
  },
  // 完成处理器
  [SnapfileStatusCode.task_complete]: {
    event: SnapfileCallbackEvent.COMPLETE,
    callbackType: SnapfileCallbackType.ON_COMPLETE,
    shouldCleanup: true,
  },
  // 删除处理器
  [SnapfileStatusCode.task_deleted]: {
    event: SnapfileCallbackEvent.STATUS_CHANGE,
    callbackType: SnapfileCallbackType.ON_STATUS_CHANGE,
    shouldCleanup: true,
  },
  // 特殊操作处理器
  [SnapfileStatusCode.stop_recording_live]: {
    event: SnapfileCallbackEvent.RESPONSE,
    callbackType: SnapfileCallbackType.ON_RESPONSE,
  },
} as const
