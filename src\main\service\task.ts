import type { SelectTask } from '@main/lib/db/schema'
import type { RendererHandlers } from '@main/renderer-handlers'
import type {
  DownloadItem,
  MediaInfo,
  SettingStoreType,
  SubtitleFormat,
  TaskItem,
  TaskStatus,
  TempTask,
  TempTaskProgress,
  YtDlpFormat,
  YtDlpResponse,
} from '@main/types'

import { createWriteStream } from 'node:fs'
import fs from 'node:fs/promises'
import path from 'node:path'
import { trackEvent } from '@aptabase/electron/main'
import { getRendererHandlers } from '@egoist/tipc/main'
import { isDev } from '@main/constants/env'
import { SnapfileStatusCode } from '@main/constants/snapfile'
import { actionEnum, errorMessageEnum, errorStatusEnum, taskStatus } from '@main/constants/status'
import { db } from '@main/lib/db'
import { task } from '@main/lib/db/schema'
import { logError, logInfo, logWarn } from '@main/lib/logger'
import { settingStore } from '@main/lib/store'
import {
  ensureDirectoryExists,
  formatTime,
  getBase64Image,
  getExtensionFromHeaders,
  getFilePathMediaInfo,
  getHttpHeaders,
  getVideoResolution,
  isUrlDuplicate,
  md5,
} from '@main/utils'
import { fetch } from '@main/utils/fetch'
import { mapSnapfileErrorToErrorStatus, mapSnapfileStatusToTaskStatus } from '@main/utils/snapfile'

import { getMainWindow } from '@main/window'
import { and, desc, eq, inArray, isNull, ne, not } from 'drizzle-orm'
import { app } from 'electron'
import { v4 as uuidv4 } from 'uuid'
import ffmpegService from './ffmpeg'
import ProxyService from './proxy'
import snapfileService from './snapfile'
import ytDlpService from './yt-dlp'

// 临时任务map
export const tempTaskMap = new Map<string, TempTask>()
export const tempTaskProgressMap = new Map<string, TempTaskProgress>()

class TaskService {
  constructor() { }

  // 获取任务列表
  public async getTaskList(): Promise<TaskItem[]> {
    const tasks = await db.select().from(task).orderBy(desc(task.createdAt)).all()
    const list = tasks.map((item) => {
      return {
        ...item,
        ...tempTaskProgressMap.get(item.id),
      }
    })
    return list
  }

  // 删除任务
  public async deleteTask(taskId: string): Promise<void> {
    await db.delete(task).where(eq(task.id, taskId))
  }

  public async getInterruptTasks(): Promise<SelectTask[]> {
    const tasks = await db.select().from(task).where(
      inArray(
        task.taskStatus,
        [taskStatus.extracting, taskStatus.readyDownload, taskStatus.downloading, taskStatus.pendingConversion, taskStatus.converting],
      ),
    ).all()
    return tasks
  }

  // 删除所有任务
  public async deleteTaskList(isDeleteDownloading: boolean): Promise<SelectTask[]> {
    if (isDeleteDownloading) {
      // 获取所有任务
      const tasksToDelete = await db.select().from(task).all()
      // 删除所有任务
      await db.delete(task).execute()
      return tasksToDelete
    }
    else {
      // 获取真正正在下载中且没有错误的任务（这些任务将被保留），返回被删除的任务
      const downloadingTasks = await db.select().from(task).where(
        and(
          inArray(task.taskStatus, [
            taskStatus.extracting,
            taskStatus.downloading,
            taskStatus.pendingConversion,
            taskStatus.converting,
            taskStatus.readyDownload,
          ]),
          isNull(task.errorMessage),
        ),
      ).all()
      // 查询出除下载中任务外的所有任务
      const tasksToDelete = await db.select().from(task).where(not(inArray(task.id, downloadingTasks.map(item => item.id)))).all()
      // 删除除下载中任务外的所有任务
      await db.delete(task).where(not(inArray(task.id, downloadingTasks.map(item => item.id)))).execute()
      return tasksToDelete
    }
  }

  public async parseTask(task: SelectTask): Promise<YtDlpResponse> {
    const handlers = getRendererHandlers<RendererHandlers>(getMainWindow().webContents)
    let ytDlpResponse: YtDlpResponse = { url: task.url }
    let status: string = taskStatus.extracting
    let errorMessage: string = errorMessageEnum.extractError
    let errorStatus = null
    let errorAction = null
    let isError = false
    try {
      const response = await ytDlpService.getParseInfo(task.url, task.id)
      if (response) {
        ytDlpResponse = response
        logInfo('yt-dlp解析成功', {
          taskId: task.id,
          url: task.url,
          title: response.title,
          formatCount: response.formats?.length,
          isLive: response.is_live,
        })
      }
      trackEvent('提取成功', {
        url: task.url,
      })
    }
    catch (error: any) {
      isError = true
      trackEvent('提取失败', {
        url: task.url,
      })
      status = taskStatus.failed
      logError('获取yt-dlp解析信息失败', {
        taskId: task.id,
        url: task.url,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      })
      let parseErrorMessage = error instanceof Error ? error.message : error
      parseErrorMessage = parseErrorMessage.toLowerCase()
      if (parseErrorMessage.includes('unsupported url')
        || parseErrorMessage.includes('drm protection')
        || (parseErrorMessage.includes('douyin') && parseErrorMessage.includes('fresh cookies'))) {
        status = taskStatus.failed
        errorMessage = errorMessageEnum.unsupportedUrl
        errorStatus = errorStatusEnum.unsupportedUrl
      }
      else if (parseErrorMessage.includes('authentication')
        || parseErrorMessage.includes('necessarily logged in')
      ) {
        status = taskStatus.extracting
        errorStatus = errorStatusEnum.extractError
        errorMessage = errorMessageEnum.needLogin
        errorAction = actionEnum.login
      }
      else if (parseErrorMessage.includes('no video formats found')) {
        status = taskStatus.failed
        errorStatus = errorStatusEnum.extractError
        errorMessage = errorMessageEnum.noVideoFormats
      }
      else if (
        parseErrorMessage.includes('establish a new connection')
        || parseErrorMessage.includes('read time out')
        || parseErrorMessage.includes('read timed out')
        || parseErrorMessage.includes('timeout was reached')
        || parseErrorMessage.includes('sslerror')
        || parseErrorMessage.includes('httperror 403')
        || (parseErrorMessage.includes('pornhub') && parseErrorMessage.includes('getaddrinfo failed'))
      ) {
        status = taskStatus.extracting
        errorStatus = errorStatusEnum.extractError
        errorMessage = errorMessageEnum.timeout
      }
      else if (parseErrorMessage.includes('need to purchase')) {
        status = taskStatus.extracting
        errorStatus = errorStatusEnum.extractError
        errorMessage = errorMessageEnum.needPurchase
      }
      else if (parseErrorMessage.includes('object has no attribute')
        || parseErrorMessage.includes('request is blocked')
      ) {
        status = taskStatus.extracting
        errorStatus = errorStatusEnum.extractError
        errorMessage = errorMessageEnum.serverError
      }
      else if (parseErrorMessage.includes('video unavailable')) {
        status = taskStatus.extracting
        errorStatus = errorStatusEnum.extractError
        errorMessage = errorMessageEnum.videoNotAccess
      }

      // 更新任务状态
      await this.updateTask(task.id, {
        taskStatus: status,
        errorStatus,
        errorMessage,
        errorAction,
      })
      handlers.onDownloadProgress.send({
        ...task,
        taskStatus: status as TaskStatus,
        taskId: task.id,
        totalSize: null,
        downloadedSize: null,
        speed: null,
        eta: null,
        errorStatus,
        errorMessage,
        errorAction,
        isLive: task.isLive || false,
      })
    }
    if (isError) {
      return null
    }
    return ytDlpResponse
  }

  // 保存下载任务
  public async saveTaskByUrls(urls: string[]): Promise<SelectTask[]> {
    const taskList: SelectTask[] = []

    for (const url of urls) {
      const tempTask: TempTask = {
        text: url,
        thumbnail: null,
        items: [],
        setting: settingStore.store,
      }
      taskList.push({
        id: uuidv4(),
        text: '',
        url,
        filePath: null,
        fileSize: null,
        taskStatus: taskStatus.extracting,
        thumbnail: null,
        extension: null,
        resolutionWidth: null,
        resolutionHeight: null,
        bitrate: null,
        duration: null,
        errorStatus: null,
        errorMessage: null,
        errorAction: null,
        tempTask: JSON.stringify(tempTask),
        requestHeaders: null,
        isLive: false,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      })
    }
    await db.insert(task).values(taskList)
    return taskList
  }

  // 保存嗅探任务
  public async saveSnifferTask(taskData: SelectTask): Promise<void> {
    await db.insert(task).values(taskData)
  }

  public async cancelTaskList() {
    try {
      // 取消所有未完成的任务
      const tasks = await db.select().from(task).where(
        and(
          ne(task.taskStatus, taskStatus.completed),
          ne(task.taskStatus, taskStatus.failed),
        ),
      ).all()
      const mainWindow = getMainWindow()
      const handlers = getRendererHandlers<RendererHandlers>(mainWindow.webContents)

      for (const task of tasks) {
        try {
          // 无论任务状态如何，都尝试取消yt-dlp进程
          ytDlpService.cancelYtDlpProcess(task.id)
          ffmpegService.cancelFFmpegProcess(task.id)
          // 取消snapfile任务
          snapfileService.cancelTask(task.id)

          await this.updateTask(task.id, {
            errorStatus: errorStatusEnum.interrupted,
            errorMessage: errorMessageEnum.interrupted,
          })

          handlers.onDownloadProgress.send({
            ...task,
            taskStatus: task.taskStatus as TaskStatus,
            taskId: task.id,
            eta: null,
            totalSize: null,
            downloadedSize: null,
            speed: null,
            errorAction: null,
            errorStatus: errorStatusEnum.interrupted,
            errorMessage: errorMessageEnum.interrupted,
            isLive: task.isLive || false,
          })
        }
        catch (err) {
          logError('取消任务失败', {
            taskId: task.id,
            error: err instanceof Error ? err.message : String(err),
            stack: err instanceof Error ? err.stack : undefined,
          })
          // 继续处理下一个任务，不中断整个流程
          continue
        }
      }
    }
    catch (err) {
      logError('批量取消任务失败', {
        error: err instanceof Error ? err.message : String(err),
        stack: err instanceof Error ? err.stack : undefined,
      })
    }
  }

  // 更新任务
  public async updateTask(taskId: string, data: Partial<SelectTask>): Promise<void> {
    await db.update(task).set(data).where(eq(task.id, taskId))
  }

  // 获取需要下载的下载项列表
  public async getNeedDownloadItems(task: SelectTask, data: YtDlpResponse, setting: SettingStoreType): Promise<DownloadItem[]> {
    // 检查data是否为空
    if (!data) {
      logWarn('getNeedDownloadItems: data为空，返回空数组', {
        taskId: task.id,
        url: task.url,
      })
      return [] as DownloadItem[]
    }

    // 特殊处理，如果data.is_live为true，移除data.formats中所有ext为flv并且vcodec值为"hevc"的项
    if (data.is_live === true) {
      data.formats = data.formats.filter(item => !(item.ext === 'flv' && item.vcodec === 'hevc'))
    }

    const downloadItems: DownloadItem[] = []
    const headers = getHttpHeaders(data)
    // 获取标题
    const text = data.title
      || data.fulltitle
      || data.description
      || data.channel
      || data.uploader
    // 如果是直链直接返回
    if (data.direct && data.url) {
      downloadItems.push({
        url: data.url,
        headers,
      })

      // 获取封面图
      const thumbnail = data?.thumbnail

      // 创建临时任务对象
      const tempTask = {
        text,
        thumbnail,
        items: downloadItems,
        setting,
      }

      // 保存临时任务到数据库中
      await this.updateTask(task.id, {
        text,
        tempTask: JSON.stringify(tempTask),
      })
      // 保存临时任务到内存中
      tempTaskMap.set(task.id, tempTask)

      return downloadItems
    }
    if (!data.formats || data.formats.length === 0) {
      return downloadItems
    }
    // 获取封面图
    const thumbnail = data.thumbnail ? data.thumbnail : data.url
    // 选择音频
    const audio = this.selectAudioBySetting(data, setting)
    // 选择视频
    if (setting.downloadType === 'video') {
      // 选择视频
      const video = this.selectVideoBySetting(data, setting)
      if (video) {
        downloadItems.push({
          ...video,
          headers,
        })
      }
      // 选择字幕
      const subtitle = this.selectSubtitleBySetting(data, setting)
      if (subtitle) {
        downloadItems.push(...subtitle.map(item => ({
          ...item,
          headers,
          optionalDownload: true, // 字幕下载失败不影响整个任务
        })))
      }
    }

    if (downloadItems.length === 0) {
      downloadItems.push(...audio.map(item => ({
        ...item,
        headers,
      })))
    }
    else {
      // 如果是直播，则不再筛选音频
      if (data.is_live !== true) {
        const notAudio = downloadItems.every(item =>
          !item.acode
          || ['none', 'undefined', 'null'].includes(item.acode))

        // 检查audio中的语言是否被downloadItems完全包含
        const existingLanguages = new Set(
          downloadItems
            .filter(item => item.acode && !['none', 'undefined', 'null'].includes(item.acode))
            .map(item => item.language || 'default'),
        )

        const audioLanguages = audio.map(item => item.language || 'default')
        const hasUncoveredLanguages = audioLanguages.some(lang => !existingLanguages.has(lang))

        if (notAudio || hasUncoveredLanguages) {
          downloadItems.push(...audio.map(item => ({
            ...item,
            headers,
          })))
        }
      }
    }
    logInfo('生成下载项列表', {
      taskId: task.id,
      url: task.url,
      downloadItemsCount: downloadItems.length,
    })
    const uniqueDownloadItems = downloadItems.reduce((acc, current) => {
      const isDuplicate = acc.some(item => item.url === current.url)
      if (!isDuplicate) {
        acc.push(current)
      }
      return acc
    }, [] as DownloadItem[])

    // 在视频筛选逻辑之后，将封面图追加到文件列表的最后
    if (thumbnail && setting.isDownloadThumbnail) {
      console.log('添加封面图到下载列表最后:', thumbnail)
      uniqueDownloadItems.push({
        url: thumbnail,
        headers,
        optionalDownload: true, // 封面图下载失败不影响整个任务
      })
    }
    else {
      console.log('跳过封面图下载:', {
        hasThumbnail: !!thumbnail,
        isDownloadThumbnail: setting.isDownloadThumbnail,
      })
    }

    const tempTask = {
      text,
      thumbnail,
      items: uniqueDownloadItems,
      setting,
    }
    // 保存临时任务到数据库中
    await this.updateTask(task.id, {
      text,
      tempTask: JSON.stringify(tempTask),
    })
    // 保存临时任务到内存中
    tempTaskMap.set(task.id, tempTask)
    return uniqueDownloadItems
  }

  // 下载封面并保存
  private async downloadThumbnail(taskId: string, downloadTempPath: string): Promise<DownloadItem> {
    // 先从内存中获取临时任务
    let tempTask = tempTaskMap.get(taskId)
    if (!tempTask) {
      const task = await this.getTaskById(taskId)
      if (!task.tempTask) {
        return null
      }
      tempTask = JSON.parse(task.tempTask)
    }
    if (!tempTask.thumbnail) {
      return
    }
    const { thumbnail, text, items } = tempTask
    const filePathDir = path.join(downloadTempPath, 'temp')
    const data = await ensureDirectoryExists(filePathDir)
    if (!data.success) {
      if (data.error) {
        logError('封面图下载失败', { taskId, error: data.error })
        if (data.error.includes('not permitted') || data.error.includes('permission denied')) {
        // 更新任务状态为失败
          await this.updateTask(taskId, {
            taskStatus: taskStatus.failed,
            errorStatus: errorStatusEnum.extractError,
            errorMessage: errorMessageEnum.permissionDenied,
          })

          // 获取最新的任务信息并通知前端
          const updatedTask = await this.getTaskById(taskId)
          const handlers = getRendererHandlers<RendererHandlers>(getMainWindow().webContents)
          handlers.onDownloadProgress.send({
            ...updatedTask,
            taskId: updatedTask.id,
            taskStatus: taskStatus.failed,
            totalSize: null,
            downloadedSize: null,
            speed: null,
            eta: null,
            errorAction: null,
            errorStatus: errorStatusEnum.extractError,
            errorMessage: errorMessageEnum.permissionDenied,
            isLive: updatedTask.isLive || false,
          })
        }
        else {
          logError('封面图下载失败', { taskId, error: data.error })
          await this.updateTask(taskId, {
            taskStatus: taskStatus.failed,
            errorStatus: errorStatusEnum.extractError,
            errorMessage: errorMessageEnum.extractError,
          })

          // 获取最新的任务信息并通知前端
          const updatedTask = await this.getTaskById(taskId)
          const handlers = getRendererHandlers<RendererHandlers>(getMainWindow().webContents)
          handlers.onDownloadProgress.send({
            ...updatedTask,
            taskId: updatedTask.id,
            taskStatus: taskStatus.failed,
            totalSize: null,
            downloadedSize: null,
            speed: null,
            eta: null,
            errorAction: null,
            errorStatus: errorStatusEnum.extractError,
            errorMessage: errorMessageEnum.extractError,
            isLive: updatedTask.isLive || false,
          })
        }
      }
      return
    }
    let headers = {}
    if (items.length > 0) {
      headers = items[0].headers
    }

    const resp = await fetch({ url: thumbnail, method: 'GET', headers })
    if (resp.statusCode !== 200) {
      return null
    }
    const ext = getExtensionFromHeaders(thumbnail, resp.headers)
    const filePath = path.join(filePathDir, `${md5(thumbnail)}.${ext}`)

    // 创建写入流以正确处理多个数据块
    const writer = createWriteStream(filePath)
    await new Promise<void>((resolve, reject) => {
      resp.on('data', (chunk) => {
        writer.write(chunk)
      })

      resp.on('end', () => {
        writer.end(() => resolve())
      })

      resp.on('error', (error) => {
        writer.end()
        reject(error)
      })

      writer.on('error', (error) => {
        writer.end()
        reject(error)
      })
    })
    const base64Image = await getBase64Image(filePath)
    const extension = ext.split('.').pop()
    await this.updateTask(taskId, {
      text,
      thumbnail: `data:image/${extension};base64,${base64Image}`,
      extension,
    })
    return {
      url: thumbnail,
      tempFilePath: filePath,
    }
  }

  // 获取任务
  public async getTaskById(taskId: string): Promise<SelectTask> {
    const tasks = await db.select().from(task).where(eq(task.id, taskId)).limit(1).all()
    if (tasks.length === 0) {
      throw new Error('任务不存在')
    }
    return tasks[0]
  }

  // public async download(taskId: string, items: DownloadItem[], setting: SettingStoreType) {
  //   // 使用snapfile进行下载
  //   await this.downloadWithSnapfile(taskId, items, setting)
  // }

  /**
   * 使用snapfile进行下载
   */
  public async downloadWithSnapfile(taskId: string, items: DownloadItem[], setting: SettingStoreType) {
    const handlers = getRendererHandlers<RendererHandlers>(getMainWindow().webContents)
    const appName = app.getName()
    const downloadPath = setting.downloadPath
    const downloadTempPath = path.join(downloadPath, `.${appName}`, taskId)

    // 创建临时目录文件夹
    const tempDirResult = await ensureDirectoryExists(downloadTempPath)
    if (!tempDirResult.success) {
      if (tempDirResult.error) {
        logError('创建临时目录失败', { taskId, error: tempDirResult.error })
        if (tempDirResult.error.includes('not permitted') || tempDirResult.error.includes('permission denied')) {
          // 更新任务状态为可重试的错误状态
          await this.updateTask(taskId, {
            taskStatus: taskStatus.readyDownload,
            errorStatus: errorStatusEnum.extractError,
            errorMessage: errorMessageEnum.permissionDenied,
          })

          // 获取最新的任务信息并通知前端
          const updatedTask = await this.getTaskById(taskId)
          handlers.onDownloadProgress.send({
            ...updatedTask,
            taskId: updatedTask.id,
            taskStatus: taskStatus.readyDownload,
            totalSize: null,
            downloadedSize: null,
            speed: null,
            eta: null,
            errorAction: null,
            errorStatus: errorStatusEnum.extractError,
            errorMessage: errorMessageEnum.permissionDenied,
            isLive: updatedTask.isLive || false,
          })
        }
        else {
          logError('创建临时目录失败', { taskId, error: tempDirResult.error })
          await this.updateTask(taskId, {
            taskStatus: taskStatus.readyDownload,
            errorStatus: errorStatusEnum.extractError,
            errorMessage: errorMessageEnum.extractError,
          })

          // 获取最新的任务信息并通知前端
          const updatedTask = await this.getTaskById(taskId)
          handlers.onDownloadProgress.send({
            ...updatedTask,
            taskId: updatedTask.id,
            taskStatus: taskStatus.readyDownload,
            totalSize: null,
            downloadedSize: null,
            speed: null,
            eta: null,
            errorAction: null,
            errorStatus: errorStatusEnum.extractError,
            errorMessage: errorMessageEnum.extractError,
            isLive: updatedTask.isLive || false,
          })
        }
      }
      return
    }

    // windows环境下设置文件夹属性为隐藏
    if (process.platform === 'win32') {
      fs.chmod(downloadTempPath, 0o0400)
    }

    // 获取任务信息
    const task = await this.getTaskById(taskId)

    // 确保snapfile进程运行
    if (!snapfileService.isProcessRunning()) {
      try {
        await snapfileService.start()
      }
      catch (error) {
        logError('启动snapfile进程失败', {
          taskId,
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        })
        await this.updateTask(taskId, {
          taskStatus: taskStatus.failed,
          errorStatus: errorStatusEnum.downloadError,
          errorMessage: errorMessageEnum.downloadError,
        })

        // 发送错误状态给前端，设置为failed，不可重试
        handlers.onDownloadProgress.send({
          ...task,
          taskId: task.id,
          taskStatus: taskStatus.failed,
          totalSize: null,
          downloadedSize: null,
          speed: null,
          eta: null,
          errorStatus: errorStatusEnum.downloadError,
          errorMessage: errorMessageEnum.downloadError,
          errorAction: null,
          isLive: task.isLive || false,
        })
        return
      }
    }

    // 下载封面图用于界面显示（转换为base64格式存储到数据库）
    await this.downloadThumbnail(taskId, downloadTempPath)

    logInfo('任务下载开始', {
      taskId,
      startTime: new Date().toLocaleString('zh-CN', { hour12: false }),
      downloadItemsCount: items.length,
    })

    // 更新任务状态为下载中
    // await this.updateTask(taskId, {
    //   taskStatus: taskStatus.downloading,
    //   errorStatus: null,
    //   errorMessage: null,
    //   errorAction: null,
    // })

    // 获取代理配置
    const proxyConfig = ProxyService.getProxyConfig(setting.proxy)
    let proxyParam = 'system'

    // 根据代理类型设置代理参数
    proxyParam = proxyConfig.mode === 'direct'
      ? 'direct'
      : proxyConfig.mode === 'system'
        ? 'system'
        : proxyConfig.proxyRules || 'system'

    // 构建snapfile任务参数
    const snapfileTask = {
      taskID: taskId,
      name: task.text || 'download',
      outputDir: downloadPath, // 最终输出目录
      tempDir: downloadTempPath, // 临时目录
      outputType: setting.downloadType as 'video' | 'audio',
      outputVideoFormat: (setting.downloadType === 'video' ? setting.videoConfig.format.format : 'mp4') as 'mp4' | 'mkv',
      outputAudioFormat: (setting.downloadType === 'audio' ? setting.audioConfig.format.format : 'mp3') as 'mp3' | 'm4a' | 'ogg',
      live: false, // 根据需要设置
      embeddedSubtitle: setting.embedSubtitle || false,
      proxy: proxyParam, // 添加代理配置
      files: items.map(item => ({
        url: item.url,
        language: item.language,
        header: item.headers,
        optionalDownload: item.optionalDownload, // 传递optionalDownload属性到snapfile
      })),
    }

    // 启动snapfile任务
    const success = snapfileService.startTask(snapfileTask, {
      onProgress: async (progressData) => {
        // 处理进度更新 - 使用snapfile返回的实际进度数据
        const { done, total, speed, remainingTime, progressType } = progressData

        // 格式化ETA显示
        let etaDisplay = '00:00'
        if (remainingTime > 0) {
          if (remainingTime <= 86400) { // 最多显示24小时
            etaDisplay = formatTime(remainingTime)
          }
          else {
            etaDisplay = '24:00:00+' // 超过24小时显示为24小时+
          }
        }

        // 计算进度百分比（用于转换和合并阶段）
        const progressPercent = total > 0 ? Math.round((done / total) * 100) : 0

        // 获取当前任务状态
        const currentTask = await this.getTaskById(taskId)
        const currentTaskStatus = currentTask.taskStatus as TaskStatus

        // 根据进度类型处理不同的进度数据
        let progressInfo: any
        let progressMessage: any

        if (progressType === 'download') {
          // 下载进度：done和total是字节数
          progressInfo = {
            totalSize: total,
            downloadedSize: done,
            speed,
            eta: etaDisplay,
            percent: null,
          }

          progressMessage = {
            ...currentTask,
            taskId: currentTask.id,
            taskStatus: currentTaskStatus,
            totalSize: total,
            downloadedSize: done,
            speed,
            eta: etaDisplay,
            percent: null,
            errorAction: null,
            errorStatus: null,
            errorMessage: null,
            isLive: currentTask.isLive || false,
          }
        }
        else if (progressType === 'conversion') {
          // 转换进度：done和total是微秒数，需要转换为百分比
          progressInfo = {
            totalSize: null,
            downloadedSize: null,
            speed: null,
            eta: etaDisplay,
            percent: progressPercent,
          }

          progressMessage = {
            ...currentTask,
            taskId: currentTask.id,
            taskStatus: currentTaskStatus,
            totalSize: null,
            downloadedSize: null,
            speed: null,
            eta: etaDisplay,
            percent: progressPercent,
            errorAction: null,
            errorStatus: null,
            errorMessage: null,
            isLive: currentTask.isLive || false,
          }
        }

        // 更新进度数据并发送给前端
        tempTaskProgressMap.set(currentTask.id, progressInfo)
        handlers.onDownloadProgress.send(progressMessage)
      },
      onStatusChange: async (status, _data) => {
        // 特殊处理直播检测事件
        if (status === SnapfileStatusCode.task_live_detected) {
          // 更新数据库中的 isLive 字段
          await this.updateTask(taskId, {
            isLive: true,
          })

          // 获取更新后的任务信息
          const liveTask = await this.getTaskById(taskId)

          // 发送直播检测状态更新，保持下载中状态
          handlers.onDownloadProgress.send({
            ...liveTask,
            taskId: liveTask.id,
            taskStatus: 'downloading', // 保持下载中状态
            totalSize: tempTaskProgressMap.get(liveTask.id)?.totalSize || null,
            downloadedSize: tempTaskProgressMap.get(liveTask.id)?.downloadedSize || null,
            speed: tempTaskProgressMap.get(liveTask.id)?.speed || null,
            eta: tempTaskProgressMap.get(liveTask.id)?.eta || null,
            errorAction: null,
            errorStatus: null,
            errorMessage: null,
            isLive: true, // 添加直播标识
          })
          return
        }

        // 处理状态变更 - 将snapfile状态映射到项目状态
        const currentTaskStatus = mapSnapfileStatusToTaskStatus(status)

        // 更新任务状态
        await this.updateTask(taskId, {
          taskStatus: currentTaskStatus,
          errorStatus: null,
          errorMessage: null,
          errorAction: null,
        })

        // 获取最新的任务信息
        const latestTask = await this.getTaskById(taskId)

        // 判断是否需要清空进度数据
        const shouldClearProgress =
          // 从下载阶段进入等待转换阶段
          status === SnapfileStatusCode.task_pending_conversion ||
          // 从下载阶段进入转换阶段
          status === SnapfileStatusCode.task_start_conversion ||
          // 从转换阶段进入移动阶段
          status === SnapfileStatusCode.task_start_move

        // 发送状态更新
        handlers.onDownloadProgress.send({
          ...latestTask,
          taskId: latestTask.id,
          taskStatus: currentTaskStatus,
          totalSize: shouldClearProgress ? null : (tempTaskProgressMap.get(latestTask.id)?.totalSize || null),
          downloadedSize: shouldClearProgress ? null : (tempTaskProgressMap.get(latestTask.id)?.downloadedSize || null),
          speed: shouldClearProgress ? null : (tempTaskProgressMap.get(latestTask.id)?.speed || null),
          eta: shouldClearProgress ? null : (tempTaskProgressMap.get(latestTask.id)?.eta || null),
          errorAction: null,
          errorStatus: null,
          errorMessage: null,
          isLive: latestTask.isLive || false, // 添加直播标识
        })
      },
      onComplete: async (data) => {
        // snapfile已完成所有步骤（下载、转换、移动）
        logInfo('snapfile任务完成', {
          taskId: task.id,
          url: task.url,
          snapfileTaskId: data.taskID,
          filesCount: data.files?.length || 0,
        })

        trackEvent('下载成功', {
          url: task.url,
        })

        // 从snapfile返回的文件中获取主要媒体文件信息
        const mainFile = data.files[0] // 假设第一个文件是主要媒体文件
        let mediaInfo: MediaInfo = {
          mediaType: 'other',
        }

        // 尝试获取文件信息
        try {
          // 获取文件最终的信息
          const fileExt = path.extname(mainFile)
          const extension = fileExt.split('.').pop()

          // 使用 getFilePathMediaInfo 获取详细的媒体信息
          const detailedMediaInfo = await getFilePathMediaInfo(mainFile)

          // 从视频流中获取信息
          const videoStream = detailedMediaInfo.streams?.find(
            stream => stream.codec_type === 'video' && stream.codec_name !== 'mjpeg',
          )
          if (videoStream) {
            mediaInfo.mediaType = 'video'
            mediaInfo.resolutionWidth = videoStream.width
            mediaInfo.resolutionHeight = videoStream.height

            // 设置持续时间
            if (videoStream.duration) {
              mediaInfo.duration = Math.ceil(Number.parseFloat(videoStream.duration))
            }
          }

          // 如果视频流中没有持续时间，则使用格式中的持续时间
          if (
            !mediaInfo.duration
            && detailedMediaInfo.format
            && detailedMediaInfo.format.duration
          ) {
            mediaInfo.duration = Math.ceil(Number(detailedMediaInfo.format.duration))
          }

          // 获取音频比特率
          const audioStream = detailedMediaInfo.streams?.find(
            stream => stream.codec_type === 'audio',
          )
          if (audioStream && audioStream.bit_rate) {
            const bitrate = Math.ceil(Number.parseFloat(audioStream.bit_rate))
            mediaInfo.bitrate = bitrate
            mediaInfo.mediaType = videoStream ? 'video' : 'audio'
          }

          const subtitleStream = detailedMediaInfo.streams?.find(
            stream => stream.codec_type === 'subtitle',
          )
          if (subtitleStream && !audioStream && !videoStream) {
            mediaInfo.mediaType = 'subtitle'
          }

          // 获取文件大小
          const stats = await fs.stat(mainFile)

          // 合并所有信息
          mediaInfo = {
            ...mediaInfo,
            extension,
            filePath: mainFile,
            fileSize: stats.size,
          }

          console.log('解析的媒体信息:', mediaInfo)
        }
        catch (error) {
          logWarn('获取文件信息失败', {
            taskId: task.id,
            filePath: mainFile,
            error: error instanceof Error ? error.message : String(error),
          })
          // 如果获取失败，至少设置基本信息
          try {
            const stats = await fs.stat(mainFile)
            const fileExt = path.extname(mainFile)
            const extension = fileExt.split('.').pop()
            mediaInfo = {
              mediaType: 'other',
              extension,
              filePath: mainFile,
              fileSize: stats.size,
            }
          }
          catch (statError) {
            logWarn('获取文件基本信息也失败', {
              taskId: task.id,
              filePath: mainFile,
              error: statError instanceof Error ? statError.message : String(statError),
            })
            mediaInfo = {
              mediaType: 'other',
              filePath: mainFile,
              fileSize: 0,
            }
          }
        }

        // 更新任务状态为完成
        await this.updateTask(taskId, {
          taskStatus: taskStatus.completed,
          ...mediaInfo,
          tempTask: null,
          errorStatus: null,
          errorMessage: null,
          errorAction: null,
        })

        // 重新获取最新的任务信息（包含最新的isLive状态）
        const completedTask = await this.getTaskById(taskId)

        // 发送完成事件
        handlers.onDownloadProgress.send({
          ...completedTask,
          taskId: completedTask.id,
          taskStatus: taskStatus.completed,
          filePath: mediaInfo.filePath,
          duration: mediaInfo.duration,
          bitrate: mediaInfo.bitrate,
          extension: mediaInfo.extension,
          totalSize: mediaInfo.fileSize,
          resolutionWidth: mediaInfo.resolutionWidth,
          resolutionHeight: mediaInfo.resolutionHeight,
          eta: null,
          downloadedSize: null,
          speed: null,
          errorAction: null,
          errorStatus: null,
          errorMessage: null,
          isLive: completedTask.isLive || false,
        })

        // 清理临时数据
        tempTaskProgressMap.delete(completedTask.id)

        // 删除临时文件（如果需要）
        if (!isDev) {
          try {
            await fs.rm(downloadTempPath, { recursive: true, force: true })
          }
          catch (error) {
            logWarn('删除临时文件失败', {
              taskId: task.id,
              tempPath: downloadTempPath,
              error: error instanceof Error ? error.message : String(error),
            })
          }
        }
      },
      onError: async (error) => {
        logError('snapfile任务失败', {
          taskId: task.id,
          url: task.url,
          errorCode: error.code,
          errorMessage: error.message,
          retryable: error.retryable,
        })

        tempTaskProgressMap.delete(task.id)

        trackEvent('下载失败', {
          url: task.url,
        })

        // 使用映射后的错误状态，如果没有则使用默认值
        const errorStatus = mapSnapfileErrorToErrorStatus(error.code) || errorStatusEnum.downloadError

        // 根据错误码确定错误消息和任务状态
        let errorMessage: string | null = errorMessageEnum.downloadError
        let finalTaskStatus: typeof taskStatus.downloading | typeof taskStatus.converting | typeof taskStatus.failed = taskStatus.downloading // 默认设置为可重试状态

        // 特殊错误码的消息映射
        switch (error.code) {
          case SnapfileStatusCode.http_status_forbidden_error: // 403错误
            errorMessage = errorMessageEnum.videoNotAccess
            finalTaskStatus = taskStatus.downloading // 可重试
            break
          case SnapfileStatusCode.prepare_error: // 准备阶段错误
          case SnapfileStatusCode.parse_m3u8_error: // m3u8解析错误
          case SnapfileStatusCode.download_error: // 下载阶段错误
            errorMessage = errorMessageEnum.downloadError
            finalTaskStatus = taskStatus.downloading // 可重试
            break
          case SnapfileStatusCode.convert_error: // 转换阶段错误
            errorMessage = errorMessageEnum.convertError
            finalTaskStatus = taskStatus.failed // 不可重试
            break
          case SnapfileStatusCode.move_error: // 移动阶段错误
            errorMessage = errorMessageEnum.moveError
            finalTaskStatus = taskStatus.converting // 可重试
            break
          case SnapfileStatusCode.unknown_event: // 未知事件
          case SnapfileStatusCode.task_already_started: // 任务已开始
            errorMessage = errorMessageEnum.downloadError
            finalTaskStatus = taskStatus.downloading // 可重试
            break
          case SnapfileStatusCode.disk_full: // 磁盘已满
            errorMessage = errorMessageEnum.diskFull
            finalTaskStatus = taskStatus.downloading // 可重试
            break
          case SnapfileStatusCode.os_permission_denied: // 文件权限不足
            errorMessage = errorMessageEnum.permissionDenied
            finalTaskStatus = taskStatus.downloading // 可重试
            break
          case SnapfileStatusCode.unknown_error: // 未知错误
            errorMessage = errorMessageEnum.downloadError
            finalTaskStatus = taskStatus.failed // 不可重试
            break
          default:
            errorMessage = errorMessageEnum.downloadError
            finalTaskStatus = taskStatus.downloading // 可重试
            break
        }

        // 确定要更新的任务ID - 优先使用error中的taskID，否则使用当前任务ID
        const finalTaskId = error.taskID || taskId

        // 更新任务状态
        await this.updateTask(finalTaskId, {
          taskStatus: finalTaskStatus,
          errorStatus,
          errorMessage,
          errorAction: null,
        })

        // 获取最新的任务信息用于发送给前端
        const updatedTask = await this.getTaskById(finalTaskId)

        // 发送错误事件
        handlers.onDownloadProgress.send({
          ...updatedTask,
          taskId: updatedTask.id,
          taskStatus: finalTaskStatus,
          totalSize: null,
          downloadedSize: null,
          speed: null,
          eta: null,
          errorAction: null,
          errorStatus,
          errorMessage,
          isLive: updatedTask.isLive || false,
        })
      },
    })

    if (!success) {
      logError('启动snapfile任务失败', {
        taskId,
        itemsCount: items.length,
      })
      await this.updateTask(taskId, {
        taskStatus: taskStatus.failed,
        errorStatus: errorStatusEnum.downloadError,
        errorMessage: errorMessageEnum.downloadError,
      })

      // 发送错误状态给前端，设置为failed，不可重试
      const updatedTask = await this.getTaskById(taskId)
      handlers.onDownloadProgress.send({
        ...updatedTask,
        taskId: updatedTask.id,
        taskStatus: taskStatus.failed,
        totalSize: null,
        downloadedSize: null,
        speed: null,
        eta: null,
        errorStatus: errorStatusEnum.downloadError,
        errorMessage: errorMessageEnum.downloadError,
        errorAction: null,
        isLive: updatedTask.isLive || false,
      })
    }
  }

  private filterFormatsBySize(formats: YtDlpFormat[]): YtDlpFormat[] {
    const formatsWithSize = formats.filter(f => f.filesize != null)
    if (formatsWithSize.length > 0) {
      const maxSize = Math.max(...formatsWithSize.map(f => f.filesize))
      return formatsWithSize.filter(f => f.filesize === maxSize)
    }

    const formatsWithApproxSize = formats.filter(f => f.filesize_approx != null)
    if (formatsWithApproxSize.length > 0) {
      const maxSize = Math.max(...formatsWithApproxSize.map(f => f.filesize_approx))
      return formatsWithApproxSize.filter(f => f.filesize_approx === maxSize)
    }

    return formats
  }

  // 过滤音频格式
  private filterAudioFormats(formats: YtDlpFormat[], setting: SettingStoreType): YtDlpFormat {
    let audioFormats = formats.filter(format => format.acodec && format.acodec !== 'none')
    // 没有找到有效的音频格式，将使用最后一个可用格式
    if (audioFormats.length === 0) {
      return formats[formats.length - 1]
    }

    // 音频编码优先级映射
    const codecMap = new Map([
      ['mp3', ['mp3', 'aac', 'mp4a', 'm4a', 'opus']],
      ['ogg', ['opus', 'aac', 'mp4a', 'm4a', 'mp3']],
      ['m4a', ['aac', 'mp4a', 'm4a', 'mp3', 'opus']],
    ])

    const userFormat = setting.audioConfig.format.format || 'mp3'
    const codecPriorities = codecMap.get(userFormat) || codecMap.get('m4a')

    // 按编码优先级筛选
    for (const codec of codecPriorities) {
      const isAacFamily = ['aac', 'mp4a', 'm4a'].includes(codec)
      const filtered = audioFormats.filter((format) => {
        const acodec = format.acodec?.toLowerCase()
        return isAacFamily
          ? ['aac', 'mp4a', 'm4a'].some(c => acodec?.startsWith(c))
          : acodec?.startsWith(codec)
      })

      if (filtered.length > 0) {
        audioFormats = filtered
        break
      }
    }

    // 比特率范围映射
    // 320 289+
    // 256 225-288
    // 192 161-224
    // 128 97-160
    // 96 0-96
    const bitrateRanges = [
      { min: 289, label: '320+' },
      { min: 225, max: 288, label: '256-288' },
      { min: 161, max: 224, label: '192-224' },
      { min: 97, max: 160, label: '128-160' },
      { min: 0, max: 96, label: '0-96' },
    ]

    // 按比特率分组并选择最高组
    const groups = audioFormats.reduce((acc, format) => {
      const abr = format.abr
      const range = bitrateRanges.find(r =>
        abr >= r.min && (!r.max || abr <= r.max),
      )
      if (range) {
        acc[range.label] = acc[range.label] || []
        acc[range.label].push(format)
      }
      return acc
    }, {})

    const highestGroup = Object.keys(groups)[0]
    return (groups[highestGroup] || audioFormats)[0]
  }

  // 选择视频
  private selectVideoBySetting(data: YtDlpResponse, setting: SettingStoreType): DownloadItem {
    let videoFormats = data.formats.filter(format => ((format.vcodec && format.vcodec !== 'none') || format.video_ext))
    if (videoFormats.length === 0) {
      return null
    }
    // 按分辨率分组
    const resolutionGroups = videoFormats.reduce((groups, format) => {
      if (format.width || format.height) {
        const resolution = Math.min(
          format.width || Number.MAX_SAFE_INTEGER,
          format.height || Number.MAX_SAFE_INTEGER,
        )
        const standardRes = getVideoResolution(resolution)
        groups[standardRes] = groups[standardRes] || []
        groups[standardRes].push(format)
      }
      return groups
    }, {})

    // 选择合适的分辨率组
    const availableResolutions = Object.keys(resolutionGroups)
      .map(Number)
      .sort((a, b) => b - a)

    if (availableResolutions.length > 0) {
      const preferredResolution = setting.videoConfig.resolution ?? '1080'
      const numericResolution = Number(preferredResolution)

      if (!Number.isNaN(numericResolution) && preferredResolution !== 'highest') {
        const selectedResolution = availableResolutions.reduce((prev, curr) =>
          Math.abs(curr - numericResolution) < Math.abs(prev - numericResolution) ? curr : prev,
        )
        videoFormats = resolutionGroups[selectedResolution] || videoFormats
      }
      else {
        videoFormats = resolutionGroups[availableResolutions[0]] || videoFormats
      }
    }

    // 视频编码优先级映射
    const codecPriorityMap = new Map([
      ['h264', 1],
      ['avc1', 1],
      ['h265', 2],
      ['hevc', 2],
      ['hev1', 2],
      ['vp9', 3],
      ['vp09', 3],
      ['av01', 4],
      ['av1', 4],
    ])

    // 按编码优先级筛选
    const getCodecPriority = (format: YtDlpFormat) => {
      const vcodec = (format.vcodec || '').toLowerCase()
      let priority = 5 // 默认为 unknown codec priority
      codecPriorityMap.forEach((value, codec) => {
        if (vcodec.startsWith(codec)) {
          priority = value
        }
      })
      return priority
    }

    // 获取最高优先级的编码组
    const priorityGroups = videoFormats.reduce((acc, format) => {
      const priority = getCodecPriority(format)
      acc[priority] = acc[priority] || []
      acc[priority].push(format)
      return acc
    }, {})

    const minPriority = Math.min(...Object.keys(priorityGroups).map(Number))
    videoFormats = priorityGroups[minPriority] || videoFormats

    // 优先选择有音频的格式
    const formatsWithAudio = videoFormats.filter(format => format.acodec && format.acodec !== 'none')
    if (formatsWithAudio.length > 0) {
      videoFormats = formatsWithAudio
    }

    // 按 FPS 筛选
    const maxFps = Math.max(...videoFormats.map(f => f.fps || 0))
    videoFormats = videoFormats.filter(f => (f.fps || 0) === maxFps)

    // 按文件大小筛选
    videoFormats = this.filterFormatsBySize(videoFormats)

    const bestFormat = videoFormats[0]
    return {
      url: bestFormat.url,
      headers: bestFormat.http_headers,
      acode: bestFormat.acodec,
      language: bestFormat.language,
      type: 'video',
    }
  }

  // 选择音频
  private selectAudioBySetting(data: YtDlpResponse, setting: SettingStoreType): DownloadItem[] {
    // 首先尝试找只有音频的格式
    let audioFormats = data.formats.filter(format =>
      ((format.acodec && format.acodec !== 'none') || format.resolution === 'audio only')
      && (format.vcodec === 'none' || format.video_ext === 'none'),
    )
    // 如果没有找到，尝试任何包含音频的格式
    if (audioFormats.length === 0) {
      audioFormats = data.formats.filter(format => (format.acodec && format.acodec !== 'none') || format.video_ext)
      if (audioFormats.length === 0) {
        return []
      }
    }

    // 按语言分组
    const languageGroups = audioFormats.reduce((acc, format) => {
      const lang = format.language || 'default'
      acc[lang] = acc[lang] || []
      acc[lang].push(format)
      return acc
    }, {})

    // 确定目标语言
    const availableLanguages = Object.keys(languageGroups)
    let targetLanguages = []

    if (setting.audioTracks.length > 0) {
      if (setting.audioTracks.includes('all')) {
        targetLanguages = availableLanguages
      }
      else {
        targetLanguages = availableLanguages.filter(availableLang =>
          setting.audioTracks.some(selectedLang =>
            selectedLang !== 'default'
            && availableLang.toLowerCase().startsWith(selectedLang.toLowerCase()),
          ),
        )
      }
    }
    let isSelectDefault = false
    // 没有选择音频语言或者没选到，则选择默认的
    if (targetLanguages.length === 0 && audioFormats.length > 0) { // 没有选择音频语言，则选择默认的
      targetLanguages = availableLanguages
      isSelectDefault = true
    }

    // 为每个目标语言选择最佳格式
    const items = targetLanguages.map((language) => {
      const formats = languageGroups[language]
      const bestFormat = this.filterAudioFormats(formats, setting)
      return {
        url: bestFormat.url,
        headers: bestFormat.http_headers,
        acode: bestFormat.acodec,
        language: language === 'default' ? null : language,
        type: 'audio',
      }
    })
    if (isSelectDefault && items.length > 0) {
      return [items[items.length - 1] as DownloadItem]
    }
    return items as DownloadItem[]
  }

  // 选择字幕
  private selectSubtitleBySetting(data: YtDlpResponse, setting: SettingStoreType): DownloadItem[] {
    const downloadItems: DownloadItem[] = []

    console.log('选择的字幕', setting.subtitles)

    // 如果不是视频下载或者用户未选择字幕，则直接返回
    if (
      setting.downloadType !== 'video'
      || !setting.subtitles.length
      || setting.subtitles.includes('none')
    ) {
      return downloadItems
    }

    // 获取用户选择的字幕语言
    const selectedSubtitleLanguages = setting.subtitles

    // 先使用手动字幕，如果特定语言没有手动字幕，再使用自动生成的字幕
    const manualSubtitles = data?.subtitles || {}
    const autoSubtitles = data?.automatic_captions || {}

    // 创建一个新的对象来存储合并后的字幕
    const allSubtitles: Record<string, SubtitleFormat[]> = {}

    // 首先添加所有手动字幕
    Object.entries(manualSubtitles).forEach(([lang, formats]) => {
      allSubtitles[lang] = formats as SubtitleFormat[]
    })

    // 然后只添加手动字幕中不存在的自动字幕语言
    Object.entries(autoSubtitles).forEach(([lang, formats]) => {
      if (!allSubtitles[lang]) {
        allSubtitles[lang] = formats as SubtitleFormat[]
      }
    })

    // 如果不存在字幕
    if (Object.keys(allSubtitles).length === 0) {
      console.log('No subtitles available')
      return downloadItems
    }

    // 遍历所有可用的字幕语言
    for (const [subtitleLang, subtitleFormats] of Object.entries(allSubtitles)) {
      // 检查该字幕语言是否匹配用户选择的任一语言
      const matchedLanguage = selectedSubtitleLanguages.find((selectedLang) => {
        const selectedLangLower = selectedLang.toLowerCase()
        const subtitleLangLower = subtitleLang.toLowerCase()

        // 完全匹配的情况
        if (selectedLangLower === subtitleLangLower) {
          return true
        }

        // 处理带有区域代码的语言匹配（如 zh-Hant 和 zh）
        const [mainSubLang] = subtitleLangLower.split('-')
        const [mainSelectedLang] = selectedLangLower.split('-')

        return mainSubLang === mainSelectedLang
      })

      // 如果找到匹配的语言
      if (matchedLanguage) {
        // 优先选择的字幕格式优先级
        const preferredFormats = [
          'srt',
          'ass',
          'vtt',
          'json3',
          'ttml',
          'srv1',
          'srv2',
          'srv3',
        ]
        let selectedFormat: SubtitleFormat | null = null

        // 按优先级查找格式
        for (const format of preferredFormats) {
          selectedFormat = subtitleFormats.find(
            f => f.ext === format,
          )
          if (selectedFormat)
            break
        }

        // 如果没有找到首选格式，使用最后一个可用格式
        if (!selectedFormat && subtitleFormats.length > 0) {
          selectedFormat = subtitleFormats[subtitleFormats.length - 1]
        }

        // 如果找到了字幕格式，添加到下载列表
        if (selectedFormat) {
          const headers = getHttpHeaders(data)
          // 检查URL是否已存在
          if (!isUrlDuplicate(downloadItems, selectedFormat.url)) {
            downloadItems.push({
              url: selectedFormat.url,
              headers,
              language: subtitleLang,
              type: 'subtitle',
              ext: selectedFormat.ext,
              optionalDownload: true, // 字幕下载失败不影响整个任务
            })
            console.log(
              `Added subtitle: ${subtitleLang} (${selectedFormat.ext})`,
            )
          }
          else {
            console.log(
              `Skipped duplicate subtitle: ${subtitleLang} (${selectedFormat.ext})`,
            )
          }
        }
      }
    }

    return downloadItems
  }
}

export default new TaskService()
