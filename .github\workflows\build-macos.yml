name: Build macOS App

on:
  workflow_dispatch:
    inputs:
      arch_x64:
        description: 是否构建 x64 版本
        type: boolean
        required: true
        default: true
      arch_arm64:
        description: 是否构建 arm64 版本
        type: boolean
        required: true
        default: true

jobs:
  build:
    runs-on: macos-latest

    env:
      CSC_LINK: ${{ secrets.MAC_CERTIFICATE_BASE64 }}
      CSC_KEY_PASSWORD: ${{ secrets.MAC_CERTIFICATE_PASSWORD }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v3

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: 设置 Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'

      - name: 获取版本号
        id: package_version
        run: echo "VERSION=$(node -p "require('./package.json').version")" >> $GITHUB_OUTPUT

      - name: 安装依赖
        run: pnpm i

      - name: 下载二进制依赖
        run: |
          node scripts/install-ytdlp.mjs
          node scripts/install-ffmpeg.mjs
          node scripts/install-ffprobe.mjs

      - name: 获取当前日期
        id: date
        run: echo "DATE=$(date +'%Y-%m-%d')" >> $GITHUB_OUTPUT

      - name: 清理构建目录
        run: pnpm run clean

      - name: 解码证书
        run: |
          mkdir -p extra/certificates
          echo "$CSC_LINK" | base64 --decode > extra/certificates/证书.p12
          security create-keychain -p "${{ secrets.KEYCHAIN_PASSWORD }}" build.keychain
          security default-keychain -s build.keychain
          security unlock-keychain -p "${{ secrets.KEYCHAIN_PASSWORD }}" build.keychain
          security import extra/certificates/证书.p12 -k build.keychain -P "$CSC_KEY_PASSWORD" -T /usr/bin/codesign
          security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k "${{ secrets.KEYCHAIN_PASSWORD }}" build.keychain

      - name: 准备构建目录
        run: |
          mkdir -p dist
          mkdir -p release

      - name: 构建应用
        run: |
          if [[ "${{ inputs.arch_x64 }}" == "true" && "${{ inputs.arch_arm64 }}" == "true" ]]; then
            echo "构建 arm64 版本"
            pnpm run clean
            pnpm run build:mac-arm64
            echo "构建 x64 版本"
            pnpm run clean
            pnpm run build:mac-x64
          elif [[ "${{ inputs.arch_x64 }}" == "true" ]]; then
            echo "仅构建 x64 版本"
            pnpm run clean
            pnpm run build:mac-x64
          elif [[ "${{ inputs.arch_arm64 }}" == "true" ]]; then
            echo "仅构建 arm64 版本"
            pnpm run clean
            pnpm run build:mac-arm64
          else
            echo "错误: 至少需要选择一个构建架构"
            exit 1
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
          RELEASE_NAME: 'Release ${{ steps.date.outputs.DATE }}'
      - name: 上传构建产物
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: macos-artifacts
          path: |
            release/*.dmg
