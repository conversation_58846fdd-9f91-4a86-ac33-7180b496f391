import { isMac } from '@main/constants/env'
import { checkFileExists, getBinPath, setFilePermissions } from '@main/utils'
import ffmpeg from 'fluent-ffmpeg'

/**
 * 初始化 FFmpeg 和 FFprobe 路径
 * 在应用启动时调用此方法，之后可以直接使用 fluent-ffmpeg
 * @returns 包含 FFmpeg 和 FFprobe 路径的对象
 */
export async function initFFmpeg(): Promise<{
  ffmpegPath: string
  ffprobePath: string
}> {
  try {
    // 获取 ffmpeg 和 ffprobe 路径
    const ffmpegPath = getBinPath('ffmpeg')
    const ffprobePath = getBinPath('ffprobe')

    // 检查文件是否存在
    const [ffmpegExists, ffprobeExists] = await Promise.all([
      checkFileExists(ffmpegPath),
      checkFileExists(ffprobePath),
    ])

    if (!ffmpegExists || !ffprobeExists) {
      throw new Error('FFmpeg 或 FFprobe 文件不存在')
    }

    // 在 Mac 平台上设置执行权限
    if (isMac) {
      try {
        setFilePermissions(ffmpegPath)
        setFilePermissions(ffprobePath)
        console.log('已设置 FFmpeg 工具执行权限')
      }
      catch (error) {
        console.error('设置 FFmpeg 工具执行权限失败:', error)
      }
    }

    // 设置 ffmpeg 和 ffprobe 路径
    ffmpeg.setFfmpegPath(ffmpegPath)
    ffmpeg.setFfprobePath(ffprobePath)

    return { ffmpegPath, ffprobePath }
  }
  catch (error) {
    console.error('初始化 FFmpeg 工具失败:', error)
    throw error
  }
}
