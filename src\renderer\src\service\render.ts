// import type { AuthSite, Setting } from '@common/types/setting';

// const { electronAPI, electron } = window;

// export const checkForUpdate = () => electronAPI.checkForUpdates();

// export const getSettings = () => electronAPI.getSettings();

// export function saveSettings(settingData: Setting) {
//   return electronAPI.saveSettings(settingData);
// }

// export function openPathLocation(path: string) {
//   return electronAPI.openPathLocation(path);
// }

// export const getSystemLanguage = () => electronAPI.getSystemLanguage();

// export const selectDirectory = () => electronAPI.selectDirectory();

// export function getLocationFileInfo(fileName: string) {
//   return electronAPI.getLocationFileInfo(fileName);
// }

// export function changeLanguage(lang: string) {
//   return electronAPI.changeLanguage(lang);
// }

// export const getSavedSites = () => electronAPI.getSavedSites();

// export const saveSites = (sites: AuthSite[]) => electronAPI.saveSites(sites);

// export function openAuthWindow(authUrl: string, siteUrl: string) {
//   return electronAPI.openAuthWindow(authUrl, siteUrl);
// }

// export const removeAuth = (authUrl: string) => electronAPI.removeAuth(authUrl);

// export function fetchImage(url: string, headers?: HeadersInit) {
//   return electronAPI.fetchImage(url, headers);
// }

// // export const startParseDownloadConvert = electronAPI.startParseDownloadConvert

// export const saveDownloadTask = electronAPI.saveDownloadTask;

// export const openFileLocation = electronAPI.openFileLocation;

// export const cancelDownload = electronAPI.cancelDownload;

// export const clearJsonTasks = electronAPI.clearJsonTasks;

// export const getDownloadTask = electronAPI.getDownloadTask;

// export const resumeDownload = electronAPI.resumeDownload;

// export const getDownloadTasks = electronAPI.getDownloadTasks;

// export const downloadUpdate = electronAPI.downloadUpdate;

// export const quitAndInstall = electronAPI.quitAndInstall;

// export const checkLocalInstaller = electronAPI.checkLocalInstaller;

// export const onUpdateDownloadProgress = electronAPI.onUpdateDownloadProgress;

// export const removeUpdateProgressListener =
//   electronAPI.removeUpdateProgressListener;

// const { ipcRenderer } = electron;

// export const onRenderer = ipcRenderer.on;
// export const removeRenderer = ipcRenderer.removeListener;
