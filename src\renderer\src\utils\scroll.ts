export function scrollToTop(parent: HTMLElement | null, child: HTMLElement | null, options: ScrollIntoViewOptions = {}) {
  if (!parent || !child)
    return
  const parentRect = parent.getBoundingClientRect()
  const childRect = child.getBoundingClientRect()

  // 计算需要滚动的距离
  const scrollTop = childRect.top - parentRect.top + parent.scrollTop

  parent.scrollTo({
    top: scrollTop,
    behavior: 'smooth',
    ...options,
  })
}
