const flowbite = require('flowbite-react/tailwind')

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    // ...
    flowbite.content(),
  ],
  plugins: [
    // ...
    flowbite.plugin(),
  ],
  theme: {
    extend: {
      ringColor: {
        none: 'transparent',
      },
      keyframes: {
        highlight: {
          '0%': { boxShadow: '0 0 5px rgba(63, 131, 248, 0.5)' },
          '50%': { border: '1px solid #3F83F8', background: '#E1EFFE', boxShadow: '0 0 15px 0 #1c64f2' },
          '100%': { boxShadow: '0 0 5px rgba(63, 131, 248, 0.5)' },
        },
      },
      animation: {
        highlight: 'highlight 2s 2',
      },
    },
  },
}
