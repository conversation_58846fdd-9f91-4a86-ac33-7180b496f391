import type { RendererHandlers } from '@main/renderer-handlers'
import type { Router } from '@main/tipc'
import { createClient, createEventHandlers } from '@egoist/tipc/renderer'

export const client = createClient<Router>({
  ipcInvoke: window.electron.ipcRenderer.invoke,
})

export const handlers = createEventHandlers<RendererHandlers>({
  on: window.electron.ipcRenderer.on,
  send: window.electron.ipcRenderer.send,
})
