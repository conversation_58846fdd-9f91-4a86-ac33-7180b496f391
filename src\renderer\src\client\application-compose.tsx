import type {
  CSSProperties,
  ReactElement,
  ReactNode,
} from 'react'
import { useMergeStore } from '@/store/merge'
import { useTaskStore } from '@/store/task'
import { Tooltip } from 'flowbite-react'
import {
  cloneElement,
  useMemo,
} from 'react'
import { useLocation } from 'react-router-dom'

const ACTIVE_COLOR = '#3F83F8'
const NORMAL_COLOR = '#6B7280'
const ACTIVE_BG_COLOR = '#E1EFFE'
const DEFAULT_SIZE = 40

export function ApplicationSideItem(props: {
  icon: ReactElement
  iconSize?: number
  active?: boolean
  text: string
  to: string
  onClick?: () => void
}) {
  const location = useLocation()
  const { icon, iconSize, active, text, to, onClick } = props
  const size = iconSize || DEFAULT_SIZE
  const color = active ? ACTIVE_COLOR : NORMAL_COLOR
  const strokeWidth = active ? 2 : 1

  const { progressData } = useMergeStore()
  const { downloadingTaskCount } = useTaskStore()

  const modifiedIcon = useMemo(
    () =>
      cloneElement(icon, {
        className: 'text-[var(--icon-color)]',
        width: `${size}px`,
        height: `${size}px`,
        style: {
          fontSize: size,
          strokeWidth,
        },
      }),
    [icon, size, strokeWidth],
  )

  return (
    <section
      className="relative flex flex-col items-center justify-center cursor-pointer p-4 gap-1 hover:bg-gray-100"
      onClick={onClick}
      style={{ '--icon-color': color, 'backgroundColor': active && ACTIVE_BG_COLOR } as CSSProperties}
    >
      {modifiedIcon}

      <span className="text-center break-all text-[var(--icon-color)]">{text}</span>
      {location.pathname !== '/merge' && to === '/merge' && progressData?.status === 'merging' && <span className="w-5 h-5 absolute top-2 right-6 text-center align-middle text-sm font-bold text-white bg-blue-700 rounded-full">1</span>}
      {location.pathname !== '/download' && to === '/download' && downloadingTaskCount > 0 && <span className="w-5 h-5 absolute top-2 right-6 text-center align-middle text-sm font-bold text-white bg-blue-700 rounded-full">{downloadingTaskCount}</span>}
    </section>
  )
}

export function ApplicationRequireSpan(props: {
  required?: boolean
  children: ReactNode
}) {
  return (
    <span
      className={`text-sm after:content-["*"] after:text-[#E02424] after:ml-1 ${props.required ? '' : 'after:hidden'}`}
    >
      {props.children}
    </span>
  )
}

export function ApplicationIconWrap(props: {
  children: ReactNode
  content: ReactNode
  onClick: () => void
}) {
  return (
    <Tooltip content={props.content}>
      <div className="p-1 hover:bg-gray-100 hover:text-gray-900 rounded cursor-pointer text-xl text-gray-500" onClick={props.onClick}>
        {props.children}
      </div>
    </Tooltip>
  )
}
