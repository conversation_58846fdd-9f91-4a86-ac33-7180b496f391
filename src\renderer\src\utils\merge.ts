import type { FileMediaInfoItem, MediaStreamSelection } from '@main/types/ffmpeg'
import { t } from 'i18next'

export function handleMediaInfoData(medias: FileMediaInfoItem[]) {
  const newMedias: FileMediaInfoItem[] = []
  medias.forEach((item) => {
    const hasVideo = item.children.some(i => i.codec_type === 'video')
    if (hasVideo) {
      item.children.sort(i => i.codec_type === 'video' ? -1 : 1)
      newMedias.push(item)
    }
    else {
      item.children.forEach((i) => {
        const ele = { ...item, children: [i] }
        newMedias.push(ele)
      })
    }
  })
  return newMedias
}

/**
 * 合并两个数组，将第二个数组中所有相同key的元素插入到第一个数组中对应key的位置，并删除第一个数组中所有相同key的元素
 * @param arr1 第一个数组
 * @param arr2 第二个数组
 * @param key 用于比较的key
 * @returns 合并后的数组
 */
export function mergeArraysByKey<T>(arr1: T[], arr2: T[], key: keyof T): { replaceSetKey: Set<unknown>, data: T[] } {
  // 创建一个Map来存储第二个数组中的元素，按key分组
  const arr2Map = new Map()
  arr2.forEach((item) => {
    const keyValue = item[key]
    if (!arr2Map.has(keyValue)) {
      arr2Map.set(keyValue, [])
    }
    arr2Map.get(keyValue).push(item)
  })

  // 遍历第一个数组，替换或保留元素
  const result: T[] = []
  const processedKeys = new Set()

  arr1.forEach((item1) => {
    const keyValue = item1[key]
    if (arr2Map.has(keyValue) && !processedKeys.has(keyValue)) {
      // 如果第二个数组中有相同key的元素且这个key还没处理过，则替换
      result.push(...arr2Map.get(keyValue))
      processedKeys.add(keyValue)
    }
    else if (!arr2Map.has(keyValue)) {
      // 如果第二个数组中没有相同key的元素，则保留
      result.push(item1)
    }
  })

  // 添加第二个数组中的新元素
  const newItems = arr2.filter((item2) => {
    const keyValue = item2[key]
    return !arr1.some(item1 => item1[key] === keyValue)
  })
  console.log('processedKeys', processedKeys)

  return {
    replaceSetKey: processedKeys,
    data: [...result, ...newItems],
  }
}

/**
 * 查找有效的索引
 * @param currentIndex 当前索引
 * @param array 要查找的数组
 * @returns 找到的有效索引或undefined
 */
export function findValidIndex<T extends FileMediaInfoItem>(
  currentIndex: number | undefined,
  array: T[],
): number | undefined {
  // 如果没有当前索引，从头开始查找第一个符合要求的元素
  if (currentIndex === undefined) {
    return array.findIndex(item => item.children.some(i => i.codec_type === 'video'))
  }

  // 检查当前索引的元素是否符合要求
  if (array[currentIndex]?.children.some(i => i.codec_type === 'video')) {
    return currentIndex
  }

  // 从当前索引往下查找
  const nextIndex = array.slice(currentIndex + 1).findIndex(item => item.children.some(i => i.codec_type === 'video'))
  if (nextIndex !== -1) {
    return currentIndex + 1 + nextIndex
  }

  // 从头开始查找
  return array.findIndex(item => item.children.some(i => i.codec_type === 'video'))
}

/**
 * 格式化数据-返回给后端
 * @param medias 媒体信息数组
 * @param targetIndex 需要保留视频流的索引
 * @returns 处理后的媒体信息数组
 */
export function mergeMediaData(medias: FileMediaInfoItem[], targetIndex: number): MediaStreamSelection {
  const data: MediaStreamSelection = {
    name: '',
    input: [],
  }
  medias.forEach((item, index) => {
    if (index === targetIndex) {
      data.name = t('merge.mergePrefixName') + item.name
      data.input.push({ filePath: item.filePath, ids: item.children.map(i => i.id) })
    }
    else {
      const ids = item.children.filter(i => i.codec_type !== 'video').map(i => i.id)
      data.input.push({ filePath: item.filePath, ids })
    }
  })
  return data
}

/**
 * 合并两个数组，只追加第二个数组中不存在于第一个数组的元素
 * @param arr1 第一个数组
 * @param arr2 第二个数组
 * @param key 用于比较的key
 * @returns 合并后的数组
 */
export function mergeArraysUnique<T>(arr1: T[], arr2: T[], key: keyof T): T[] {
  // 创建一个Set来存储第一个数组中所有元素的key值
  const existingKeys = new Set(arr1.map(item => item[key]))

  // 从第二个数组中过滤出key值不在第一个数组中的元素
  const uniqueItems = arr2.filter(item => !existingKeys.has(item[key]))

  // 返回合并后的数组
  return [...arr1, ...uniqueItems]
}
