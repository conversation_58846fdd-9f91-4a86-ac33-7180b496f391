import type { DownloadAppModalRef } from '@/components/DownloadAppModal'
import type { YtDlpStatus } from '@main/types/yt-dlp'
import { client, handlers } from '@/client'
import DownloadAppModal from '@/components/DownloadAppModal'
import { useUpdateStore } from '@/store/update'
import { useSnapany } from '@renderer/hooks/snapany'
import { useLockFn } from 'ahooks'
import { Button } from 'flowbite-react'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

function SettingAboutPanel() {
  const { t } = useTranslation()
  const [componentData, setComponentData] = useState<{ version: string, status: YtDlpStatus }>()
  const { updateInfo } = useUpdateStore()
  const downloadAppRef = useRef<DownloadAppModalRef>(null)
  const { aboutInfo } = useSnapany()
  const { softwareVersion, website } = aboutInfo || {}
  const currentVersion = softwareVersion?.version
  const [clickCount, setClickCount] = useState(0)
  const clickTimeoutRef = useRef<NodeJS.Timeout>()

  const getComponentData = async () => {
    const data = await client.getLocalYtDlpVersion()
    setComponentData(data)
  }

  useEffect(() => {
    getComponentData()
    const unlisten = handlers.onYtDlpUpdateStatus.listen((version, status) => {
      setComponentData(prev => ({
        ...prev,
        version,
        status: status as YtDlpStatus,
      }))
    })
    return () => {
      unlisten()
      // 清理定时器
      if (clickTimeoutRef.current) {
        clearTimeout(clickTimeoutRef.current)
      }
    }
  }, [])

  const onUpdate = async () => {
    downloadAppRef.current?.setShowModal(true)
  }

  const handleRetry = useLockFn(async () => {
    await client.updateYtDlp()
    getComponentData()
  })

  const goWebsite = () => {
    client.openExternalLink({ url: website! })
  }

  const handleVersionClick = () => {
    setClickCount(prev => prev + 1)

    // 清除之前的超时
    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current)
    }

    // 检查是否达到7次点击
    if (clickCount + 1 >= 7) {
      // 打开日志目录
      client.openLogDirectory()
      setClickCount(0)
      return
    }

    // 设置2秒后重置计数
    clickTimeoutRef.current = setTimeout(() => {
      setClickCount(0)
    }, 2000)
  }

  return (
    <div className="space-y-[22px]">
      <div className="flex items-center gap-6">
        <p className="space-x-3 text-sm text-gray-900">
          <span className="text-gray-500">{t('settings.version')}</span>
          <span
            // className="cursor-pointer select-none"
            onClick={handleVersionClick}
          >
            v
            {currentVersion}
          </span>
        </p>
        {updateInfo?.hasUpdate && (
          <Button
            size="xs"
            color="blue"
            onClick={onUpdate}
          >
            {t('settings.upgrade')}
            (v
            {updateInfo.latestVersion}
            )
          </Button>
        )}
      </div>
      <div className="flex items-center gap-6">
        <p className="space-x-3 text-sm text-gray-900">
          <span className="text-gray-500">{t('settings.componentVersion')}</span>
          <span>{componentData?.version}</span>
        </p>
        {componentData?.status === 'updating' && (
          <Button
            size="xs"
            color="blue"
            className="cursor-default enabled:hover:bg-blue-700"
          >
            {t('settings.update')}
          </Button>
        )}
        {componentData?.status === 'failed' && (
          <Button size="xs" color="red" onClick={handleRetry}>
            {t('settings.updateErrorAndRetry')}
          </Button>
        )}
      </div>
      <p className="space-x-3 text-sm text-gray-900">
        <span className="text-gray-500">{t('menu.website')}</span>
        <span className="cursor-pointer hover:underline hover:text-blue-700" onClick={goWebsite}>{website}</span>
      </p>
      <DownloadAppModal ref={downloadAppRef} />
    </div>
  )
}

export default SettingAboutPanel
