import { resolve } from 'node:path'
import tailwindcss from '@tailwindcss/vite'
import react from '@vitejs/plugin-react'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import svgr from 'vite-plugin-svgr'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()],
    resolve: {
      alias: {
        '@main': resolve('src/main'),
        '@renderer': resolve('src/renderer/src'),
      },
    },
  },
  preload: {
    plugins: [externalizeDepsPlugin()],
  },
  renderer: {
    publicDir: resolve('src/renderer/assets'),
    resolve: {
      alias: {
        '@': resolve('src/renderer/src'),
        '@common': resolve('src/common'),
        '@renderer': resolve('src/renderer/src'),
        '@main': resolve('src/main'),
      },
    },
    plugins: [react(), tailwindcss(), svgr()],
  },
})
