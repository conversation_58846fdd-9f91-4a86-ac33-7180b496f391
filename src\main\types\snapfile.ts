import type { SnapfileEventType, SnapfileStatusCode } from '@main/constants/snapfile'

/**
 * Snapfile响应接口
 * 定义了snapfile进程返回的响应数据结构
 */
export interface SnapfileResponse {
  /** 状态码，参考SnapfileStatusCode常量 */
  code: string
  /** 响应消息 */
  message: string
  /** 响应数据，具体内容根据不同状态码而变化 */
  data?: SnapfileProgressData | SnapfileStatusData | SnapfileCompleteData | SnapfileErrorData
  /** 是否可重试，用于错误处理 */
  retryable?: boolean
}

/**
 * 启动任务载荷类型
 * 定义了start-task事件的参数结构
 */
export interface StartTaskPayload {
  /** 任务ID，由调用端提供的唯一标识 */
  taskID: string
  /** 下载任务名称，最终下载文件以该字段命名，不需要加后缀 */
  name: string
  /** 下载转换完后的最终保存目录 */
  outputDir: string
  /** 临时下载地址 */
  tempDir: string
  /** 任务下载完成保存时优先导出的文件类型 */
  outputType: 'video' | 'audio'
  /** 保存为视频时的格式 */
  outputVideoFormat: 'mp4' | 'mkv'
  /** 保存为音频时的格式 */
  outputAudioFormat: 'mp3' | 'm4a' | 'ogg'
  /** 是否为直播 */
  live?: boolean
  /** 导出为视频时，字幕文件是否内嵌（只在outputType=video时生效） */
  embeddedSubtitle?: boolean
  /** 代理地址。为direct不使用代理；为空或system使用系统代理；其他为代理地址，支持socks和http */
  proxy?: string
  /** 待下载的文件列表 */
  files: Array<{
    /** 下载直链 */
    url: string
    /** 语言，用于字幕 */
    language?: string
    /** 请求头信息 */
    header?: Record<string, string>
    /** 标记此文件下载失败是否影响整个任务，true表示失败不影响整个任务 */
    optionalDownload?: boolean
  }>
}

/**
 * 删除任务载荷类型
 * 定义了delete-task事件的参数结构
 */
export interface DeleteTaskPayload {
  /** 要删除的任务ID数组 */
  taskIDs: string[]
}

/**
 * 更新最大下载任务数载荷类型
 * 定义了update-max-download-task事件的参数结构
 */
export interface UpdateMaxDownloadTaskPayload {
  /** 限制的最大同时下载任务数量 */
  limit: number
}

/**
 * 停止录制直播载荷类型
 * 定义了stop-recording-live事件的参数结构
 */
export interface StopRecordingLivePayload {
  /** 要停止录制的直播任务ID */
  taskID: string
}

/**
 * Snapfile进度数据类型
 * 用于下载和转换进度回调
 */
export interface SnapfileProgressData {
  /** 任务ID */
  taskID: string
  /** 已完成的进度 - 下载时为字节数，转换时为微秒数 */
  done: number
  /** 总进度 - 下载时为字节数，转换时为微秒数 */
  total: number
  /** 速度 - 下载时为每秒字节数，转换时为每秒微秒数 */
  speed: number
  /** 剩余时间，单位为秒 */
  remainingTime: number
  /** 进度类型 - 用于区分下载、转换进度 */
  progressType?: 'download' | 'conversion'
}

/**
 * Snapfile状态变更数据类型
 * 用于任务状态变更回调
 */
export interface SnapfileStatusData {
  /** 任务ID */
  taskID: string
}

/**
 * Snapfile任务完成数据类型
 * 用于任务完成回调
 */
export interface SnapfileCompleteData {
  /** 任务ID */
  taskID: string
  /** 生成的文件列表 */
  files: string[]
}

/**
 * Snapfile错误数据类型
 * 用于错误回调
 */
export interface SnapfileErrorData {
  /** 错误码 */
  code: string
  /** 错误消息 */
  message: string
  /** 任务ID（如果有） */
  taskID?: string
  /** 是否可重试 */
  retryable?: boolean
  /** 映射到项目的错误状态 */
  errorStatus?: string
}

/**
 * Snapfile任务状态类型
 * 基于SnapfileStatusCode常量定义的任务状态联合类型
 */
export type SnapfileTaskStatus = typeof SnapfileStatusCode[keyof typeof SnapfileStatusCode]

/**
 * Snapfile事件类型定义
 * 定义了所有可能的snapfile事件类型及其对应的载荷类型
 * 使用联合类型确保类型安全，每种事件类型都有对应的载荷结构
 */
export type SnapfileEvent =
  | { type: typeof SnapfileEventType.START_TASK, payload: StartTaskPayload }
  | { type: typeof SnapfileEventType.DELETE_TASK, payload: DeleteTaskPayload }
  | { type: typeof SnapfileEventType.UPDATE_MAX_DOWNLOAD_TASK, payload: UpdateMaxDownloadTaskPayload }
  | { type: typeof SnapfileEventType.STOP_RECORDING_LIVE, payload: StopRecordingLivePayload }
