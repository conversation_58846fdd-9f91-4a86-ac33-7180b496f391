import { extractUrlsFromText } from '@main/utils'
import { expect, it } from 'vitest'

it('extract links from text', () => {
  const texts = [
    '37 【懒人养成瘦子习惯❗️小基数0痛苦瘦14斤分享 - 野生桃崽 | 小红书 - 你的生活指南】 😆 V1S4nuXTZAVsE1R 😆 https://www.xiaohongshu.com/discovery/item/6416c25c0000000012032d9c?source=webshare&xhsshare=pc_web&xsec_token=AB1tOXXYWBvEwwjDJWB3xav72OQTIzy5FNCeL1Ef3Reu4=&xsec_source=pc_share 念我的覅',
    '37 【懒人养成瘦子习惯❗️小基数0痛苦瘦14斤分享 - 野生桃崽 | 小红书 - 你的生活指南】 😆 V1S4nuXTZAVsE1R 😆 https://www.xiaohongshu.com/discovery/item/6416c25c0000000012032d9c?source=webshare&xhsshare=pc_web&xsec_token=AB1tOXXYWBvEwwjDJWB3xav72OQTIzy5FNCeL1Ef3Reu4=&xsec_source=pc_share/念我的覅',
    '37 【懒人养成瘦子习惯❗️小基数0痛苦瘦14斤分享 - 野生桃崽 | 小红书 - 你的生活指南】 😆 V1S4nuXTZAVsE1R 😆 https://www.xiaohongshu.com/discovery/item/6416c25c0000000012032d9c?source=webshare&xhsshare=pc_web&xsec_token=AB1tOXXYWBvEwwjDJWB3xav72OQTIzy5FNCeL1Ef3Reu4=&xsec_source=pc_share，念我的覅',
    '37 【懒人养成瘦子习惯❗️小基数0痛苦瘦14斤分享 - 野生桃崽 | 小红书 - 你的生活指南】 😆 V1S4nuXTZAVsE1R 😆 https://www.xiaohongshu.com/discovery/item/6416c25c0000000012032d9c?source=webshare&xhsshare=pc_web&xsec_token=AB1tOXXYWBvEwwjDJWB3xav72OQTIzy5FNCeL1Ef3Reu4=&xsec_source=pc_share。念我的覅',
    '37 【懒人养成瘦子习惯❗️小基数0痛苦瘦14斤分享 - 野生桃崽 | 小红书 - 你的生活指南】 😆 V1S4nuXTZAVsE1R 😆 https://www.xiaohongshu.com/discovery/item/6416c25c0000000012032d9c?source=webshare&xhsshare=pc_web&xsec_token=AB1tOXXYWBvEwwjDJWB3xav72OQTIzy5FNCeL1Ef3Reu4=&xsec_source=pc_share,念我的覅',
    '37 【懒人养成瘦子习惯❗️小基数0痛苦瘦14斤分享 - 野生桃崽 | 小红书 - 你的生活指南】 😆 V1S4nuXTZAVsE1R 😆 https://www.xiaohongshu.com/discovery/item/6416c25c0000000012032d9c?source=webshare&xhsshare=pc_web&xsec_token=AB1tOXXYWBvEwwjDJWB3xav72OQTIzy5FNCeL1Ef3Reu4=&xsec_source=pc_share.念我的覅',
  ]

  const list: string[] = []
  for (const text of texts) {
    const links = extractUrlsFromText(text)
    list.push(...links)
  }
  expect(list).toEqual([
    'https://www.xiaohongshu.com/discovery/item/6416c25c0000000012032d9c?source=webshare&xhsshare=pc_web&xsec_token=AB1tOXXYWBvEwwjDJWB3xav72OQTIzy5FNCeL1Ef3Reu4=&xsec_source=pc_share',
    'https://www.xiaohongshu.com/discovery/item/6416c25c0000000012032d9c?source=webshare&xhsshare=pc_web&xsec_token=AB1tOXXYWBvEwwjDJWB3xav72OQTIzy5FNCeL1Ef3Reu4=&xsec_source=pc_share',
    'https://www.xiaohongshu.com/discovery/item/6416c25c0000000012032d9c?source=webshare&xhsshare=pc_web&xsec_token=AB1tOXXYWBvEwwjDJWB3xav72OQTIzy5FNCeL1Ef3Reu4=&xsec_source=pc_share',
    'https://www.xiaohongshu.com/discovery/item/6416c25c0000000012032d9c?source=webshare&xhsshare=pc_web&xsec_token=AB1tOXXYWBvEwwjDJWB3xav72OQTIzy5FNCeL1Ef3Reu4=&xsec_source=pc_share',
    'https://www.xiaohongshu.com/discovery/item/6416c25c0000000012032d9c?source=webshare&xhsshare=pc_web&xsec_token=AB1tOXXYWBvEwwjDJWB3xav72OQTIzy5FNCeL1Ef3Reu4=&xsec_source=pc_share',
    'https://www.xiaohongshu.com/discovery/item/6416c25c0000000012032d9c?source=webshare&xhsshare=pc_web&xsec_token=AB1tOXXYWBvEwwjDJWB3xav72OQTIzy5FNCeL1Ef3Reu4=&xsec_source=pc_share',
  ])
})
