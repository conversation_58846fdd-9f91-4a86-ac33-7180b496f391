import Auth from '@/pages/Auth'
import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'

function AuthLayout() {
  const location = useLocation()

  // 组件挂载时记录日志
  useEffect(() => {
    console.log('AuthLayout 组件已加载，准备渲染相应组件')
    // 输出当前URL和查询参数信息
    console.log('当前URL信息:', {
      pathname: location.pathname,
      hash: location.hash,
      search: location.search,
    })
  }, [location])

  // 渲染Auth组件
  return (
    <div className="h-screen w-full overflow-hidden">
      <main className="h-full"><Auth /></main>
    </div>
  )
}

export default AuthLayout
