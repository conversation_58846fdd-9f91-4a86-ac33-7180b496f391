import type { MediaStreamSelection } from '@main/types/ffmpeg'
import SettingService from '@main/service/setting'
import VideoAudioMergeService from '@main/service/video-audio-merge'
import { t } from './instance'

export const videoAudioMergeRoute = {
  // 获取媒体文件信息
  getFilesMediaInfo: t.procedure
    .input<string[]>()
    .action(async ({ input }) => {
      const result = await VideoAudioMergeService.getFilesMediaInfo(input)
      return {
        success: result.length > 0,
        data: result,
      }
    }),

  // 合并视频和音频
  mergeMedia: t.procedure
    .input<MediaStreamSelection>()
    .action(async ({ input }) => {
      // 检查输入是否有效
      if (!input || input.input.length === 0) {
        return {
          success: false,
          error: '没有选择任何媒体流',
        }
      }

      try {
        // 调用合并函数
        const setting = SettingService.getSetting()
        const outputDir = setting.downloadPath
        const outputFormat = setting.videoConfig.format.format
        const result = await VideoAudioMergeService.mergeMediaStreams(input, outputDir, outputFormat)

        return result
      }
      catch (error) {
        console.error('合并媒体流失败:', error)
        return {
          success: false,
          error: `合并失败: ${error.message}`,
        }
      }
    }),

  // 停止当前合并任务
  stopMergeTask: t.procedure
    .action(async () => {
      return VideoAudioMergeService.stopCurrentMergeTask()
    }),
}
