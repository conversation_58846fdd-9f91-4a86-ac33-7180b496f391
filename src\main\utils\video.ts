/**
 * 查找一个数字最接近哪个标准分辨率
 * @param number 输入的数字（像素值）
 * @returns 最接近的标准分辨率值
 */
export function getVideoResolution(number: number): number {
  // 定义标准分辨率列表
  const standardResolutions = [
    '144', // 144p
    '240', // 240p
    '360', // 360p
    '480', // 480p
    '720', // 720p HD
    '1080', // 1080p HD
    '1440', // 1440p 2K
    '2160', // 2160p 4K
    '4320', // 4320p 8K
  ]

  // 如果数字小于最小分辨率，返回最小分辨率
  if (number <= Number.parseInt(standardResolutions[0])) {
    return Number.parseInt(standardResolutions[0])
  }

  // 如果数字大于最大分辨率，返回最大分辨率
  if (number >= Number.parseInt(standardResolutions[standardResolutions.length - 1])) {
    return Number.parseInt(standardResolutions[standardResolutions.length - 1])
  }

  // 找到最接近的分辨率
  let closest = Number.parseInt(standardResolutions[0])
  let minDiff = Math.abs(number - closest)

  for (const resolution of standardResolutions) {
    const diff = Math.abs(number - Number.parseInt(resolution))
    if (diff < minDiff) {
      minDiff = diff
      closest = Number.parseInt(resolution)
    }
  }

  return closest
}
