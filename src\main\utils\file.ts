import type { DownloadResult } from '@main/types'
import { exec } from 'node:child_process'
import crypto from 'node:crypto'
import fs from 'node:fs/promises'
import path from 'node:path'
import { promisify } from 'node:util'
import { isDev, isMac, isWin } from '@main/constants/env'

import { imageExt } from '@main/constants/utils'
import { app } from 'electron'
import fileType from 'file-type'
import mime from 'mime-types'

const execAsync = promisify(exec)
/**
 * 检查文件是否存在
 * @param filePath 文件路径
 * @returns 文件是否存在
 */
export async function checkFileExists(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath)
    return true
  }
  catch {
    return false
  }
}

/**
 * 检查文件夹是否存在
 * @param dirPath 文件夹路径
 * @returns 文件夹是否存在
 */
export async function checkDirectoryExists(dirPath: string): Promise<boolean> {
  try {
    const stats = await fs.stat(dirPath)
    return stats.isDirectory()
  }
  catch {
    return false
  }
}

/**
 * 为文件设置可执行权限（仅在 Mac 平台）
 * @param filePath 文件路径
 */
export async function setFilePermissions(filePath: string): Promise<void> {
  if (isMac) {
    const mode = 0o755
    await fs.chmod(filePath, mode)
  }
}

/**
 * 获取资源文件的完整路径
 * 此函数会自动处理平台差异，为Windows平台添加.exe扩展名
 * @param fileName 文件名（不带扩展名）
 * @param subdir 可选的子目录，默认为 'bin'
 * @returns 完整路径
 */
export function getBinPath(fileName: string, subdir: string = 'bin'): string {
  // 处理平台相关的文件名
  const executableName = isWin ? `${fileName}.exe` : fileName

  if (isDev) {
    // 开发环境下的路径
    return path.join(__dirname, '../../public', subdir, executableName)
  }

  // 生产环境路径
  const basePath = app.isPackaged
    ? path.join(process.resourcesPath, 'app.asar.unpacked')
    : path.join(__dirname, '..', '..')

  return path.join(basePath, 'public', subdir, executableName)
}

/**
 * 获取图片的base64编码
 * @param filePath 图片路径
 * @returns 图片的base64编码
 */
export async function getBase64Image(filePath: string): Promise<string> {
  // 确保文件存在
  const exists = await checkFileExists(filePath)
  if (!exists) {
    return ''
  }
  // buff读取，避免文件过大
  const buffer = await fs.readFile(filePath)
  return buffer.toString('base64')
}

export async function isImageFile(filePath: string): Promise<boolean> {
  const file = await fileType.fromFile(filePath)

  if (file && file.mime && file.mime.startsWith('image/')) {
    return true
  }
  else {
    // 根据后缀判断
    const ext = path.extname(filePath).toLowerCase()
    return imageExt.includes(ext)
  }
}

/**
 * 获取新的PNG文件路径
 * @description 将原始图片文件路径转换为对应的PNG格式文件路径
 * 保持原始文件名，仅改变扩展名为.png
 * @param imgFile 原始图片文件路径
 * @returns PNG格式的新文件路径
 * @example
 * getNewPngPath('/path/to/image.jpg') // 返回 '/path/to/image.png'
 */
export function getNewPngPath(imgFile: string): string {
  const imgDir = path.dirname(imgFile)
  const imgBaseName = path.basename(imgFile, path.extname(imgFile))
  return path.join(imgDir, `${imgBaseName}.png`)
}

/**
 * 确保目录存在
 * @description 检查指定目录是否存在，如果不存在则创建
 * 支持递归创建多层目录结构
 * @param dirPath 目录路径
 * @returns 返回包含成功状态和错误消息的对象
 */
export async function ensureDirectoryExists(dirPath: string): Promise<{ success: boolean, error?: string }> {
  try {
    if (!await checkFileExists(dirPath)) {
      await fs.mkdir(dirPath, { recursive: true })
    }
    return { success: true }
  }
  catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    console.error('创建目录失败:', error)
    return { success: false, error: `${errorMessage}` }
  }
}

/**
 * 更新文件路径映射
 * @description 在文件路径映射数组中更新指定文件的路径
 * 通常用于更新文件格式转换后的新路径
 * @param inputDirs 输入目录映射数组
 * @param oldPath 需要更新的原始文件路径
 * @param newPath 更新后的新文件路径
 */
export function updateFilePath(
  inputDirs: DownloadResult[],
  oldPath: string,
  newPath: string,
): void {
  const index = inputDirs.findIndex(item => item.filePath === oldPath)
  if (index !== -1) {
    inputDirs[index].filePath = newPath
    inputDirs[index].type = 'thumbnail'
  }
}

/**
 * 获取文件的目录路径
 * @description 从完整的文件路径中提取目录路径部分
 * @param filePath 完整的文件路径
 * @returns 文件所在的目录路径
 * @example
 * getDirectoryPath('/path/to/file.txt') // 返回 '/path/to'
 */
export function getDirectoryPath(filePath: string): string {
  return path.dirname(filePath)
}

/**
 * 文件名合法化处理
 * @param fileName 原始文件名
 * @returns 合法化后的文件名
 */
export function sanitizeFileName(fileName: string): string {
  let sanitizedFileName = fileName.replace(/[<>:"/\\|?*]/g, '')
  // 如果文件名包含后缀，去掉后缀
  const extname = path.extname(sanitizedFileName)
  if (extname) {
    sanitizedFileName = sanitizedFileName.slice(0, -extname.length)
  }
  sanitizedFileName = sanitizedFileName
    .replace(/[<>:"/\\|?*]/g, '') // 除Windows不允许的字符
    .replace(/[^a-z0-9\u0080-\uFFFF\s.-]/gi, '') // 将其他特殊字符替换为下划线
    .replace(/\s+/g, ' ') // 将多个空格替换为单个空格
    .trim() // 移除首尾空格
    .substring(0, 200) // 限制文件长度
  return sanitizedFileName
}

/**
 * Windows系统下检查文件是否被占用
 * @param filePath 文件路径
 * @returns 文件是否被占用
 */
export async function isFileLockedWin(filePath: string): Promise<boolean> {
  try {
    // 首先检查文件是否存在
    if (!await checkFileExists(filePath)) {
      console.log(`文件不存在: ${filePath}`)
      return false // 文件不存在，视为未被占用
    }

    // 尝试以读写方式打开文件，如果被占用会抛出异常
    const fd = await fs.open(filePath, 'r+')
    await fd.close()
    console.log(`Windows: 文件 ${filePath} 未被占用`)
    return false // 能成功打开，说明文件未被占用
  }
  catch (error) {
    // 确认错误是由于文件被占用引起的
    const errCode = (error as NodeJS.ErrnoException).code
    if (errCode === 'EBUSY' || errCode === 'EPERM' || errCode === 'EACCES') {
      console.log(`Windows: 文件 ${filePath} 被占用，错误代码: ${errCode}`)
      return true // 文件被占用
    }
    // 其他错误
    console.error(`Windows: 检查文件占用状态出错，错误类型: ${errCode}`, error)
    return false // 不是因为占用问题，返回未被占用
  }
}

/**
 * macOS系统下使用lsof命令检查文件是否被占用
 * @param filePath 文件路径
 * @returns 文件是否被占用
 */
export async function isFileLockedMac(filePath: string): Promise<boolean> {
  try {
    // 首先检查文件是否存在
    if (!await checkFileExists(filePath)) {
      console.log(`文件不存在: ${filePath}`)
      return false // 文件不存在，视为未被占用
    }

    // 对路径进行转义，避免路径中的特殊字符导致命令执行错误
    const escapedPath = filePath.replace(/'/g, '\'\\\'\'') // 转义单引号
    // 使用execAsync执行lsof命令
    const { stdout } = await execAsync(`lsof '${escapedPath}'`)

    const isLocked = stdout.trim().length > 0
    console.log(`macOS: 文件 ${filePath} ${isLocked ? '被占用' : '未被占用'}`)

    // 如果lsof命令有输出，说明文件被占用
    return isLocked
  }
  catch {
    // lsof命令执行出错（例如返回非零状态码）通常意味着没有进程使用该文件
    console.log(`macOS: lsof命令执行结果表明文件 ${filePath} 未被占用`)
    return false // 文件未被占用
  }
}

export function isM3u8Url(url: string): boolean {
  const decodedUrl = decodeURIComponent(url)
  const urlObj = new URL(decodedUrl)
  const pathname = urlObj.pathname.toLowerCase()
  return pathname.endsWith('.m3u8')
}

export function md5(str: string): string {
  return crypto.createHash('md5').update(str).digest('hex')
}

/**
 * 异步处理 m3u8 文件，将片段文件名重命名为 1.<ext>, 2.<ext> 等，并可选地重命名实际文件
 * @param inputFile 输入的 m3u8 文件路径
 */
export async function renameM3u8Segments(
  inputFile: string,
): Promise<void> {
  // 读取 m3u8 文件
  const content = await fs.readFile(inputFile, 'utf-8')
  const lines = content.split('\n')

  // 存储旧文件名到新文件名的映射
  let segmentIndex = 1

  // 处理每一行，替换片段文件名
  const newLines = lines.map((line) => {
    if (!line.startsWith('#') && line.trim() !== '') {
      const newLine = line.replace(/\?.*$/, '')
      const ext = path.extname(newLine) || '.ts' // 默认使用 .ts 如果没有后缀
      const newName = `${segmentIndex++}${ext}`
      return newName
    }
    else if (line.startsWith('#EXT-X-MAP:URI')) {
      // 正则提取 #EXT-X-MAP:URI="/amplify_video/1915686893804810240/vid/avc1/0/0/544x960/TPozgEgJdqBR55nY.mp4"
      const match = line.match(/#EXT-X-MAP:URI="([^"]+)"/)
      if (match) {
        const uri = match[1]
        const newUri = uri.replace(/\?.*$/, '')
        const ext = path.extname(newUri) || '.ts' // 默认使用 .ts 如果没有后缀
        const newName = `#EXT-X-MAP:URI="${segmentIndex++}${ext}"`
        return newName
      }
    }
    return line
  })

  // 写回新的 m3u8 文件
  await fs.writeFile(inputFile, newLines.join('\n'), 'utf-8')
  console.log(`新 m3u8 文件已生成：${inputFile}`)
}

/**
 * 从响应头中获取文件扩展名
 * @param url 请求的URL
 * @param headers 响应头
 * @returns 文件扩展名
 */
export function getExtensionFromHeaders(url: string, headers: Record<string, string | string[]>): string {
  // 尝试获取文件后缀
  let extension = ''
  const contentType = headers['content-type']
  if (contentType && !Array.isArray(contentType)) {
    extension = mime.extension(contentType) || ''
    if (extension === '') {
      extension = contentType.split('/').pop()
    }
  }

  if (extension === '') {
    const contentDisposition = headers['content-disposition']
    if (contentDisposition && !Array.isArray(contentDisposition)) {
      extension
        = contentDisposition.replace(/"/g, '').split('.').pop() || ''
    }
  }

  if (extension === '' || extension === 'bin') {
    // 尝试解析url后缀
    const urlObj = new URL(url)
    extension
      = path.extname(urlObj.pathname).split('.').pop() || ''
  }
  return extension
}

export async function forceRemoveWithExec(dirPath) {
  try {
    // 首先检查路径是否存在
    const stats = await fs.stat(dirPath)
    const isDirectory = stats.isDirectory()

    const command = isWin
      ? isDirectory
        ? `rmdir /s /q "${path.resolve(dirPath)}"` // 在 Windows 上使用 rmdir 删除目录
        : `del /f /q "${path.resolve(dirPath)}"` // 在 Windows 上使用 del 删除文件
      : `rm -rf "${path.resolve(dirPath)}"` // 在 Linux/macOS 上使用 rm (同时适用于文件和目录)

    console.log(`Executing command: ${command}`)

    const { stderr } = await execAsync(command)
    if (stderr) {
      // 命令成功时也可能在 stderr 输出信息，需要谨慎判断
      console.warn(`Stderr output (may not indicate failure): ${stderr}`)
    }
    console.log(`Successfully executed command to remove ${isDirectory ? 'directory' : 'file'}: ${dirPath}`)

    // 检查路径是否还存在
    try {
      await fs.access(dirPath)
      console.warn(`Path ${dirPath} might still exist after command execution.`)
    }
    catch (accessErr) {
      if (accessErr.code === 'ENOENT') {
        console.log(`Verified path ${dirPath} no longer exists.`)
      }
      else {
        console.warn(`Error checking path existence after removal attempt:`, accessErr)
      }
    }
  }
  catch (err) {
    console.error(`Failed to execute command for removal of ${dirPath}:`, err)
    // 这里的错误可能是命令执行失败，比如权限不足
    throw err
  }
}
