/**
 * 格式化剩余时间为 HH:MM:SS 或 MM:SS 格式
 * @param seconds 需要格式化的秒数
 * @returns 格式化后的时间字符串
 */
export function formatTime(seconds: number): string {
  // 处理无效输入
  if (Number.isNaN(seconds) || !Number.isFinite(seconds) || seconds < 0) {
    return '00:00'
  }

  // 如果速度太慢（导致时间过长）或无限大，显示为未知
  if (seconds > 86400 * 365) {
    // 超过1年的时间
    return '--:--'
  }

  // 向下取整总秒数
  seconds = Math.floor(seconds)

  // 计算小时、分钟和剩余秒数
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  // 如果有小时，返回 HH:MM:SS 格式
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 否则返回 MM:SS 格式
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}
