import type { PROXY_TYPE_ENUM } from '@common/constants/setting'

export interface AuthSite {
  name: string
  url: string
  authUrl: string
  isAuthorized: boolean
  enableDelete?: boolean
}

export interface ProxySetting {
  type: PROXY_TYPE_ENUM
  host?: string
  port?: string
  username?: string
  password?: string
}

export interface Setting {
  language: string
  defaultDownloadPath: string
  downloadType: 'video' | 'audio'
  downloadTypeVideo: {
    quality: string
    format: string
    subtitle: string[]
    audioChange: string[]
  }
  downloadTypeAudio: {
    quality: string
    format: string
  }
  downloadPlatform: string
  thumbnail: boolean
  subtitles: string
  subtitleType: 'embedded' | 'external'
  proxy: ProxySetting
}

export interface SettingData {
  defaultDownloadPath: string
  // 下载类型
  downloadType: 'video' | 'audio'
  downloadTypeVideo: {
    // 质量选项
    quality: string
    // 格式选项
    format: string
    // 字幕选项
    subtitle: string[]
    // 音轨选项
    audioChange: string[]
  }
  downloadTypeAudio: {
    // 质量选项
    quality: string
    // 格式选项
    format: string
  }
  // 封面
  thumbnail: boolean
  subtitles?: string
  subtitleType?: 'embedded' | 'external'
  language: string
  proxy: {
    type: 'SYSTEM' | 'HTTP' | 'SOCKS5' | 'NONE'
    host?: string
    port?: string
    username?: string
    password?: string
  }
}
