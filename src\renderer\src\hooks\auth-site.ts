import type { AuthSite } from '@common/types/setting'
import { client } from '@/client'
import { Notice } from '@/components/Notice'
import { useLockFn } from 'ahooks'
import { useTranslation } from 'react-i18next'
import useSWR from 'swr'

export function useAuthSite() {
  const { t } = useTranslation()
  const { data: authSites = [], mutate: mutateAuthSites } = useSWR(
    'getSavedSites',
    () => client.getAuthSites(),
  )

  const loginSite = useLockFn(async (authSite: AuthSite) => {
    await client.openAuthWindow({ url: authSite.authUrl })
  })

  const logoutSite = useLockFn(async (authSite: AuthSite) => {
    try {
      await client.logoutAuthSite({ url: authSite.url })
      await mutateAuthSites()
    }
    catch {
      Notice.error(t('messages.removeAuthFailed'))
    }
  })

  const addCustomSite = useLockFn(async (url: string) => {
    try {
      const { authUrl } = await client.addAuthSite({ authUrl: url })
      mutateAuthSites()
      if (authUrl) {
        return authUrl
      }
    }
    catch (e: any) {
      Notice.error(t(e.message ?? 'messages.defaultError'))
    }
  })

  const deleteSite = useLockFn(async (authSite: AuthSite) => {
    await client.deleteAuthSite({ name: authSite.name })
    await mutateAuthSites()
  })

  return {
    authSites,
    mutateAuthSites,
    loginSite,
    logoutSite,
    addCustomSite,
    deleteSite,
  }
}
