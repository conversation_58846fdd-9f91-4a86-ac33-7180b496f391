import type {
  DownloadProgressData,
  MediaResource,
  SoftwareUpdateProgressData,
  VideoAudioConverProgressData,
  VideoAudioMergeProgressData,
  WebviewStatusChangeData,
  YtDlpStatus,
} from '@main/types'

export type RendererHandlers = {
  // 下载进度回调
  onDownloadProgress: (data: DownloadProgressData) => void
  // 软件下载更新进度回调
  onSoftwareUpdateProgress: (data: SoftwareUpdateProgressData) => void
  // 软件关闭
  onAppClose: (existUnfinishedTask: boolean) => void
  // 音视频合并进度
  onVideoAudioMergeProgress: (data: VideoAudioMergeProgressData) => void
  // 音视频转换进度
  onVideoAudioConverProgress: (data: VideoAudioConverProgressData) => void
  // webview状态变化
  onWebviewStatusChange: (data: WebviewStatusChangeData) => void
  // yt-dlp更新状态
  onYtDlpUpdateStatus: (version: string, status: YtDlpStatus) => void
  // 资源嗅探回调
  onResourceSniffed: (data: MediaResource) => void
  // window.open拦截回调
  onWindowOpenIntercept: (url: string) => void
}
