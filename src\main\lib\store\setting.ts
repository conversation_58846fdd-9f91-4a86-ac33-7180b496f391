import type { SettingStoreType } from '@main/types/setting'
import { app } from 'electron'
import Store from 'electron-store'

export const settingStore = new Store<SettingStoreType>({
  name: 'setting',
  defaults: {
    downloadPath: app.getPath('downloads'),
    downloadType: 'video',
    videoConfig: {
      format: {
        format: 'mp4',
        platform: undefined,
      },
      resolution: 1080,
    },
    audioConfig: {
      format: {
        format: 'mp3',
        platform: undefined,
      },
      bitrate: 128,
    },
    subtitles: [],
    audioTracks: [],
    maxConcurrentDownloads: 8,
    createSubfolder: false,
    addIndexToFile: false,
    embedSubtitle: true,
    proxy: {
      type: 'system',
    },
    isDownloadThumbnail: false,
    language: 'system',
    authSites: [
      {
        name: 'YouTube',
        url: 'https://www.youtube.com',
        authUrl: 'https://www.youtube.com/signin',
        isAuthorized: false,
        enableDelete: false,
      },
      {
        name: 'Instagram',
        url: 'https://www.instagram.com',
        authUrl: 'https://www.instagram.com',
        isAuthorized: false,
        enableDelete: false,
      },
      {
        name: 'Twitter',
        url: 'https://x.com',
        authUrl: 'https://x.com',
        isAuthorized: false,
        enableDelete: false,
      },
    ],
  },
})
