import { addOrRemoveUniqueItemOfArray } from '@/utils/array'
import { Button, Checkbox, Label, Modal } from 'flowbite-react'

import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

interface MultipleLinksModalProps {
  open: boolean
  onCancel: () => void
  links: string[]
  onOk: (selectedLinks: string[]) => void
}

function MultipleLinksModal(props: MultipleLinksModalProps) {
  const { open, onCancel, links, onOk } = props
  const { t } = useTranslation()

  const [selectedUrls, setSelectedUrls] = useState<string[]>([])

  // 默认全选所有链接
  useEffect(() => {
    if (open && links.length > 0) {
      setSelectedUrls(links)
    }
  }, [open, links])

  const currentHasAll = useMemo(
    () => selectedUrls.length === links.length,
    [selectedUrls.length, links.length],
  )

  return (
    <Modal
      show={open}
      onClose={onCancel}
      // ! HACK 因为 flowbite-react bug
      className="bg-gray-900/50 dar:bg-gray-900/80"
    >
      <Modal.Header className="border-b-0 p-8 pb-0 [&>button]:cursor-pointer [&>button]:px-0 [&>button:hover]:bg-transparent">
        <b className="text-2xl">{t('download.modal.needDownloadToSelect')}</b>
      </Modal.Header>
      <Modal.Body className="p-8 pt-3">
        {links.map(link => (
          <section key={link} className="h-10 gap-3 flex items-center">
            <Checkbox
              // ! HACK 因为 flowbite-react bug
              className="checked:bg-[#1C64F2] checked:border-[#1C64F2] flex-shrink-0 focus:ring-none"
              color="blue"
              id={`multipleLink/${link}`}
              checked={selectedUrls.includes(link)}
              onChange={() => {
                const newSelectedUrls = addOrRemoveUniqueItemOfArray(
                  link,
                  selectedUrls,
                )
                setSelectedUrls(newSelectedUrls)
              }}
            />
            <Label
              htmlFor={`multipleLink/${link}`}
              className="truncate overflow-hidden"
              title={link}
            >
              {link}
            </Label>
          </section>
        ))}
        <div className="pt-12 flex items-center gap-3">
          <Checkbox
            color="blue"
            // ! HACK 因为 flowbite-react bug
            className="checked:bg-[#1C64F2] checked:border-[#1C64F2] focus:ring-none"
            id="selectAll"
            checked={currentHasAll}
            onChange={() => {
              if (currentHasAll) {
                setSelectedUrls([])
              }
              else {
                setSelectedUrls(links)
              }
            }}
          />
          <Label htmlFor="selectAll">
            {currentHasAll
              ? t('download.modal.cancelAll')
              : t('download.modal.selectAll')}
          </Label>
          <Button
            className="ml-auto"
            color="blue"
            disabled={selectedUrls.length === 0}
            onClick={() => onOk(selectedUrls)}
          >
            {t('download.modal.download')}
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  )
}

export default MultipleLinksModal
