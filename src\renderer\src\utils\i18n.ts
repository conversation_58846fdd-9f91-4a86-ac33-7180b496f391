// import { getSettings } from '@/service/render'
// 国际化
import bn from '@common/locales/bn.json'
import de from '@common/locales/de.json'
import en from '@common/locales/en.json'
import es from '@common/locales/es.json'
import fr from '@common/locales/fr.json'
import hi from '@common/locales/hi.json'
import id from '@common/locales/id.json'
import it from '@common/locales/it.json'
import ja from '@common/locales/ja.json'
import ko from '@common/locales/ko.json'
import pt from '@common/locales/pt.json'
import ru from '@common/locales/ru.json'
import tr from '@common/locales/tr.json'
import vi from '@common/locales/vi.json'
import zhHans from '@common/locales/zh-Hans.json'
import zhHant from '@common/locales/zh-Hant.json'
import i18n from 'i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

import { initReactI18next } from 'react-i18next'

// 应用支持的语言映射表
const APP_LANGUAGE_MAP: { [key: string]: string } = {
  'zh-Hans': 'zh-Hans',
  'zh-Hant': 'zh-Hant',
  'hi': 'hi',
  'es': 'es',
  'fr': 'fr',
  'ru': 'ru',
  'id': 'id',
  'bn': 'bn',
  'pt': 'pt',
  'de': 'de',
  'ja': 'ja',
  'ko': 'ko',
  'vi': 'vi',
  'tr': 'tr',
  'it': 'it',
}

// 获取实际语言设置
async function getActualLanguage(lang: string): Promise<string> {
  if (lang === 'system') {
    const systemLang = await window.electronAPI.getSystemLanguage()
    return APP_LANGUAGE_MAP[systemLang] || 'en'
  }
  return APP_LANGUAGE_MAP[lang] || lang
}

i18n
  .use(initReactI18next)
  .use(LanguageDetector)
  .init({
    resources: {
      'en': { translation: en },
      'zh-Hans': { translation: zhHans },
      'zh-Hant': { translation: zhHant },
      'hi': { translation: hi },
      'es': { translation: es },
      'fr': { translation: fr },
      'ru': { translation: ru },
      'id': { translation: id },
      'bn': { translation: bn },
      'pt': { translation: pt },
      'de': { translation: de },
      'ja': { translation: ja },
      'ko': { translation: ko },
      'vi': { translation: vi },
      'tr': { translation: tr },
      'it': { translation: it },
    },
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
  })

// 从主进程获取初始语言设置
// getSettings().then(async (settings) => {
//   if (settings.language) {
//     const actualLang = await getActualLanguage(settings.language);
//     i18n.changeLanguage(actualLang);
//   }
// // });

i18n.changeLanguage('zh-Hans')

export default i18n
