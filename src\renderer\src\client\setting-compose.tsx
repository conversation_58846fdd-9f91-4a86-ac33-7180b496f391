import type { ReactNode } from 'react'
import { Button, List } from 'flowbite-react'

export function SettingMenuItem(props: {
  text: string
  active?: boolean
  onClick?: () => void
}) {
  return (
    <div
      className={`px-2 py-4 text-lg font-bold text-center cursor-pointer ${props.active ? 'bg-white text-[#3F83F8]' : ''}`}
      onClick={props.onClick}
    >
      {props.text}
    </div>
  )
}

export function SettingSiteInfo(props: {
  icon: ReactNode
  siteName: string
  siteLink: string
}) {
  return (
    <div className="flex flex-col gap-1.5">
      <section className="flex gap-1 items-center">
        {props.icon}
        <span className="text-black">{props.siteName}</span>
      </section>
      <section className="text-[#4B5563] text-sm">{props.siteLink}</section>
    </div>
  )
}

export function SettingSiteItem(props: { children: ReactNode }) {
  return (
    <List.Item className="flex justify-between items-center px-4 py-3 border-[1px] border-[#D1D5DB] rounded-[10px]">
      {props.children}
    </List.Item>
  )
}

export function SettingSiteAction(props: {
  isAuthorized: boolean
  enableDelete?: boolean
  onLogin: () => void
  onLogout: () => void
  onDelete: () => void
  logoutText: string
  loginText: string
  deleteText: string
}) {
  return (
    <div className="flex gap-2.5">
      {props.isAuthorized
        ? (
            <Button size="sm" color="blue" outline onClick={props.onLogout}>
              {props.logoutText}
            </Button>
          )
        : (
            <Button color="blue" size="sm" onClick={props.onLogin}>
              {props.loginText}
            </Button>
          )}
      {props.enableDelete && (
        <Button size="sm" color="red" outline onClick={props.onDelete}>
          {props.deleteText}
        </Button>
      )}
    </div>
  )
}
