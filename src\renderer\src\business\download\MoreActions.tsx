import type { TaskType } from '@/store/task'
import { client } from '@/client'
import { Notice } from '@/components/Notice'
import { useTaskStore } from '@/store/task'
import { addOrRemoveUniqueItemOfArray } from '@/utils/array'
import { taskStatus } from '@main/constants/status'
import { Button, Checkbox, Dropdown, Label, Modal, Spinner, theme, Tooltip } from 'flowbite-react'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { HiDotsVertical, HiExclamationCircle, HiOutlineX } from 'react-icons/hi'
import { twMerge } from 'tailwind-merge'

export function ReomveAllModal() {
  const { t } = useTranslation()
  const [selected, setSelected] = useState([])
  const [hasOngoing, setHasOngoing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { tasks, isRemoveAllModal, setIsRemoveAllModal, fetchTask, clearDoingTasks } = useTaskStore()

  useEffect(() => {
    if (isRemoveAllModal) {
      const flag = tasks.some(task => !task.errorMessage && ([taskStatus.extracting, taskStatus.readyDownload, taskStatus.downloading, taskStatus.converting].includes(task.taskStatus as any)))
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setHasOngoing(flag)
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setSelected((pre) => {
        if (flag) {
          return pre.includes('goingDownload') ? pre : [...pre, 'goingDownload']
        }
        else {
          return pre.filter(item => item !== 'goingDownload')
        }
      })
    }
    return () => {
      if (!isRemoveAllModal) {
        setSelected([])
      }
    }
  }, [isRemoveAllModal])

  const handleRemove = async () => {
    setIsLoading(true)
    try {
      if (selected.includes('goingDownload')) {
        clearDoingTasks()
      }
      await client.deleteTaskList({ isDeleteFile: selected.includes('download'), isDeleteDownloading: selected.includes('goingDownload') })
      fetchTask()
    }
    catch (error) {
      Notice.error(error)
    }
    finally {
      setIsLoading(false)
      setIsRemoveAllModal(false)
    }
  }

  return (
    <Modal show={isRemoveAllModal} size="md">
      <Modal.Body className="relative p-5">
        <div className="absolute top-4 right-4" onClick={() => setIsRemoveAllModal(false)}>
          <HiOutlineX className="text-gray-400 cursor-pointer" />
        </div>
        <div className="flex flex-col items-center gap-3 mt-7 pb-5">
          <HiExclamationCircle className="text-2xl text-red-500" />
          <p>{t('dialogs.removeAll.removeAllItemsFromTheList')}</p>
          <div className="flex items-center gap-1">
            <Checkbox
              id="download"
              className="checked:bg-[#1C64F2] checked:border-[#1C64F2] flex-shrink-0 focus:ring-none"
              checked={selected.includes('download')}
              onChange={() => {
                const newSelected = addOrRemoveUniqueItemOfArray(
                  'download',
                  selected,
                )
                setSelected(newSelected)
              }}
            />
            <Label htmlFor="download" className="flex">
              {t('dialogs.removeAll.deleteDownloadedFiles')}
            </Label>
          </div>
          {hasOngoing && (
            <div className="flex items-center gap-1">
              <Checkbox
                id="goingDownload"
                className="checked:bg-[#1C64F2] checked:border-[#1C64F2] flex-shrink-0 focus:ring-none"
                checked={selected.includes('goingDownload')}
                onChange={() => {
                  const newSelected = addOrRemoveUniqueItemOfArray(
                    'goingDownload',
                    selected,
                  )
                  setSelected(newSelected)
                }}
              />
              <Label htmlFor="goingDownload" className="flex">
                {t('dialogs.removeAll.removeDownloading')}
              </Label>
            </div>
          )}
        </div>
        <div className="flex items-center justify-center gap-4">
          <Button color="light" onClick={() => setIsRemoveAllModal(false)}>{t('dialogs.removeAll.cancel')}</Button>
          <Button color="red" className="[&>span]:flex [&>span]:items-center [&>span]:gap-2" disabled={isLoading} onClick={handleRemove}>
            {isLoading && <Spinner color="failure" className="w-4 h-4" />}
            {t('dialogs.removeAll.remove')}
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  )
}

function MoreActions({ task, onDelete }: { task: TaskType, onDelete?: (task: TaskType) => void }) {
  const { t } = useTranslation()
  const { setIsRemoveAllModal } = useTaskStore()

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    }
    catch (error) {
      Notice.error(error)
    }
  }

  return (
    <div>
      <Dropdown
        arrowIcon={false}
        inline
        label={(
          <Tooltip content={t('taskActions.more')}>
            <HiDotsVertical className="text-[40px] cursor-pointer p-2 hover:bg-gray-100 hover:rounded-lg" />
          </Tooltip>
        )}
        theme={{ content: 'p-3', floating: { item: { base: twMerge(theme.dropdown.floating.item.base, 'p-1.5 rounded') } } }}
      >
        {task.text && (
          <>
            <Dropdown.Item key="copyCaption" tabIndex={0} onClick={() => handleCopy(task.text)}>
              {t('contextMenu.copyCaption')}
            </Dropdown.Item>
            <Dropdown.Divider />
          </>
        )}

        <Dropdown.Item key="copyLinkAddress" tabIndex={1} onClick={() => handleCopy(task.url)}>
          {t('contextMenu.copyLinkAddress')}
        </Dropdown.Item>
        <Dropdown.Item key="openInBrowser" tabIndex={2} onClick={() => client.openExternalLink({ url: task.url })}>
          {t('contextMenu.openInBrowser')}
        </Dropdown.Item>
        <Dropdown.Divider />
        <Dropdown.Item className="p-0">
          <p
            className="w-full h-full p-1.5 text-start"
            onClick={() => onDelete?.(task)}
          >
            {t('contextMenu.remove')}
          </p>
        </Dropdown.Item>
        <Dropdown.Item key="removeAll" tabIndex={4} onClick={() => setIsRemoveAllModal(true)}>
          {t('contextMenu.removeAll')}
        </Dropdown.Item>
      </Dropdown>
    </div>
  )
}

export default MoreActions
