import { useDownloadStore } from '@/store/download'
import {
  DownloadConfig,
  DownloadConfigShowCurrent,
} from '@renderer/client/download-compose'
import { DEFAULT_BITRATE, DEFAULT_QUALITIES } from '@renderer/constants/media'

import { useSnapany } from '@renderer/hooks/snapany'
import { Dropdown } from 'flowbite-react'
import { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

function DownloadConfigQuality() {
  const { t } = useTranslation()

  const { downloadType, changeBitrate, changeQuality, resolution, bitrate }
    = useDownloadStore()

  const { updateDownloadConfig } = useSnapany()

  const DEFAULT_QUALITIES_WITH_BEST = useMemo(
    () => [
      { label: t('download.videoQuality.best'), value: 'highest' } as const,
      ...DEFAULT_QUALITIES,
    ],
    [t],
  )

  const DEFAULT_BITRATE_WITH_HIGHTEST = useMemo(
    () => [
      { label: t('download.audioQuality.highest'), value: 'highest' } as const,
      ...DEFAULT_BITRATE,
    ],
    [t],
  )

  const isVideo = useMemo(() => downloadType === 'video', [downloadType])

  const currentSelectInfo = useMemo(() => {
    const current = isVideo ? resolution : bitrate
    const list = isVideo
      ? DEFAULT_QUALITIES_WITH_BEST
      : DEFAULT_BITRATE_WITH_HIGHTEST

    return list.find(x => x.value === current)?.label
  }, [
    isVideo,
    DEFAULT_QUALITIES_WITH_BEST,
    DEFAULT_BITRATE_WITH_HIGHTEST,
    resolution,
    bitrate,
  ])

  const bindAfter = useCallback(
    (fn: () => void) => async () => {
      await fn()
      const latestDownloadConfig = useDownloadStore.getState()
      await updateDownloadConfig(latestDownloadConfig)
    },
    [updateDownloadConfig],
  )

  const renderVideoList = () =>
    DEFAULT_QUALITIES_WITH_BEST.map(({ label, value }) => (
      <DownloadConfig
        key={value}
        showCheck={resolution === value}
        label={label}
        onClick={bindAfter(() => changeQuality(value))}
      />
    ))

  const renderAudioList = () =>
    DEFAULT_BITRATE_WITH_HIGHTEST.map(({ label, value }) => (
      <DownloadConfig
        key={value}
        showCheck={bitrate === value}
        label={label}
        onClick={bindAfter(() => changeBitrate(value))}
      />
    ))

  return (
    <Dropdown
      inline
      label={(
        <DownloadConfigShowCurrent
          content={currentSelectInfo}
          label={t('download.quality')}
        />
      )}
      theme={{
        arrowIcon: 'ml-1 text-gray-500',
      }}
    >
      {isVideo ? renderVideoList() : renderAudioList()}
    </Dropdown>
  )
}

export default DownloadConfigQuality
