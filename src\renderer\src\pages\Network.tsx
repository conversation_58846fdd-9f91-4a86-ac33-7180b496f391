import type { errorType } from './Auth'
import CollapseImg from '@/assets/collapse.png'
import Sniffing from '@/business/network/Sniffing'
import { useNetworkStore } from '@/store/network'
import { containsUrl, extractUrls } from '@/utils/url'
import NetworkHome from '@renderer/business/network/Home'
import { But<PERSON>, Spinner, TextInput } from 'flowbite-react'
import i18n from 'i18next'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TbArrowNarrowLeft, TbArrowNarrowRight, TbHome, TbReload, TbSearch } from 'react-icons/tb'
import { useLocation } from 'react-router-dom'

function NetworkPage() {
  const { t } = useTranslation()
  const location = useLocation()
  const [error, setError] = useState<errorType>()
  const webviewRef = useRef<Electron.WebviewTag>(null)
  const { search, isHome, url, loading, isDomReady, setSearch, setIsHome, setUrl, setTitle, setLoading, setIsDomReady, setIsSniffingBtn, getSiteList, initResourceSniffer, listenResourceSniffer, clearListenResourceSniffer, clearSniffedResource, listenWindowOpen, clearListenWindowOpen } = useNetworkStore()

  const handleBack = () => {
    if (isDomReady && webviewRef.current?.canGoBack()) {
      setError(undefined)
      webviewRef.current?.goBack()
      const url = webviewRef.current?.getURL()
      setIsHome(url === 'about:blank')
    }
  }

  const handleForward = () => {
    if (isDomReady && webviewRef.current?.canGoForward()) {
      setError(undefined)
      webviewRef.current?.goForward()
      const url = webviewRef.current?.getURL()
      setIsHome(url === 'about:blank')
    }
  }

  const handleReload = () => {
    if (url && isDomReady) {
      clearSniffedResource()
      setError(undefined)
      setIsSniffingBtn(true)
      webviewRef.current?.reload()
    }
  }

  const handleHome = () => {
    if (url) {
      clearSniffedResource()
      setError(undefined)
      setIsHome(true)
      setSearch('')
      setIsSniffingBtn(true)
      // 清除 webview 内容
      if (webviewRef.current) {
        webviewRef.current.loadURL('about:blank')
      }
    }
  }

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value.trim())
  }

  const isUrl = useMemo(() => {
    return containsUrl(search)
  }, [search])

  const handleGo = () => {
    const site = extractUrls(search)[0]
    clearSniffedResource()
    if (site && site !== url) {
      setUrl(site)
    }
    else {
      webviewRef.current?.reload()
    }
  }

  const handleToSearch = () => {
    // const searchUrl = `https://www.youtube.com/results?search_query=${encodeURIComponent(search)}`
    const tiktokSearchUrl = `https://www.tiktok.com/search?q=${encodeURIComponent(search)}`
    const douyinSearchUrl = `https://www.douyin.com/search/${encodeURIComponent(search)}`
    const searchUrl = i18n.language.startsWith('zh-Hans') ? douyinSearchUrl : tiktokSearchUrl
    setUrl(searchUrl)
  }

  const handleSearchSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!search) {
      handleHome()
      return
    }
    setIsSniffingBtn(true)
    if (isUrl) {
      handleGo()
    }
    else {
      handleToSearch()
    }
  }

  const handleSiteItemClick = (site: string) => {
    if (isDomReady && url === site) {
      webviewRef.current?.loadURL(site)
    }
    setSearch(site)
    setUrl(site)
  }

  useEffect(() => {
    getSiteList()
    listenResourceSniffer()
    listenWindowOpen()
    return () => {
      clearListenResourceSniffer()
      clearListenWindowOpen()
    }
  }, [])

  useEffect(() => {
    if (isDomReady) {
      const webview = webviewRef.current
      webview.setAudioMuted(!(location.pathname === '/network'))
    }
  }, [location.pathname])

  const handleDidStartLoading = () => {
    setError(undefined)
    setLoading(true)
  }

  const handleDidStopLoading = () => {
    setLoading(false)
  }

  const handleDidFailLoad = (event: Event) => {
    const webviewEvent = event as unknown as Electron.DidFailLoadEvent
    const { errorDescription } = webviewEvent || {}

    // 只处理主框架的错误
    if (!webviewEvent.isMainFrame) {
      console.log('忽略非主框架的加载失败')
      return
    }

    let errData
    switch (errorDescription) {
      case 'ERR_FAILED':
        errData = {
          code: errorDescription,
          title: t('auth.cannotAccess'),
          description: t('auth.errorDescription4', { domain: webviewRef.current?.getURL() }),
          isSolution: false,
        }
        break
      case 'ERR_NETWORK_CHANGED':
        errData = {
          code: errorDescription,
          title: t('auth.connectInterrupt'),
          description: t('auth.errorDescription6'),
          isSolution: false,
        }
        break
      case 'ERR_CONNECTION_CLOSED':
        errData = {
          code: errorDescription,
          title: t('auth.cannotAccess'),
          description: t('auth.errorDescription1', { domain: webviewRef.current?.getURL() }),
          isSolution: true,
        }
        break
      case 'ERR_CONNECTION_REFUSED':
        errData = {
          code: errorDescription,
          title: t('auth.cannotAccess'),
          description: t('auth.errorDescription3', { domain: webviewRef.current?.getURL() }),
          isSolution: true,
        }
        break
      case 'ERR_NAME_NOT_RESOLVED':
        errData = {
          code: errorDescription,
          title: t('auth.cannotAccess'),
          description: t('auth.errorDescription5', { domain: webviewRef.current?.getURL() }),
          isSolution: false,
        }
        break
      case 'ERR_INTERNET_DISCONNECTED':
        errData = {
          code: errorDescription,
          title: t('auth.noInternet'),
          isSolution: true,
        }
        break
      case 'ERR_CONNECTION_TIMED_OUT':
        errData = {
          code: errorDescription,
          title: t('auth.cannotAccess'),
          description: t('auth.errorDescription2', { domain: webviewRef.current?.getURL() }),
          isSolution: true,
        }
        break
      case 'ERR_PROXY_CONNECTION_FAILED':
        errData = {
          code: errorDescription,
          title: t('auth.noInternet'),
          description: t('auth.errorDescription7'),
          isSolution: true,
        }
        break
      case 'ERR_EMPTY_RESPONSE':
        errData = {
          code: errorDescription,
          title: t('auth.noWork'),
          description: t('auth.errorDescription8', { domain: webviewRef.current?.getURL() }),
          isSolution: false,
        }
        break
      case 'ERR_CONNECTION_RESET':
        errData = {
          code: errorDescription,
          title: t('auth.cannotAccess'),
          description: t('auth.errorDescription9'),
          isSolution: true,
        }
        break
      default:
        errData = { code: errorDescription, title: t('auth.cannotAccess'), description: t('auth.errorDescription4', { domain: webviewRef.current?.getURL() }), isSolution: false }
    }
    setError(errData)
  }

  const handleCrashed = () => {
    console.log('页面崩溃')
    setError({
      code: 'ERR_EMPTY_RESPONSE',
      title: t('auth.noWork'),
      description: t('auth.errorDescription8', { domain: webviewRef.current?.getURL() }),
      isSolution: false,
    })
  }

  const handleUnresponsive = () => {
    console.log('页面无响应')
    setError({
      code: 'ERR_EMPTY_RESPONSE',
      title: t('auth.noWork'),
      description: t('auth.errorDescription8', { domain: webviewRef.current?.getURL() }),
      isSolution: false,
    })
  }

  const handlePluginCrashed = () => {
    console.log('插件崩溃')
    setError({
      code: 'ERR_EMPTY_RESPONSE',
      title: t('auth.noWork'),
      description: t('auth.errorDescription8', { domain: webviewRef.current?.getURL() }),
      isSolution: false,
    })
  }

  const handleDomReady = async () => {
    setIsDomReady(true)
    setLoading(false)
    const webview = webviewRef.current
    // 监听网络请求
    const webContentsId = webview.getWebContentsId()
    console.log('webContentsId', webContentsId)
    await initResourceSniffer(webContentsId)
  }

  const handlePageTitleUpdated = (event: any) => {
    console.log('handlePageTitleUpdated', event)
    setTitle(event.title)
  }

  const handleDidStartNavigation = (event: any) => {
    console.log('did-start-navigation', event)
    if (event.isMainFrame) {
      setSearch(event.url === 'about:blank' ? '' : event.url)
      setIsHome(event.url === 'about:blank')
    }
  }

  const handleDidNavigateInPage = (event: any) => {
    setSearch(event.url)
    console.log('did-navigate-in-page', event)
  }

  useEffect(() => {
    const webview = webviewRef.current
    if (url && webview) {
      setIsHome(false)
      webview.addEventListener('did-start-loading', handleDidStartLoading)
      webview.addEventListener('did-stop-loading', handleDidStopLoading)
      webview.addEventListener('dom-ready', handleDomReady)
      webview.addEventListener('page-title-updated', handlePageTitleUpdated)
      webview.addEventListener('did-fail-load', handleDidFailLoad)
      webview.addEventListener('crashed', handleCrashed)
      webview.addEventListener('unresponsive', handleUnresponsive)
      webview.addEventListener('plugin-crashed', handlePluginCrashed)
      webview.addEventListener('did-start-navigation', handleDidStartNavigation)
      webview.addEventListener('did-navigate-in-page', handleDidNavigateInPage)
    }

    return () => {
      webview?.removeEventListener('did-start-loading', handleDidStartLoading)
      webview?.removeEventListener('did-stop-loading', handleDidStopLoading)
      webview?.removeEventListener('dom-ready', handleDomReady)
      webview?.removeEventListener('page-title-updated', handlePageTitleUpdated)
      webview?.removeEventListener('did-fail-load', handleDidFailLoad)
      webview?.removeEventListener('crashed', handleCrashed)
      webview?.removeEventListener('unresponsive', handleUnresponsive)
      webview?.removeEventListener('plugin-crashed', handlePluginCrashed)
      webview?.removeEventListener('did-start-navigation', handleDidStartNavigation)
      webview?.removeEventListener('did-navigate-in-page', handleDidNavigateInPage)
    }
  }, [url])

  return (
    <div className="flex flex-col h-full">
      <div className="py-2 px-4 bg-white">
        <div className="flex items-center gap-2.5">
          <div className="flex items-center gap-2.5">
            <span className={`p-1.75 text-lg ${(isDomReady && webviewRef.current?.canGoBack()) ? 'text-gray-900 cursor-pointer' : 'text-gray-300'}`} onClick={handleBack}><TbArrowNarrowLeft /></span>
            <span className={`p-1.75 text-lg ${(isDomReady && webviewRef.current?.canGoForward()) ? 'text-gray-900 cursor-pointer' : 'text-gray-300'}`} onClick={handleForward}><TbArrowNarrowRight /></span>
            <span className={`p-1.75 text-lg ${(url && isDomReady) ? 'text-gray-900 cursor-pointer' : 'text-gray-300'}`} onClick={handleReload}><TbReload /></span>
            <span className={`p-1.75 text-lg ${url ? 'text-gray-900 cursor-pointer' : 'text-gray-300'}`} onClick={handleHome}><TbHome /></span>
          </div>
          <form className="w-full h-9 flex items-center gap-2.5" onSubmit={handleSearchSubmit}>
            <div className="w-full relative">
              <TextInput value={search} onChange={handleSearch} placeholder={t('network.searchPlaceholder')} theme={{ field: { input: { sizes: { md: 'pl-10.5 pr-4 py-2 text-xs' } } } }} />
              <span className="absolute left-4 top-1/2 -translate-y-1/2 text-base text-gray-500"><TbSearch /></span>
            </div>
            {isUrl ? <Button outline color="blue" className="shrink-0" theme={{ size: { md: 'px-4 py-1.75 text-sm' } }} type="submit">{t('network.go')}</Button> : <Button className="shrink-0" type="submit">{t('network.search')}</Button>}
          </form>
        </div>
      </div>
      <div className="grow relative overflow-hidden">
        <NetworkHome onSiteItemClick={handleSiteItemClick} />
        <div className={`absolute top-0 left-0 ${!isHome ? 'flex' : 'hidden'} w-full h-full bg-white`}>
          {loading && (
            <div className="absolute w-full h-full bg-white flex items-center justify-center">
              <Spinner className="w-25 h-25 fill-blue-600" />
            </div>
          )}
          {error && (
            <div className="absolute w-full h-full bg-white flex items-center justify-center flex-col gap-4">
              <div className="flex flex-col max-w-[60%]">
                <img src={CollapseImg} alt="collapse" className="w-18 h-18 mb-10" />
                <h1 className="text-2xl mb-4">{error?.title}</h1>
                {error?.description && (
                  <p className="mb-4 text-gray-700 text-[15px]">
                    {error?.description}
                  </p>
                )}
                {error?.isSolution && (
                  <div className="mb-3 text-[15px] text-gray-700">
                    <p>{t('auth.try')}</p>
                    <ul className="list-disc pl-10">
                      <li>{t('auth.tip1')}</li>
                      <li>{t('auth.tip2')}</li>
                    </ul>
                  </div>
                )}
                <p className="text-xs text-gray-700">{error?.code}</p>
              </div>
            </div>
          )}
          {/* @ts-expect-error allowpopups要求值是字符串 */}
          <webview ref={webviewRef} id="network-webview" className="w-full h-full" src={url} allowpopups="true" />
          <Sniffing />
        </div>
      </div>
    </div>
  )
}

export default NetworkPage
