import type { SnifferStoreType, UrlBookmarkStoreType } from '@main/types/sniffer'
import { defaultExtFilters, defaultRegexFilters, defaultTypeFilters } from '@main/constants/sniffer'
import Store from 'electron-store'

// 创建存储配置
export const snifferStore = new Store<SnifferStoreType>({
  name: 'sniffer',
  defaults: {
    resourceSnifferFilters: {
      extFilters: defaultExtFilters,
      typeFilters: defaultTypeFilters,
      regexFilters: defaultRegexFilters,
    },
  },
})

// url书签
export const urlBookmarkStore = new Store<UrlBookmarkStoreType>({
  name: 'urlBookmark',
  defaults: {
    bookmarks: [
      {
        url: 'https://www.youtube.com',
        title: 'YouTube',
        mainDomain: 'YouTube',
      },
      {
        url: 'https://www.x.com',
        title: 'Twitter',
        mainDomain: 'Twitter',
      },
      {
        url: 'https://www.facebook.com',
        title: 'Facebook',
        mainDomain: 'Facebook',
      },
      {
        url: 'https://www.tiktok.com',
        title: 'TikTok',
        mainDomain: 'TikTok',
      },
      {
        url: 'https://www.instagram.com',
        title: 'Instagram',
        mainDomain: 'Instagram',
      },
      {
        url: 'https://www.soundcloud.com',
        title: 'SoundCloud',
        mainDomain: 'SoundCloud',
      },
      {
        url: 'https://www.twitch.tv',
        title: 'Twitch',
        mainDomain: 'Twitch',
      },
      {
        url: 'https://www.spotify.com',
        title: 'Spotify',
        mainDomain: 'Spotify',
      },

    ],
  },
})
