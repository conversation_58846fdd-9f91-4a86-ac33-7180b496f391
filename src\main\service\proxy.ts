import type { SettingStoreType } from '@main/types'
import { settingStore } from '@main/lib/store'
import { session } from 'electron'

class ProxyService {
  constructor() { }

  getProxyConfig(proxySettings: Partial<SettingStoreType['proxy']>): {
    mode: Electron.ProxyConfig['mode']
    proxyRules: string
  } {
    let auth = ''
    let proxyUrl = ''
    let mode: Electron.ProxyConfig['mode'] = 'fixed_servers'
    let proxyRules = ''

    if (proxySettings.username && proxySettings.password) {
      auth = `${proxySettings.username}:${proxySettings.password}@`
    }

    switch (proxySettings.type) {
      case 'direct':
      case 'system':
        mode = proxySettings.type as Electron.ProxyConfig['mode']
        break
      case 'http':
        proxyUrl = `${auth}${proxySettings.host}:${proxySettings.port}`
        proxyRules = `http://${proxyUrl}`
        break
      case 'socks5':
        proxyUrl = `${auth}${proxySettings.host}:${proxySettings.port}`
        proxyRules = `socks5://${proxyUrl}`
        break
    }

    return {
      mode,
      proxyRules,
    }
  }

  // 设置代理
  async setupProxy(): Promise<void> {
    try {
      // 获取代理设置
      const proxySettings = await settingStore.get('proxy')
      const config: Electron.ProxyConfig = {
        mode: 'fixed_servers',
        proxyBypassRules: '<local>,localhost,127.0.0.1,::1',
      }
      const proxyConfig = this.getProxyConfig(proxySettings)
      config.mode = proxyConfig.mode as Electron.ProxyConfig['mode']
      config.proxyRules = proxyConfig.proxyRules

      await session.defaultSession.setProxy(config)
      console.log('代理设置成功:', {
        type: proxySettings.type,
        mode: config.mode,
        rules: config.proxyRules || '无',
      })
    }
    catch (error) {
      console.error('代理设置失败:', error)
      // 发生错误时，尝试直接连接
      await session.defaultSession.setProxy({
        mode: 'direct',
      } as Electron.ProxyConfig)
    }
  }
}

export default new ProxyService()
