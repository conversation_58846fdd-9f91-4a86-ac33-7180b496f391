import type { ReactNode } from 'react'
import { List, Progress } from 'flowbite-react'

export function MediaInfoItem(props: { className?: string, icon: ReactNode, content: ReactNode }) {
  return (
    <section className={`${props.className} flex items-center gap-1 text-base`}>
      {props.icon}
      <span className="text-xs">{props.content}</span>
    </section>
  )
}

export function MediaListItem(props: {
  children: ReactNode
  progress: number
}) {
  return (
    <List.Item className="bg-white shadow rounded list-none">
      <div className={`p-3 grid grid-cols-[1fr_auto] gap-x-2 ${props.progress > 0 && props.progress < 100 && 'pb-1.5'}`}>
        {props.children}
      </div>

      {props.progress > 0 && props.progress < 100 && (
        <Progress
          size="sm"
          progress={props.progress}
          color="blue"
          className="rounded-t"
          theme={{
            bar: 'rounded-bl transition-all duration-400 ease-[cubic-bezier(0.08,0.82,0.17,1)]',
          }}
        />
      )}
    </List.Item>
  )
}

export function MediaShowResolution(props: { width: number, height: number }) {
  return (
    <p className="inline-flex gap-1">
      <span>{props.width}</span>
      <span>∗</span>
      <span>{props.height}</span>
    </p>
  )
}
