import { registerIpcMain } from '@egoist/tipc/main'
import { logInfo } from '@main/lib/logger'
import { authRoute } from './auth'
import { settingRoute } from './setting'
import { snifferRoute } from './sniffer'
import { systemRoute } from './system'
import { taskRoute } from './task'
import { videoAudioConverRoute } from './video-audio-conver'
import { videoAudioMergeRoute } from './video-audio-merge'
// 合并所有路由
export const router = {
  ...authRoute,
  ...settingRoute,
  ...taskRoute,
  ...systemRoute,
  ...snifferRoute,
  ...videoAudioMergeRoute,
  ...videoAudioConverRoute,
}

// 导出路由类型
export type Router = typeof router

/**
 * 初始化所有TIPC路由
 */
export function initTipc() {
  // 注册所有路由到ipcMain
  registerIpcMain(router)
  logInfo('TIPC路由已注册', {
    routeCount: Object.keys(router).length,
  })
}
