import type { Subtitle } from '@/store/download'
import { Notice } from '@/components/Notice'
import { useDownloadStore } from '@/store/download'
import {
  DownloadConfig,
  DownloadConfigShowCurrent,
  DownloadSubDropdown,
} from '@renderer/client/download-compose'
import { AUDIO_TRACK_LANGUAGES, SUBTITLE_LANGUAGES } from '@renderer/constants/media'

import { useSnapany } from '@renderer/hooks/snapany'
import { Dropdown } from 'flowbite-react'
import { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

function DownloadConfigMedia() {
  const { t } = useTranslation()

  const {
    subtitleAddOrRemove,
    audioTracksAddOrRemove,
    changeMediaType,
    downloadType,
    isDownloadThumbnail,
    updateHasCover,
    subtitles,
    audioTracks,
    resetAudioTracks,
    resetSubtitles,
    setDefaultAudioTracks,
  } = useDownloadStore()

  const { updateDownloadConfig } = useSnapany()

  const isVideo = useMemo(() => downloadType === 'video', [downloadType])

  const currentInfo = useMemo(() => {
    return isVideo
      ? t('download.resourceType.video')
      : t('download.resourceType.audio')
  }, [isVideo, t])

  const bindAfter = useCallback(
    (fn: () => void) => async () => {
      await fn()
      const latestDownloadConfig = useDownloadStore.getState()
      await updateDownloadConfig(latestDownloadConfig)
    },
    [updateDownloadConfig],
  )

  const handleSubtitleSelection = useCallback(
    (value: Subtitle) => {
      const currentSubtitles = useDownloadStore.getState().subtitles
      const isCurrentlySelected = currentSubtitles.includes(value)

      // 如果是要添加新字幕且已经有5个字幕
      if (!isCurrentlySelected && currentSubtitles.length >= 5) {
        Notice.error(t('download.maxSubtitlesError'))
        return
      }

      // 正常处理添加或移除
      subtitleAddOrRemove(value)
      const latestDownloadConfig = useDownloadStore.getState()
      updateDownloadConfig(latestDownloadConfig)
    },
    [subtitleAddOrRemove, updateDownloadConfig, t],
  )

  return (
    <Dropdown
      className="relative"
      label={(
        <DownloadConfigShowCurrent
          label={t('download.download')}
          content={currentInfo}
        />
      )}
      inline
      dismissOnClick={false}
      theme={{
        arrowIcon: 'ml-1 text-gray-500',
      }}
    >
      <DownloadConfig
        label={t('download.resourceType.video')}
        showCheck={isVideo}
        onClick={bindAfter(() => changeMediaType('video'))}
      />

      <DownloadConfig
        label={t('download.resourceType.audio')}
        showCheck={!isVideo}
        onClick={bindAfter(() => changeMediaType('audio'))}
      />

      <Dropdown.Divider />

      <DownloadConfig
        label={t('download.thumbnail')}
        showCheck={isDownloadThumbnail}
        onClick={bindAfter(() => updateHasCover())}
      />

      {isVideo && (
        <DownloadSubDropdown label={t('download.subtitles')}>
          <DownloadConfig
            label={t('download.none')}
            showCheck={subtitles.length === 0}
            onClick={bindAfter(() => resetSubtitles())}
          />

          {SUBTITLE_LANGUAGES.map(({ label, value }) => (
            <DownloadConfig
              showCheck={Array.isArray(subtitles) && subtitles.includes(value)}
              key={value}
              label={label}
              onClick={() => handleSubtitleSelection(value)}
            />
          ))}
        </DownloadSubDropdown>
      )}

      <DownloadSubDropdown label={t('download.audioTracks')}>
        <DownloadConfig
          label={t('download.default')}
          showCheck={!audioTracks.includes('all')}
          onClick={bindAfter(() => setDefaultAudioTracks())}
        />
        <DownloadConfig
          label={t('download.allTracks')}
          showCheck={audioTracks.includes('all')}
          onClick={bindAfter(() => resetAudioTracks())}
        />
        <Dropdown.Divider />

        {AUDIO_TRACK_LANGUAGES.map(({ label, value }) => (
          <DownloadConfig
            key={value}
            label={label}
            showCheck={!!audioTracks?.includes(value)}
            onClick={bindAfter(() => audioTracksAddOrRemove(value))}
          />
        ))}
      </DownloadSubDropdown>
    </Dropdown>
  )
}

export default DownloadConfigMedia
