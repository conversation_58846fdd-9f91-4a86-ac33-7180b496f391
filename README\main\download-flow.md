```mermaid
graph TD
    %% 定义节点
    Start(开始) --> A[渲染进程生成taskId]
    A --> B[主进程初始化任务]
    B --> C[写入本地存储<br>状态: 解析中]
    C --> D[渲染进程更新界面列表]
    D --> E[主进程加载当前设置]
    E --> F[创建临时目录]
    F --> G[调用yt-dlp解析URL<br>获取JSON信息]

    %% 解析过程
    G --> H[更新本地存储的任务信息<br>封面图/标题等]
    H --> I[通知渲染进程<br>状态: 下载中]
    I --> J[渲染进程再次读取任务列表<br>更新UI]
    J --> K[解析JSON获取下载URL列表]

    %% 下载过程
    K --> L[计算总下载大小]
    L --> M[开始执行下载]
    M --> N[向渲染进程发送下载进度]
    N --> O[下载完成<br>进度: 100%]

    %% 后处理过程
    O --> P[开始合并转换<br>状态: 合并中]
    P --> Q[处理文件<br>设置标题/语言等]
    Q --> R[发送合并进度]
    R --> S[准备移动文件]
    S --> T[移动文件<br>处理重名文件]

    %% 完成过程
    T --> U[向渲染进程发送完成信息<br>状态: 已完成]
    U --> V[更新本地存储<br>保存最终文件路径]
    V --> W[清理临时目录]
    W --> End(结束)

    %% 错误处理
    Error(错误处理) --- B
    Error --- G
    Error --- M
    Error --- P
    Error --- T

    %% 状态流转
    subgraph 状态流转
        State1[解析中] --> State2[下载中]
        State2 --> State3[合并中]
        State3 --> State4[已完成]
    end

    %% 注释说明
    classDef processNode fill:#f9f,stroke:#333,stroke-width:2px;
    classDef stateNode fill:#bbf,stroke:#33f,stroke-width:2px;
    class A,B,E,F,G,K,M,P,Q,T,W processNode;
    class C,H,I,U,V stateNode;
```
