import type { SettingStoreType } from '@main/types/setting'
import type {
  AUDIO_FORMATS,
  AUDIO_TRACK_LANGUAGES,
  DEFAULT_BITRATE,
  DEFAULT_QUALITIES,
  SUBTITLE_LANGUAGES,
  VIDEO_FORMATS,
} from '@renderer/constants/media'
import type { Platform } from '@renderer/utils/environment'
import {
  FORMAT_PLATFORM_MAP,
} from '@renderer/constants/media'
import { addOrRemoveUniqueItemOfArray } from '@renderer/utils/array'
import { mapSettingsToDownloadConfig } from '@renderer/utils/setting'
import { create } from 'zustand'

export type MediaType = 'video' | 'audio'

export type Subtitle = (typeof SUBTITLE_LANGUAGES)[number]['value']

export type AudioTrack = (typeof AUDIO_TRACK_LANGUAGES)[number]['value']

export type Quality = (typeof DEFAULT_QUALITIES)[number]['value'] | 'highest'

export type Bitrate = (typeof DEFAULT_BITRATE)[number]['value'] | 'highest'

export type VideoFormat = (typeof VIDEO_FORMATS)[number]['value']

export type AudioFormat = (typeof AUDIO_FORMATS)[number]['value']

export interface DownloadConfigStore {
  downloadType: MediaType
  isDownloadThumbnail: boolean
  subtitles: Subtitle[]
  audioTracks: (AudioTrack | 'all')[]
  resolution: Quality
  bitrate: Bitrate
  videoFormat: VideoFormat
  audioFormat: AudioFormat
  platform: Platform | null
  subtitleAddOrRemove: (value: Subtitle) => void
  audioTracksAddOrRemove: (value: AudioTrack) => void
  changeMediaType: (type: MediaType) => void
  updateHasCover: () => void
  resetSubtitles: () => void
  resetAudioTracks: () => void
  setDefaultAudioTracks: () => void
  changeQuality: (value: Quality) => void
  changeBitrate: (value: Bitrate) => void
  changePlatform: (value: Platform) => void
  changeVideoFormat: (value: VideoFormat) => void
  changeAudioFormat: (value: AudioFormat) => void
  loadSetting: (setting: SettingStoreType) => void
}

export const useDownloadStore = create<DownloadConfigStore>(set => ({
  downloadType: 'video',
  isDownloadThumbnail: false,
  subtitles: [],
  audioTracks: [],
  resolution: 'highest',
  bitrate: 'highest',
  platform: null,
  videoFormat: 'mp4',
  audioFormat: 'm4a',
  subtitleAddOrRemove: value =>
    set((state) => {
      const uniques = addOrRemoveUniqueItemOfArray(value, state.subtitles)
      return { subtitles: uniques }
    }),
  audioTracksAddOrRemove: value =>
    set((state) => {
      const uniques = addOrRemoveUniqueItemOfArray(
        value,
        state.audioTracks,
      ).filter(item => item !== 'all')
      return { audioTracks: uniques }
    }),

  changeMediaType: value => set(() => ({ downloadType: value })),
  updateHasCover: () => set(state => ({ isDownloadThumbnail: !state.isDownloadThumbnail })),
  resetAudioTracks: () => set(() => ({ audioTracks: ['all'] })),
  resetSubtitles: () => set(() => ({ subtitles: [] })),
  setDefaultAudioTracks: () =>
    set(state => ({
      audioTracks: Array.isArray(state.audioTracks) ? state.audioTracks.filter(item => item !== 'all') : [],
    })),
  changeQuality: value => set(() => ({ resolution: value })),
  changeBitrate: value => set(() => ({ bitrate: value })),
  changeVideoFormat: value =>
    set(() => ({ videoFormat: value, platform: null })),
  changeAudioFormat: value =>
    set(() => ({ audioFormat: value, platform: null })),
  changePlatform: (value) => {
    const videoFormat = FORMAT_PLATFORM_MAP.video[value]
    const audioFormat = FORMAT_PLATFORM_MAP.audio[value]

    set(() => ({ platform: value, audioFormat, videoFormat }))
  },
  loadSetting(setting) {
    const config = mapSettingsToDownloadConfig(setting)
    set(config)
  },
}))
