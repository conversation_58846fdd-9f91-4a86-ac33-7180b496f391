export interface UpdateInfo {
  ytdlpLatestRelease: ytdlpLatestRelease
  latestVersion: string
  normalUpgradeVersion: string
  forcedUpgradeVersion: string
  downloadUrls: DownloadUrls
  upgradeContent: UpgradeContent
}

interface ytdlpLatestRelease {
  version: string
  downloadUrls: {
    macOS: string
    windows: string
  }
}

export interface UpgradeContent {
  en: string
  zh: string
}

interface DownloadUrls {
  windows: string
  macAppleSilicon: string
  macIntel: string
}

export interface SoftwareUpdateProgressData {
  totalSize?: number // 总大小，字节
  downloadedSize?: number // 已下载大小，字节
  filePath?: string
  success: boolean
}
