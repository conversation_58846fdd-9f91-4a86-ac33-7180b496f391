{"download": {"pasteLink": "Paste Link", "pasteLinkFromClipboard": "Paste Link From Clipboard", "playlistChannel": "Playlist/Channel", "pastePlaylistChannelLink": "Paste Playlist/Channel Link", "download": "Download", "resourceType": {"video": "Video", "audio": "Audio"}, "quality": "Quality", "videoQuality": {"best": "Best"}, "audioQuality": {"highest": "Highest"}, "format": "Format", "for": "For", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "subtitles": "Subtitles", "maxSubtitlesError": "Up to 5 subtitles allowed", "audioTracks": "Audio tracks", "allTracks": "All Tracks", "default": "<PERSON><PERSON><PERSON>", "none": "None", "modal": {"selectAll": "Select All", "cancelAll": "Deselect All", "cancel": "Cancel", "download": "Download", "needDownloadToSelect": "Select links to download"}, "emptyState": {"step1": "Step 1: Copy video URL", "step2": "Step 2: Click to paste link and download"}, "popconfirm": {"downloadingDeleteTitle": "Are you sure you want to delete this task?", "deleteText": "Delete", "openFileFailed": "Open file failed, do you want to delete?"}}, "taskStatus": {"retrievingInformation": "Retrieving information", "downloading": "Downloading", "audioDownloading": "Audio Downloading", "videoDownloading": "Video Downloading", "subtitlesDownloading": "Subtitles Downloading", "pendingConversion": "Pending conversion", "converting": "Converting", "merging": "Merging", "downloadFailed": "Download Failed", "parseFailed": "Parse Failed", "cancelled": "Cancelled", "preparingToDownload": "Preparing to download"}, "taskActions": {"retry": "Retry", "delete": "Remove", "showInFinder": "Show in Finder", "showInFolder": "Show in Folder", "more": "More", "logIn": "Log in", "nowLogIn": "Now Log in", "timeLeft": "Time left", "speed": "Speed", "fileSize": "File size", "stopRecording": "Stop Recording"}, "taskModal": {"tip1": "Some downloads are still in progress. Are you sure you want to quit?", "confirmClose": "Exit App", "continueDownload": "Continue", "tip2": "Some downloads were interrupted the last time. Would you like to resume them?", "cancelDownload": "Cancel", "download": "Resume"}, "auth": {"logIn": "Log in", "logOut": "Log out", "logInToX": "Log in to ", "done": "Done", "cancel": "Cancel", "cancelLogin": "<PERSON><PERSON>", "cannotAccess": "This site can't be reached", "connectInterrupt": "Your connection was interrupted", "noInternet": "No internet", "noWork": "This page isn't working", "errorDescription1": "unexpectedly closed the connection.", "errorDescription2": "took too long to respond.", "errorDescription3": "refused to connect.", "errorDescription4": "The webpage at {{domain}} might be temporarily down or it may have moved permanently to a new web address.", "errorDescription5": "Check if there is a typo in {{domain}}.", "errorDescription6": "A network change was detected.", "errorDescription7": "There is something wrong with the proxy server, or the address is incorrect.", "errorDescription8": "didn't send any data.", "errorDescription9": "The connection was reset.", "try": "Try:", "tip1": "Checking the connection", "tip2": "Checking the proxy and the firewall"}, "contextMenu": {"copyCaption": "Copy Text", "copyLinkAddress": "Copy Link Address", "openInBrowser": "Open Link in Browser", "remove": "Remove", "removeAll": "Remove All"}, "errors": {"connectionTimeout": "Connection timed out, please check the network connection", "unsupportedUrl": "URL not supported yet. Coming soon", "needLoginToDownload": "Need to log in to download", "fileNotFound": "File not found", "folderNotFound": "Folder not found", "openFileLocationFailed": "Open File Location Failed", "clipboardNotContainsValidUrl": "Clipboard does not contain a valid URL", "retryFailed": "Retry failed", "checkVersionFailed": "Check Version Failed", "notImplemented": "Not implemented yet", "parseError": "Parse error", "downloadError": "Download error, please check the network connection", "mergeError": "Convert error", "moveError": "Move error", "interrupted": "Interrupted", "parseInterruptedError": "Parse interrupted", "downloadInterruptedError": "Download interrupted", "pendingConversionInterruptedError": "Pending conversion interrupted", "mergeInterruptedError": "Convert interrupted", "noVideoFormats": "No videos available", "timeout": "Connection timed out, please check the network connection or proxy settings", "needPurchase": "Need to purchase the course and log in to download", "serverError": "Server error,  please try again later", "videoNotAccess": "Video unavailable, please check the URL or proxy settings", "diskFull": "Disk space is full, please free up disk space and try again", "permissionDenied": "\"Save to\" folder is not writable", "changeFolder": "Change Folder"}, "messages": {"saveSuccess": "Save Success", "saveFailed": "Save Failed", "authSuccess": "Authorization Success", "authFailed": "Authorization Failed", "removeAuthSuccess": "Remove Authorization Success", "removeAuthFailed": "Remove Authorization Failed", "validUrlPrompt": "Please enter a valid URL", "websiteAlreadyInList": "This website is already in the list", "defaultError": "Operation Failed"}, "dialogs": {"removeAll": {"removeAllItemsFromTheList": "Remove all items from the list?", "deleteDownloadedFiles": "Delete downloaded files", "removeDownloading": "Remove ongoing downloads", "remove": "Remove", "cancel": "Cancel"}, "fileDeleted": {"fileHasBeenDeletedOrMoved": "The file has been deleted or moved. Remove this item?", "remove": "Remove", "cancel": "Cancel"}, "deleteDownloading": {"fileIsDownloading": "The file is downloading. Delete it？", "delete": "Delete", "cancel": "Cancel"}}, "menu": {"website": "Website", "settings": "Settings"}, "settings": {"general": "General", "saveTo": "Save to", "changeFolderBrowser": "Change Folder", "language": "Language", "system": "System", "createSubdirectoriesForDownloadedPlaylistsAndChannels": "Create subdirectories for downloaded playlists and channels", "numerateFilesInPlaylistsAndChannels": "Numerate files in playlists and channels", "embedSubtitlesInVideoFile": "Embed subtitles in video file", "authorization": "Authorization", "logOut": "Log out", "logIn": "Log in", "delete": "Delete", "addUrl": "Add", "enterTheWebsiteUrl": "Enter the website URL", "authorizationPanelTips": "Logging in to the website allows for downloading of age-restricted content, membership content you have purchased, and other private content.", "proxy": "Proxy", "proxyType": "Proxy Type", "httpProxy": "HTTP Proxy", "socks5Proxy": "SOCKS5 Proxy", "usingSystemProxy": "Use System Proxy", "notUsingProxy": "Do Not Use Proxy", "host": "Host", "port": "Port", "proxyInfoMessage": {"pleaseEnterProxyHost": "Please enter the proxy host address", "pleaseEnterValidProxyHost": "Please enter a valid host address", "pleaseEnterProxyPort": "Please enter the proxy port", "pleaseEnterValidProxyPort": "Please enter a valid port number (1-65535)", "optional": "Optional"}, "login": "Username", "password": "Password", "testConnection": "Test Connection", "testConnectionTip": "Testing connection", "testConnectionSuccess": "Connection successful! ", "testConnectionFailed": "Connection failed, check proxy settings", "save": "Save", "saved": "Saved", "about": "About", "version": "Version", "componentVersion": "Component Version", "update": "Updating...", "updateErrorAndRetry": "Update failed. <PERSON><PERSON> to retry", "latestVersion": "Latest Version", "upgrade": "Upgrade", "message": {"loadSettingsFailed": "Load settings failed"}, "checkVersion": "Check Version", "latestVersionAvailable": "Found latest version", "latestVersionNotAvailable": "Already the latest version"}, "update": {"newVersion": "New version", "newVersionAvailable": "New version available", "whatsNew": "What's New", "upgradeNow": "Upgrade Now", "downloading": "Downloading...", "remindAfterDownload": "Remind Me After Download", "newVersionReady": "New version is ready", "installNow": "Install Now", "remindLater": "Remind Me Later", "installTip": "New version is ready, Do you want to install it now?", "laterBtn": "Install Later", "downloadErrorTip": "Download failed. Please visit our website to download the latest installation package.", "nextUpdate": "Remind Me Later", "goWebsite": "Go Website"}, "network": {"search": "Search", "searchPlaceholder": "Search by keywords", "addSite": "Add Website", "go": "Go", "noFilesFound": "No Files Found", "emptyTip1": "No videos or audio found.", "emptyTip2": "Try playing a video and we’ll grab it for you.", "resourceTitle": "{{number}} Files Found", "all": "All({{number}})", "video": "Video({{number}})", "audio": "Audio({{number}})", "selectAll": "Select All", "download": "Download", "downloadTip": "Download", "downloadedTip": "Downloaded", "previewTip": "Preview", "copyLinkTip": "Copy Link", "copyLinkSuccess": "<PERSON>pied", "live": "Live", "addSiteModal": {"title": "Add Website", "site": "URL", "sitePlaceholder": "URL", "name": "Title", "namePlaceholder": "Title", "add": "Save"}}, "format": {"header": "Media Converter", "subHeader": "Easily convert media files in batches", "videoConverter": "Video Converter", "videoDescription": "MP4, MKV, MOV, WebM, AVI, MPEG, WMV, FLV, and more", "audioConverter": "Audio Converter", "audioDescription": "MP3, AAC, WAV, FLAC, ALAC, M4A, OGG, WMA, and more", "imageConverter": "Image Converter", "imageDescription": "JPEG, PNG, GIF, BMP, WebP, RAW, TIFF, HEIF, and more", "chooseFiles": "<PERSON><PERSON>", "tipTitle": "How it works:", "tip1": "Click “Choose Files” or drag files into the box", "tip2": "Select your desired output format", "tip3": "Click “Convert”, your files will be saved automatically after conversion", "total": "{{number}} item", "failed": "Failed {{number}}", "fileName": "File Name", "resolution": "Resolution", "duration": "Duration", "original": "Original", "output": "Output", "addFiles": "Add Files", "clear": "Clear", "outputFormat": "Output", "searchPlaceholder": "Search Format", "all": "All", "video": "Video", "audio": "Audio", "convert": "Convert", "converting": "Converting", "completed": "Completed", "clearModal": {"title": "Files are converting. Clear the list?", "cancel": "Cancel", "clear": "Clear"}}, "merge": {"noData": "Select files or drag files here", "selectFiles": "Select files", "fileName": "File Name", "duration": "Duration", "format": "Format", "video": "Video", "audio": "Audio", "subtitle": "Subtitle", "addFiles": "Add Files", "clear": "Clear", "needAddFile": "Please add a video file", "merge": "<PERSON><PERSON>", "merging": "Merging", "cancel": "Cancel", "failed": "Failed", "close": "Cancel", "retry": "Retry", "success": "Success", "saveTip": "has been saved", "view": "View", "viewInWin": "Show in Folder", "viewInMac": "Show in Finder", "mergePrefixName": "[Merged]"}, "mainMenu": {"download": "Download", "online": "Online", "convert": "Convert", "audioVideoMerger": "Audio Video Merger", "joinTelegramGroup": "Join Telegram Group", "joinDiscordCommunity": "Join Discord Community"}, "application": {"menu": {"download": "Download", "network": "Online", "format": "Convert", "merge": "<PERSON><PERSON>"}, "loading": "loading..."}, "common": {"cancel": "Cancel", "ok": "Ok", "selectAll": "Select All", "cancelAll": "Deselect All"}}