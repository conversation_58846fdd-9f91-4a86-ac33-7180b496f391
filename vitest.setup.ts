import path from 'node:path'
import { vi } from 'vitest'

// 模拟electron
vi.mock('electron', () => {
  return {
    app: {
      getPath: vi.fn((key: string) => {
        if (key === 'userData') {
          return path.join(__dirname, 'drizzle')
        }
        return '/fake/path'
      }),
    },
  }
})

// 模拟 process.resourcesPath
Object.defineProperty(process, 'resourcesPath', {
  value: path.join(__dirname, 'drizzle/resources'),
  configurable: true,
})
