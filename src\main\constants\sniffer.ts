import type { ExtFilter, RegexFilter, TypeFilter } from '@main/types/sniffer'

/**
 * 默认文件扩展名过滤规则
 */
export const defaultExtFilters: ExtFilter[] = [
  { ext: 'flv', minSize: 0, enabled: true },
  { ext: 'hlv', minSize: 0, enabled: true },
  { ext: 'f4v', minSize: 0, enabled: true },
  { ext: 'mp4', minSize: 0, enabled: true },
  { ext: 'mp3', minSize: 0, enabled: true },
  { ext: 'wma', minSize: 0, enabled: true },
  { ext: 'wav', minSize: 0, enabled: true },
  { ext: 'm4a', minSize: 0, enabled: true },
  { ext: 'ts', minSize: 0, enabled: false },
  { ext: 'webm', minSize: 0, enabled: true },
  { ext: 'ogg', minSize: 0, enabled: true },
  { ext: 'ogv', minSize: 0, enabled: true },
  { ext: 'acc', minSize: 0, enabled: true },
  { ext: 'mov', minSize: 0, enabled: true },
  { ext: 'mkv', minSize: 0, enabled: true },
  { ext: 'm4s', minSize: 0, enabled: true },
  { ext: 'm3u8', minSize: 0, enabled: true },
  { ext: 'm3u', minSize: 0, enabled: true },
  { ext: 'mpeg', minSize: 0, enabled: true },
  { ext: 'avi', minSize: 0, enabled: true },
  { ext: 'wmv', minSize: 0, enabled: true },
  { ext: 'asf', minSize: 0, enabled: true },
  { ext: 'movie', minSize: 0, enabled: true },
  { ext: 'divx', minSize: 0, enabled: true },
  { ext: 'mpeg4', minSize: 0, enabled: true },
  { ext: 'vid', minSize: 0, enabled: true },
  { ext: 'aac', minSize: 0, enabled: true },
  { ext: 'mpd', minSize: 0, enabled: true },
  { ext: 'weba', minSize: 0, enabled: true },
  { ext: 'opus', minSize: 0, enabled: true },
]

/**
 * 默认MIME类型过滤规则
 */
export const defaultTypeFilters: TypeFilter[] = [
  { mime: 'audio/*', minSize: 0, enabled: true },
  { mime: 'video/*', minSize: 0, enabled: true },
  { mime: 'application/ogg', minSize: 0, enabled: true },
  { mime: 'application/vnd.apple.mpegurl', minSize: 0, enabled: true },
  { mime: 'application/x-mpegurl', minSize: 0, enabled: true },
  { mime: 'application/mpegurl', minSize: 0, enabled: true },
  { mime: 'application/octet-stream-m3u8', minSize: 0, enabled: true },
  { mime: 'application/dash+xml', minSize: 0, enabled: true },
  { mime: 'application/m4s', minSize: 0, enabled: true },
]

/**
 * 默认正则表达式过滤规则
 */
export const defaultRegexFilters: RegexFilter[] = [
  {
    pattern: 'https://cache\\.video\\.[a-z]*\\.com/dash\\?tvid=.*',
    flags: 'ig',
    specifiedExt: 'json',
    isBlocking: false,
    enabled: false,
  },
  {
    pattern: '.*\\.bilivideo\\.(com|cn).*\\/live-bvc\\/.*m4s',
    flags: 'ig',
    specifiedExt: '',
    isBlocking: true,
    enabled: false,
  },
]
