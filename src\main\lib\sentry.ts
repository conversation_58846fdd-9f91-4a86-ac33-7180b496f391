import { isDev } from '@main/constants/env'
import { deviceId } from '@main/constants/system'
import * as Sentry from '@sentry/electron/main'
import { app } from 'electron'

/**
 * 初始化 Sentry
 */
export async function initSentry() {
  Sentry.init({
    dsn: 'https://<EMAIL>/4508499640909824',
    integrations: [
      Sentry.captureConsoleIntegration({
        levels: ['error'],
      }),
    ],
    enabled: !isDev,
  })
  Sentry.setTag('version', app.getVersion())
  Sentry.setTag('device_id', deviceId)
}
