import { checkFileExists, getBinPath, setFilePermissions } from '@main/utils'

/**
 * 初始化 yt-dlp
 * @returns Promise，解析为 yt-dlp 路径，如果初始化失败则抛出错误
 */
export async function initYtDlp(): Promise<string> {
  try {
    const ytDlpPath = getBinPath('yt-dlp')
    // 在 Mac 平台上设置执行权限
    await setFilePermissions(ytDlpPath)
    // 检查文件是否存在
    const exists = await checkFileExists(ytDlpPath)
    if (!exists) {
      throw new Error('yt-dlp 可执行文件不存在，请重新安装应用')
    }

    return ytDlpPath
  }
  catch (error) {
    console.error('yt-dlp 初始化失败:', error)
    throw error
  }
}
