import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core'

export const task = sqliteTable('task', {
  id: text('id').primaryKey(), // 任务id
  text: text('text').notNull(), // 任务文案
  url: text('url').notNull(), // 任务url
  thumbnail: text('thumbnail'), // 任务封面图
  requestHeaders: text('request_headers'), // 任务http请求头
  extension: text('extension'), // 任务扩展名
  duration: integer('duration'), // 任务时长，单位秒
  fileSize: integer('file_size'), // 任务文件大小，单位字节
  filePath: text('file_path'), // 任务文件路径
  resolutionWidth: integer('resolution_width'), // 任务分辨率宽度
  resolutionHeight: integer('resolution_height'), // 任务分辨率高度
  bitrate: integer('bitrate'), // 比特率，单位：bps
  taskStatus: text('task_status').notNull(), // 任务状态
  errorStatus: text('error_status'), // 错误状态
  errorMessage: text('error_message'), // 错误信息
  errorAction: text('error_action'), // 错误操作
  tempTask: text('temp_task'), // 临时任务信息
  isLive: integer('is_live', { mode: 'boolean' }).default(false), // 是否为直播任务
  createdAt: integer('created_at').notNull(), // 任务创建时间
  updatedAt: integer('updated_at').notNull(), // 任务更新时间
})

export type InsertTask = typeof task.$inferInsert
export type SelectTask = typeof task.$inferSelect
