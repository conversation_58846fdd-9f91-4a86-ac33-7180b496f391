import { Buffer } from 'node:buffer'
import net from 'node:net'

/**
 * 测试SOCKS5代理连接
 * @param proxyConfig 代理配置
 * @param proxyConfig.url 代理服务器地址
 * @param proxyConfig.port 代理服务器端口
 * @param proxyConfig.username 代理认证用户名(可选)
 * @param proxyConfig.password 代理认证密码(可选)
 * @param proxyConfig.type 代理类型(可选)
 * @returns 连接是否成功
 */
export async function testSocks5ProxyConnection(proxyConfig: { url: string, port: number, username?: string, password?: string, type?: string }): Promise<boolean> {
  return new Promise((resolve, reject) => {
    try {
      // 创建连接选项
      const connectOptions: net.NetConnectOpts = {
        port: proxyConfig.port,
        host: proxyConfig.url,
      }

      const proxyTest = net.connect(connectOptions)

      // 处理连接错误 - 将错误处理移到这里，确保DNS错误也能被捕获
      proxyTest.on('error', (err) => {
        console.log('SOCKS5代理: 连接错误', err.message)
        proxyTest.destroy()
        reject(err)
      })

      proxyTest.on('connect', () => {
        console.log('SOCKS5代理: 已建立TCP连接')
        // 创建SOCKS5握手包
        let handshakePacket: Buffer
        if (proxyConfig.username && proxyConfig.password) {
          // 有身份验证的SOCKS5握手
          handshakePacket = Buffer.from([
            5, // SOCKS版本
            2, // 认证方法数量
            0, // 无认证方法 (作为备选)
            2, // 用户名/密码认证方法
          ])
          console.log('SOCKS5代理: 发送握手包 (支持用户名/密码认证)')
        }
        else {
          // 无身份验证的SOCKS5握手
          handshakePacket = Buffer.from([
            5, // SOCKS版本
            1, // 认证方法数量
            0, // 无认证方法
          ])
          console.log('SOCKS5代理: 发送握手包 (无认证)')
        }
        proxyTest.write(handshakePacket)

        // 用于处理代理服务器的响应
        let dataBuffer = Buffer.alloc(0)
        let authSent = false

        proxyTest.on('data', (chunk) => {
          console.log('SOCKS5代理: 收到数据', chunk.length, '字节, authSent:', authSent)
          dataBuffer = Buffer.concat([dataBuffer, chunk])
          console.log('SOCKS5代理: 当前缓冲区', dataBuffer.length, '字节，内容:', [...dataBuffer])

          // 检查握手响应
          if (!authSent && dataBuffer.length >= 2) {
            const version = dataBuffer[0]
            const authMethod = dataBuffer[1]
            console.log('SOCKS5代理: 握手响应 - 版本:', version, '认证方法:', authMethod)

            // 检查SOCKS版本
            if (version !== 5) {
              console.log('SOCKS5代理: 不是有效的SOCKS5代理')
              proxyTest.destroy()
              reject(new Error('不是有效的SOCKS5代理'))
              return
            }

            // 处理需要认证的情况
            if (authMethod === 2 && proxyConfig.username && proxyConfig.password) {
              console.log('SOCKS5代理: 服务器要求用户名/密码认证')
              // 创建用户名/密码认证包
              const usernameBuffer = Buffer.from(proxyConfig.username)
              const passwordBuffer = Buffer.from(proxyConfig.password)
              const authPacket = Buffer.alloc(3 + usernameBuffer.length + passwordBuffer.length)

              authPacket[0] = 1 // 认证子版本
              authPacket[1] = usernameBuffer.length // 用户名长度
              usernameBuffer.copy(authPacket, 2) // 复制用户名
              authPacket[2 + usernameBuffer.length] = passwordBuffer.length // 密码长度
              passwordBuffer.copy(authPacket, 3 + usernameBuffer.length) // 复制密码

              console.log('SOCKS5代理: 发送认证包', [...authPacket])
              proxyTest.write(authPacket)
              dataBuffer = Buffer.alloc(0) // 清空缓冲区，准备接收认证响应
              authSent = true
            }
            // 如果服务器选择无认证方法，则连接成功
            else if (authMethod === 0) {
              console.log('SOCKS5代理: 无需认证，连接成功')
              proxyTest.destroy()
              resolve(true)
            }
            // 如果服务器不支持我们提供的认证方法
            else {
              console.log('SOCKS5代理: 服务器不支持提供的认证方法')
              proxyTest.destroy()
              reject(new Error('代理认证失败: 不支持的认证方法'))
            }
          }
          // 处理认证响应
          else if (authSent && dataBuffer.length >= 2) {
            const authVersion = dataBuffer[0]
            const authStatus = dataBuffer[1]
            console.log('SOCKS5代理: 认证响应 - 版本:', authVersion, '状态:', authStatus)

            // 认证成功（状态码为0）
            if (authStatus === 0) {
              console.log('SOCKS5代理: 认证成功')
              proxyTest.destroy()
              resolve(true)
            }
            else {
              // 认证失败
              console.log('SOCKS5代理: 认证失败')
              proxyTest.destroy()
              reject(new Error('代理认证失败: 用户名或密码错误'))
            }
          }
        })
      })
      // 设置连接超时
      proxyTest.setTimeout(10000, () => {
        console.log('SOCKS5代理: 连接超时')
        proxyTest.destroy()
        reject(new Error('连接超时'))
      })
    }
    catch (error) {
      reject(error)
    }
  })
}

/**
 * 测试HTTP代理连接
 * @param proxyConfig 代理配置
 * @param proxyConfig.url 代理服务器地址
 * @param proxyConfig.port 代理服务器端口
 * @param proxyConfig.username 代理认证用户名(可选)
 * @param proxyConfig.password 代理认证密码(可选)
 * @param proxyConfig.type 代理类型(可选)
 * @returns 连接是否成功
 */
export async function testHttpProxyConnection(proxyConfig: { url: string, port: number, username?: string, password?: string, type?: string }): Promise<boolean> {
  return new Promise((resolve, reject) => {
    try {
      // 创建连接选项
      const connectOptions: net.NetConnectOpts = {
        port: proxyConfig.port,
        host: proxyConfig.url,
      }

      console.log('HTTP代理: 尝试连接')

      // 如果有用户名和密码，添加基本认证头部
      if (proxyConfig.username && proxyConfig.password) {
        // 建立连接
        const proxyTest = net.connect(connectOptions, () => {
          console.log('HTTP代理: 已建立TCP连接 (需要认证)')
          // 发送 CONNECT 请求并添加认证头
          const auth = Buffer.from(`${proxyConfig.username}:${proxyConfig.password}`).toString('base64')
          const connectRequest = `CONNECT ${proxyConfig.url}:${proxyConfig.port} HTTP/1.1\r\n`
            + `Host: ${proxyConfig.url}:${proxyConfig.port}\r\n`
            + `Proxy-Authorization: Basic ${auth}\r\n`
            + `\r\n`

          console.log('HTTP代理: 发送带认证的CONNECT请求')
          proxyTest.write(connectRequest)

          // 检查响应
          let response = ''
          proxyTest.on('data', (chunk) => {
            response += chunk.toString()
            console.log('HTTP代理: 收到响应:', response)

            if (response.includes('200 Connection established') || response.includes('HTTP/1.1 200')) {
              console.log('HTTP代理: 连接成功')
              proxyTest.destroy()
              resolve(true)
            }
            else if (response.includes('407 Proxy Authentication Required') || response.includes('401 Unauthorized')) {
              console.log('HTTP代理: 认证失败')
              proxyTest.destroy()
              reject(new Error('代理认证失败'))
            }
          })
        })

        proxyTest.on('error', (err) => {
          console.log('HTTP代理: 连接错误', err.message)
          proxyTest.destroy()
          reject(err)
        })

        proxyTest.setTimeout(10000, () => {
          console.log('HTTP代理: 连接超时')
          proxyTest.destroy()
          reject(new Error('连接超时'))
        })
      }
      else {
        // 不需要认证的情况
        console.log('HTTP代理: 尝试无认证连接')
        const proxyTest = net.connect(connectOptions, () => {
          console.log('HTTP代理: 连接成功 (无需认证)')
          proxyTest.destroy()
          resolve(true)
        })

        proxyTest.on('error', (err) => {
          console.log('HTTP代理: 连接错误', err.message)
          proxyTest.destroy()
          reject(err)
        })

        proxyTest.setTimeout(10000, () => {
          console.log('HTTP代理: 连接超时')
          proxyTest.destroy()
          reject(new Error('连接超时'))
        })
      }
    }
    catch (e) {
      reject(e)
    }
  })
}
