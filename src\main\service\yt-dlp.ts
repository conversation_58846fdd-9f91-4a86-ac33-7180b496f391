import type { RendererHandlers } from '@main/renderer-handlers'
import type { YtDlpResponse } from '@main/types'
import type { YtDlpStatus } from '@main/types/yt-dlp'
import type { ChildProcessWithoutNullStreams } from 'node:child_process'
import { exec, spawn } from 'node:child_process'
import fs from 'node:fs/promises'
import path from 'node:path'
import { promisify } from 'node:util'
import { getRendererHandlers } from '@egoist/tipc/main'
import { logError, logInfo, logWarn } from '@main/lib/logger'
import { settingStore } from '@main/lib/store'
import { ytDlpStore } from '@main/lib/store/yt-dlp'
import { getBinPath, isFileLockedMac, isFileLockedWin } from '@main/utils'
import FileDownloader from '@main/utils/file-downloader'
import { getMainWindow } from '@main/window'
import { getSoftwareInfo } from '../utils/software'
import AuthService from './auth'
import ProxyService from './proxy'

const execAsync = promisify(exec)

// 存储正在运行的yt-dlp进程
const ytDlpProcesses = new Map<string, ChildProcessWithoutNullStreams>()

class YtDlpService {
  constructor() { }

  /**
   * 执行 yt-dlp 命令
   * @param args - 命令参数
   * @returns 命令输出
   */
  async execute(args: string[]): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      const ytDlpPath = getBinPath('yt-dlp')
      const ytDlp = spawn(ytDlpPath, args)

      let output = ''
      let errorOutput = ''

      ytDlp.stdout.on('data', (data) => {
        output += data.toString()
      })

      ytDlp.stderr.on('data', (data) => {
        const message = data.toString()
        if (!message.includes('WARNING:')) {
          errorOutput += message
        }
      })

      ytDlp.on('close', (code) => {
        if (code === 0 && output) {
          resolve(output)
        }
        else {
          reject(new Error(errorOutput))
        }
      })

      ytDlp.on('error', (error) => {
        reject(error)
      })
    })
  }

  /**
   * 通过 yt-dlp 获取解析信息
   * @param url - 视频 URL
   * @param taskId - 任务ID
   * @returns 视频信息
   */
  async getParseInfo(url: string, taskId?: string): Promise<YtDlpResponse> {
    const args = [
      url,
      '--dump-json',
      '--no-check-certificates',
      '--no-warnings',
      '--no-playlist',
      '--ignore-errors',
      '--ignore-config',
      '--no-cache-dir',
      '--prefer-insecure',
      '--extractor-args',
      'generic:extract_flat=true',
      '--add-header',
      'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      '--add-header',
      'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      '--add-header',
      'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
      '--retries',
      '3',
      '--socket-timeout',
      '10',
      '--format-sort',
      'res,ext:mp4:m4a',
    ]

    // 先获取代理配置
    const setting = await settingStore.get('proxy')
    const proxyConfig = ProxyService.getProxyConfig(setting)
    // 如果有代理配置，添加代理参数
    if (proxyConfig.mode !== 'system') {
      args.push('--proxy', proxyConfig.proxyRules)
    }
    const cookiePath = await AuthService.getCookiesFilePath()
    // 获取cookie文件路径
    if (cookiePath !== '') {
      args.push('--cookies', cookiePath)
    }
    // 执行 yt-dlp 命令
    let output = ''
    try {
      const ytDlpPath = getBinPath('yt-dlp')
      const ytDlpProcess = spawn(ytDlpPath, args)

      // 如果有taskId，存储进程
      if (taskId) {
        ytDlpProcesses.set(taskId, ytDlpProcess)
      }

      output = await new Promise<string>((resolve, reject) => {
        let outputData = ''
        let errorData = ''

        ytDlpProcess.stdout.on('data', (data) => {
          outputData += data.toString()
        })

        ytDlpProcess.stderr.on('data', (data) => {
          const message = data.toString()
          if (!message.includes('WARNING:')) {
            errorData += message
          }
        })

        ytDlpProcess.on('close', (code) => {
          // 清理进程引用
          if (taskId) {
            ytDlpProcesses.delete(taskId)
          }

          if (code === 0 && outputData) {
            resolve(outputData)
          }
          else {
            reject(new Error(errorData))
          }
        })

        ytDlpProcess.on('error', (error) => {
          // 清理进程引用
          if (taskId) {
            ytDlpProcesses.delete(taskId)
          }
          reject(error)
        })
      })

      return JSON.parse(output) as YtDlpResponse
    }
    catch (error) {
      throw new Error(`获取解析信息失败，${output}, ${error}`)
    }
  }

  /**
   * 取消yt-dlp进程
   * @param taskId - 任务ID
   */
  cancelYtDlpProcess(taskId: string): void {
    if (!taskId) {
      logWarn('尝试取消无效的yt-dlp进程', { reason: 'taskId为空' })
      return
    }

    logInfo('尝试取消yt-dlp进程', { taskId })
    const ytdlpProcess = ytDlpProcesses.get(taskId)
    if (ytdlpProcess) {
      try {
        // 在Windows上使用SIGTERM，在其他平台上使用SIGINT
        const signal = process.platform === 'win32' ? 'SIGTERM' : 'SIGINT'
        ytdlpProcess.kill(signal)
        logInfo('yt-dlp进程已成功终止', {
          taskId,
          signal,
          platform: process.platform,
        })
      }
      catch (error) {
        logError('终止yt-dlp进程失败', {
          taskId,
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        })
      }
      finally {
        ytDlpProcesses.delete(taskId)
      }
    }
    else {
      logWarn('未找到yt-dlp进程', { taskId })
    }
  }

  /**
   * 检查 yt-dlp 更新
   * @returns 是否需要更新 true 需要更新 , false 不需要更新
   */
  async checkYtDlpUpdate(): Promise<void> {
    try {
      // 设置更新状态为空闲
      ytDlpStore.set('status', 'idle')

      // 获取本地 yt-dlp 版本
      const localVersion = await this.getYtDlpLocalVersion()
      // 将本地版本存储到 store 中
      ytDlpStore.set('version', localVersion)
      // 获取最新版本
      const latestVersion = await this.getYtDlpLatestVersion()

      console.log('localVersion', localVersion)
      console.log('latestVersion', latestVersion)
      // return latestVersion !== localVersion
      if (latestVersion !== localVersion) {
        try {
          await this.updateYtDlp()
          // 如果更新成功，将状态设置为idle
          ytDlpStore.set('status', 'idle')
        }
        catch (error) {
          console.error('检查更新失败:', error)
          ytDlpStore.set('status', 'failed')
        }
      }
      else {
        ytDlpStore.set('status', 'idle')
      }
    }
    catch {
      ytDlpStore.set('status', 'failed')
    }
  }

  /**
   * 更新 yt-dlp
   * @returns 是否更新成功
   */
  async updateYtDlp(): Promise<void> {
    const ytDlpPath = getBinPath('yt-dlp')
    const tempPath = `${ytDlpPath}.temp`
    const handlers = getRendererHandlers<RendererHandlers>(getMainWindow().webContents)
    const softwareInfo = await getSoftwareInfo()
    const platform = process.platform
    const downloadUrl = platform === 'win32'
      ? softwareInfo.ytdlpLatestRelease.downloadUrls.windows
      : softwareInfo.ytdlpLatestRelease.downloadUrls.macOS

    const updateStatus = (status: YtDlpStatus) => {
      ytDlpStore.set('status', status)
      handlers.onYtDlpUpdateStatus.send(ytDlpStore.get('version'), status)
    }

    try {
      // 下载新版本的yt-dlp
      const isDownloadSuccess = await this.downloadYtDlp(downloadUrl, tempPath)
      if (!isDownloadSuccess) {
        console.error('下载yt-dlp失败')
        updateStatus('failed')
        return
      }

      // 替换旧版本的文件
      try {
        await this.replaceYtDlp(ytDlpPath, tempPath)

        // 更新成功后获取最新版本并更新存储
        const latestVersion = await this.getYtDlpLocalVersion()
        ytDlpStore.set('version', latestVersion)
        updateStatus('idle')
      }
      catch (error) {
        console.error('替换文件失败:', error)
        updateStatus('failed')
      }
    }
    catch (error) {
      console.error('yt-dlp更新失败:', error)
      updateStatus('failed')
    }
  }

  /**
   * 下载 yt-dlp
   * @param downloadUrl - 下载地址
   * @param downloadYtDlpPath - 下载路径
   * @returns 是否下载成功
   */
  async downloadYtDlp(downloadUrl: string, downloadYtDlpPath: string, retryCount = 0): Promise<boolean> {
    try {
      // 检查文件是否已下载且是否可执行
      const checkYtDlpIsTrue = await this.checkYtDlp(downloadYtDlpPath)
      if (checkYtDlpIsTrue) {
        console.log('yt-dlp已下载且可执行，准备替换')
        return true
      }

      // 使用FileDownloader下载
      const fileDownloader = new FileDownloader()
      // 使用目标文件所在目录作为临时目录
      fileDownloader.setTempDir(path.dirname(downloadYtDlpPath))
      fileDownloader.setDeleteTempFile(true)

      // 使用Promise来处理异步下载
      return new Promise<boolean>((resolve, reject) => {
        let isCompleted = false

        fileDownloader.download(downloadUrl, {}, null, {
          onComplete: async (filePath) => {
            try {
              // 重命名文件到目标路径
              await fs.rename(filePath, downloadYtDlpPath)

              // 赋予执行权限
              if (process.platform === 'darwin') {
                await fs.chmod(downloadYtDlpPath, 0o755)
              }

              // 检查文件是否可执行
              const checkYtDlpIsTrue = await this.checkYtDlp(downloadYtDlpPath)

              if (checkYtDlpIsTrue) {
                console.log(downloadYtDlpPath, '可用')
                if (!isCompleted) {
                  isCompleted = true
                  resolve(true)
                }
              }
              else {
                if (!isCompleted) {
                  isCompleted = true
                  reject(new Error('yt-dlp可行性验证失败'))
                }
              }
            }
            catch (error) {
              console.error('yt-dlp下载处理失败:', error)
              if (!isCompleted) {
                isCompleted = true
                reject(error)
              }
            }
          },
          onError: async (error) => {
            console.error('yt-dlp下载失败:', error)
            if (!isCompleted) {
              isCompleted = true
              reject(error)
            }
          },
        }).catch((error) => {
          if (!isCompleted) {
            isCompleted = true
            reject(error)
          }
        })
      })
    }
    catch (error) {
      console.error(`yt-dlp下载失败 (尝试 ${retryCount + 1}/2):`, error)

      // 如果是第一次失败，尝试重试1次
      if (retryCount === 0) {
        console.log('正在尝试重新下载 yt-dlp...')
        return this.downloadYtDlp(downloadUrl, downloadYtDlpPath, 1)
      }

      // 已重试1次仍然失败，返回失败结果
      return false
    }
  }

  /**
   * 检查二进制文件是否存在且可用
   * @param ytDlpPath - ytdlp原目录
   * @returns 是否存在且可用
   */
  async checkYtDlp(ytDlpPath: string): Promise<boolean> {
    try {
      await execAsync(`"${ytDlpPath}" --version`)
      return true
    }
    catch {
      return false
    }
  }

  /**
   * 替换yt-dlp
   * @param ytDlpPath - ytdlp原目录
   * @param ytDlpTempPath - 新版本ytdlp目录
   * @returns 是否下载成功
   */
  async replaceYtDlp(ytDlpPath: string, ytDlpTempPath: string): Promise<void> {
    // 返回一个Promise，在替换成功时resolve
    return new Promise<void>((resolve, reject) => {
      let retries = 0
      let timerId: NodeJS.Timeout | null = null

      const replaceFile = async (): Promise<void> => {
        try {
          // 检查文件是否存在
          const fileExists = await fs.access(ytDlpPath)
            .then(() => true)
            .catch(() => false)

          if (fileExists) {
            // 检查文件是否被占用
            let isLocked = false
            if (process.platform === 'win32') {
              isLocked = await isFileLockedWin(ytDlpPath)
            }
            else if (process.platform === 'darwin') {
              isLocked = await isFileLockedMac(ytDlpPath)
            }
            else {
              // Linux平台简单尝试替换，如果失败视为被占用
              try {
                await fs.rename(ytDlpTempPath, ytDlpPath)
                console.log('yt-dlp 替换成功')
                // 设置权限为可执行
                await fs.chmod(ytDlpPath, 0o755)
                // 清除定时器并完成Promise
                if (timerId) {
                  clearInterval(timerId)
                }
                resolve()
                return
              }
              catch (error) {
                isLocked = true
                console.log('文件无法替换，可能被占用:', error)
              }
            }

            if (isLocked) {
              retries++
              console.log(`yt-dlp文件被占用，已尝试${retries}次，将在1分钟后继续重试`)
              return // 继续等待下一次定时器触发
            }
          }

          // 文件不存在或未被占用，执行替换
          await fs.rename(ytDlpTempPath, ytDlpPath)
          console.log('yt-dlp 替换成功')

          // 设置权限为可执行
          await fs.chmod(ytDlpPath, 0o755)

          // 更新成功，设置状态为空闲
          ytDlpStore.set('status', 'idle')

          // 清除定时器并完成Promise
          if (timerId) {
            clearInterval(timerId)
          }
          resolve()
        }
        catch (error) {
          console.error('替换yt-dlp失败:', error)
          ytDlpStore.set('status', 'failed')
          // 发生严重错误时，清除定时器并拒绝Promise
          if (timerId) {
            clearInterval(timerId)
          }
          reject(error)
        }
      }

      // 立即执行一次
      replaceFile().catch((error) => {
        // 第一次尝试失败但不是致命错误，启动定时器继续尝试
        console.log('首次替换尝试未成功，将启动定时器重试:', error)
      })

      // 设置定时器，每分钟检查一次
      timerId = setInterval(replaceFile, 60 * 1000)
    })
  }

  /**
   * 获取本地 yt-dlp 版本
   * @returns 本地版本
   */
  async getYtDlpLocalVersion(): Promise<string> {
    const version = await this.execute(['--version'])
    return version.trim()
  }

  /**
   * 获取最新版本
   * @returns 最新版本
   */
  async getYtDlpLatestVersion(): Promise<string> {
    const softwareInfo = await getSoftwareInfo()
    return softwareInfo.ytdlpLatestRelease.version
  }
}

export default new YtDlpService()
