export interface SettingStoreType {
  // 下载路径
  downloadPath: string
  // 下载类型
  downloadType: 'video' | 'audio'
  videoConfig: {
    format: {
      format: string // 格式
      platform: string | null // 平台
    }
    // 分辨率
    resolution: number | 'highest'
  }
  audioConfig: {
    format: {
      format: string // 格式
      platform: string | null // 平台
    }
    // 比特率
    bitrate: number | 'highest'
  }
  // 是否下载封面图
  isDownloadThumbnail: boolean
  // 字幕
  subtitles: string[]
  // 音轨
  audioTracks: string[]
  // 最大同时下载数量
  maxConcurrentDownloads: number
  // 以播放列表/频道名称创建子文件夹
  createSubfolder: boolean
  // 给播放列表/频道中的文件添加序号
  addIndexToFile: boolean
  // 是否将字幕嵌入视频文件中
  embedSubtitle: boolean
  // 代理
  proxy: {
    type: 'system' | 'direct' | 'http' | 'https' | 'socks5'
    host?: string
    port?: string
    username?: string
    password?: string
  }
  // 软件语言
  language: string
  authSites: AuthSite[]
}

export interface AuthSite {
  name: string
  url: string
  authUrl: string
  isAuthorized: boolean
  enableDelete: boolean
}
