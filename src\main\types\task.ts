import type { DownloadItem, SettingStoreType } from '@main/types'

export interface TempTask {
  text: string
  thumbnail: string
  items: DownloadItem[]
  setting: SettingStoreType
}

export interface TempTaskProgress {
  totalSize: number | null // 下载的总大小
  downloadedSize: number | null // 已下载大小
  speed: number | null // 下载速度
  eta: string | null // 预计剩余完成时间
  percent?: number | null // 转换进度
}

export interface TaskItem extends TempTaskProgress {
  id: string
  text: string
  url: string
  thumbnail: string | null
  requestHeaders: string | null
  extension: string | null
  duration: number | null
  fileSize: number | null
  filePath: string | null
  resolutionWidth: number | null
  resolutionHeight: number | null
  bitrate: number | null
  taskStatus: string
  errorStatus: string | null
  errorMessage: string | null
  errorAction: string | null
  tempTask: string | null
  createdAt: number
  updatedAt: number
}
