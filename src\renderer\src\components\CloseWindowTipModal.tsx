import { useTaskStore } from '@/store/task'
import { Button, Modal } from 'flowbite-react'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TbExclamationCircleFilled, TbX } from 'react-icons/tb'

function CloseWindowTipModal() {
  const { t } = useTranslation()
  const [showModal, setShowModal] = useState(false)
  const { isOpenCloseWindowModal, setIsOpenCloseWindowModal, closeWindow } = useTaskStore()

  useEffect(() => {
    if (isOpenCloseWindowModal) {
      setShowModal(true)
    }
  }, [isOpenCloseWindowModal])

  const handleClose = () => {
    setIsOpenCloseWindowModal(false)
    setShowModal(false)
  }

  const handleConfirmClose = async () => {
    setIsOpenCloseWindowModal(false)
    closeWindow()
  }

  return (
    <Modal show={showModal} size="lg">
      <Modal.Body className="relative p-5">
        <TbX className="absolute top-3 right-3 w-6 h-6 cursor-pointer text-gray-400" onClick={handleClose} />
        <div className="flex flex-col items-center mt-7">
          <TbExclamationCircleFilled className="w-6 h-6 mb-4 text-gray-400" />
          <p className="max-w-75 text-base text-gray-500 text-center mb-5">
            {t('taskModal.tip1')}
          </p>

          <div className="flex gap-4">
            <Button color="light" onClick={handleConfirmClose}>
              {t('taskModal.confirmClose')}
            </Button>
            <Button onClick={handleClose}>{t('taskModal.continueDownload')}</Button>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  )
}

export default CloseWindowTipModal
