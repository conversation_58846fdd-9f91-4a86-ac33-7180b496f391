import { Button } from 'flowbite-react'
import { Headphones, Image as ImageIcon, VideoCamera } from 'flowbite-react-icons/outline'
import { useTranslation } from 'react-i18next'

function SelectMedia({ handleVideo, handleAudio, handleImage }: { handleVideo: () => void, handleAudio: () => void, handleImage: () => void }) {
  const { t } = useTranslation()

  return (
    <div
      className="w-full h-full flex flex-col items-center justify-center gap-6 px-6"
    >
      <div className="flex flex-col items-center gap-3">
        <h1 className="text-2xl font-semibold">{t('format.header')}</h1>
        <h2 className="text-base">{t('format.subHeader')}</h2>
      </div>
      <div className="flex items-center justify-between gap-6">
        <div className="flex flex-col items-center justify-center gap-4 rounded-lg border-2 border-dashed border-gray-200 px-4 py-6">
          <div className="rounded-full bg-blue-100 p-3">
            <VideoCamera className="text-blue-700 w-6 h-6" />
          </div>
          <div className="text-center">
            <p className="text-sm font-semibold text-gray-900 mb-0.5">{t('format.videoConverter')}</p>
            <p className="text-xs text-gray-500">{t('format.videoDescription')}</p>
          </div>
          <Button outline color="blue" size="sm" onClick={handleVideo}>{t('format.chooseFiles')}</Button>
        </div>
        <div className="flex flex-col items-center justify-center gap-4 rounded-lg border-2 border-dashed border-gray-200 px-4 py-6">
          <div className="rounded-full bg-blue-100 p-3">
            <Headphones className="text-blue-700 w-6 h-6" />
          </div>
          <div className="text-center">
            <p className="text-sm font-semibold text-gray-900 mb-0.5">{t('format.audioConverter')}</p>
            <p className="text-xs text-gray-500">{t('format.audioDescription')}</p>
          </div>
          <Button outline color="blue" size="sm" onClick={handleAudio}>{t('format.chooseFiles')}</Button>
        </div>
        <div className="flex flex-col items-center justify-center gap-4 rounded-lg border-2 border-dashed border-gray-200 px-4 py-6">
          <div className="rounded-full bg-blue-100 p-3">
            <ImageIcon className="text-blue-700 w-6 h-6" />
          </div>
          <div className="text-center">
            <p className="text-sm font-semibold text-gray-900 mb-0.5">{t('format.imageConverter')}</p>
            <p className="text-xs text-gray-500">{t('format.imageDescription')}</p>
          </div>
          <Button outline color="blue" size="sm" onClick={handleImage}>{t('format.chooseFiles')}</Button>
        </div>
      </div>
      <ol className="text-xs text-gray-500 list-decimal list-inside ">
        <p>{t('format.tipTitle')}</p>
        <li>{t('format.tip1')}</li>
        <li>{t('format.tip2')}</li>
        <li>{t('format.tip3')}</li>
      </ol>
    </div>
  )
}

export default SelectMedia
