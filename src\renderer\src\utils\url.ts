/**
 * 验证URL是否有效
 * @param url 要验证的URL字符串
 * @returns 如果URL有效返回true，否则返回false
 */
export function isValidUrl(url: string): boolean {
  try {
    // 如果URL不以http://或https://开头，尝试添加https://
    if (!url.match(/^https?:\/\//i)) {
      url = `https://${url}`
    }

    // 使用URL构造函数验证URL，并检查hostname
    const urlObj = new URL(url)
    const hostname = urlObj.hostname

    // 检查是否包含顶级域名
    const hasTld = /\.[a-z]{2,}$/i.test(hostname)
    if (!hasTld) {
      return false
    }

    return true
  }
  catch {
    return false
  }
}

/**
 * 规范化URL
 * @param url 要规范化的URL字符串
 * @returns 规范化后的URL字符串
 */
export function normalizeUrl(url: string): string {
  try {
    // 如果URL不以http://或https://开头，添加https://
    if (!url.match(/^https?:\/\//i)) {
      url = `https://${url}`
    }

    const urlObj = new URL(url)

    // // 移除www前缀
    // if (urlObj.hostname.startsWith('www.')) {
    //   urlObj.hostname = urlObj.hostname.substring(4)
    // }

    // 移除末尾斜杠
    urlObj.pathname = urlObj.pathname.replace(/\/$/, '')

    return urlObj.toString()
  }
  catch {
    return url
  }
}

/**
 * 获取URL的domain
 * @param url 要处理的URL字符串
 * @returns URL的domain，如果URL无效则返回空字符串
 */
export function getUrlDomain(url: string): string {
  try {
    // 如果URL不以http://或https://开头，尝试添加https://
    if (!url.match(/^https?:\/\//i)) {
      url = `https://${url}`
    }

    // 使用URL构造函数获取hostname
    const urlObj = new URL(url)
    let domain = urlObj.hostname

    // 移除www前缀
    domain = domain.replace(/^www\./, '')

    return domain
  }
  catch {
    return ''
  }
}

/**
 * 检测文本中是否包含URL
 * @param text 要检测的文本内容
 * @returns 如果文本中包含有效URL返回true，否则返回false
 */
export function containsUrl(text: string): boolean {
  // 更宽松的URL匹配规则，确保能匹配到各种格式的URL
  const urlRegex = /(?:https?:\/\/)?(?:www\.)?[-\w@:%.+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b[-\w()@:%+.~#?&/=]*/g
  const matches = text.match(urlRegex) || []

  // 验证每个匹配项
  for (const match of matches) {
    try {
      // 尝试构造 URL 对象
      const url = new URL(match.startsWith('http') ? match : `https://${match}`)

      // 验证 hostname 是否有效
      const hostname = url.hostname
      if (hostname.length > 0 && hostname.includes('.')) {
        return true
      }
    }
    catch {
      // 如果 URL 构造失败，继续检查下一个匹配项
      continue
    }
  }

  return false
}

/**
 * 从文本中提取所有网址
 * @param text 要提取网址的文本内容
 * @returns 提取到的网址数组，如果没有找到则返回空数组
 */
export function extractUrls(text: string): string[] {
  // 初步匹配可能的URL
  const urlRegex = /(?:https?:\/\/)?(?:www\.)?[-\w@:%.+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b[-\w()@:%+.~#?&/=]*/g
  const matches = text.match(urlRegex) || []

  // 使用 Set 去重
  const uniqueUrls = new Set<string>()

  // 严格验证每个匹配项
  for (const match of matches) {
    try {
      // 尝试构造 URL 对象
      const url = new URL(match.startsWith('http') ? match : `https://${match}`)

      // 验证 hostname 是否有效
      const hostname = url.hostname
      if (hostname.length > 0 && hostname.includes('.')) {
        // 规范化并添加到结果集
        uniqueUrls.add(normalizeUrl(url.toString()))
      }
    }
    catch {
      // 如果 URL 构造失败，跳过该匹配项
      continue
    }
  }

  return Array.from(uniqueUrls)
}
