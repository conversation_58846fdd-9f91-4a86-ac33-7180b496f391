import crypto from 'node:crypto'
import fs from 'node:fs'
import path from 'node:path'
import { fetch } from '@main/utils/fetch'
import mime from 'mime-types'

interface FileInfo {
  totalBytes: number // 文件大小, 单位: Bytes
  extension: string // 文件扩展名
  isSupportSharding: boolean // 是否支持分片
}

interface Sharding {
  totalParts: number // 分片总数
  parts: Array<number[]> // 分片范围
}

interface DownloadProgress {
  totalBytes: number // 文件总大小
  downloadedBytes: number // 已下载总字节数
  speedBytes: number // 当前下载速度(字节/秒)
}

class FileDownloader {
  private readonly concurrentTaskCount = 8
  private readonly maxRetryCount = 1
  private deleteTempFile = false

  constructor(
    private readonly proxyUrl = '',
    private tempDir = './temp',
    private readonly headers?: Record<string, string>,
    private taskId?: string,
  ) {
    if (this.taskId) {
      this.tempDir = path.join(this.tempDir, this.taskId)
    }
  }

  public setTempDir(tempDir: string) {
    this.tempDir = tempDir
  }

  public setDeleteTempFile(deleteTempFile: boolean) {
    this.deleteTempFile = deleteTempFile
  }

  private async getFileInfo(
    url: string,
    headers?: Record<string, string>,
    controller?: AbortController,
  ): Promise<FileInfo> {
    const fileInfo: FileInfo = {
      totalBytes: 0,
      extension: '',
      isSupportSharding: false,
    }
    try {
      const response = await fetch({
        url,
        headers: {
          range: 'bytes=0-0',
          ...headers,
          ...this.headers,
        },
        abortController: controller,
      })

      const acceptRanges = response.headers['accept-ranges']
      if (
        acceptRanges === 'bytes'
        || (response.statusCode === 206 && response.headers['content-range'])
      ) {
        const contentRange = response.headers['content-range']
        if (contentRange && !Array.isArray(contentRange)) {
          fileInfo.totalBytes = Number.parseInt(contentRange.split('/')[1] ?? '0')
          if (fileInfo.totalBytes > 0) {
            fileInfo.isSupportSharding = true
          }
        }
      }

      if (!fileInfo.isSupportSharding) {
        // 获取Content-Length
        const contentLength = response.headers['content-length']
        if (contentLength && !Array.isArray(contentLength)) {
          fileInfo.totalBytes = Number.parseInt(contentLength)
        }
      }

      // 尝试获取文件后缀
      const contentType = response.headers['content-type']
      if (contentType && !Array.isArray(contentType)) {
        fileInfo.extension = mime.extension(contentType) || ''
      }

      if (fileInfo.extension === '') {
        const contentDisposition = response.headers['content-disposition']
        if (contentDisposition && !Array.isArray(contentDisposition)) {
          fileInfo.extension
            = contentDisposition.replace(/"/g, '').split('.').pop() || ''
        }
      }

      if (fileInfo.extension === '' || fileInfo.extension === 'bin') {
        // 尝试解析url后缀
        const urlObj = new URL(url)
        fileInfo.extension
          = path.extname(urlObj.pathname).split('.').pop() || ''
      }

      // 确保响应流被正确处理
      response.on('data', () => { })
      response.on('end', () => { })
    }
    catch (error) {
      console.log('[fetch error]', error)
      throw new Error(`获取文件信息失败${error}`)
    }
    return fileInfo
  }

  private async isFileExists(filePath: string): Promise<boolean> {
    return fs.promises
      .access(filePath)
      .then(() => true)
      .catch(() => false)
  }

  private async downloadFilePart(
    url: string,
    start: number,
    end: number,
    tempFilePath: string,
    retryCount: number = 0,
    controller?: AbortController,
    onProgress?: (bytes: number) => void,
    customHeaders?: Record<string, string>,
  ): Promise<void> {
    const writer = fs.createWriteStream(tempFilePath, { flags: 'a' }) // 使用追加模式

    try {
      // 判断文件是否存在
      const fileExists = await this.isFileExists(tempFilePath)
      if (!fileExists) {
        await fs.promises.writeFile(tempFilePath, '')
      }
      // 判断文件大小
      const shardingSize = end - start + 1
      const stats = await fs.promises.stat(tempFilePath)
      const fileSize = stats.size

      // 如果分片已完整下载,直接返回
      if (fileSize === shardingSize) {
        return
      }
      // 文件大小小于分片大小，则下载剩余的分片
      const newStart = start + fileSize
      // 合并默认 headers 和自定义 headers
      let headers = {
        ...this.headers,
        ...customHeaders,
      }

      if (newStart > end) {
        return
      }

      if (start >= 0 && end >= 0) {
        headers = {
          ...headers,
          range: `bytes=${newStart}-${end}`,
        }
      }

      // 使用封装的 fetch 方法
      const response = await fetch({
        url,
        headers,
        abortController: controller,
      })

      if (response.statusCode !== 200 && response.statusCode !== 206) {
        throw new Error(`下载失败，状态码: ${response.statusCode}`)
      }

      await new Promise<void>((resolve, reject) => {
        response.on('data', (chunk) => {
          try {
            writer.write(chunk)
            onProgress?.(chunk.length)
          }
          catch (error) {
            console.error('写入数据时发生错误:', error)
            reject(error)
          }
        })

        response.on('end', () => {
          writer.end(() => resolve())
        })

        response.on('error', (error) => {
          writer.end()
          reject(error)
        })
      })
    }
    catch (error) {
      // 如果是取消操作，直接抛出错误，不进行重试
      if (error instanceof Error
        && (error.message === '请求被取消' || error.message === 'Request aborted' || error.message === 'net::ERR_TIMED_OUT')) {
        throw error
      }

      console.log(`[分片下载错误] 范围: ${start}-${end}`, error)
      if (retryCount < this.maxRetryCount) {
        console.log(`[重试下载] 范围: ${start}-${end}`)
        return this.downloadFilePart(
          url,
          start,
          end,
          tempFilePath,
          retryCount + 1,
          controller,
          onProgress,
          customHeaders,
        )
      }
      // 重试失败，抛出错误以终止所有任务
      const errorMessage
        = error instanceof Error ? error.message : String(error)
      throw new Error(`分片下载失败 [${start}-${end}]: ${errorMessage}`)
    }
    finally {
      // 确保资源被清理
      if (writer && !writer.destroyed) {
        try {
          await new Promise<void>((resolve) => {
            writer.end(() => {
              writer.destroy()
              resolve()
            })
          })
        }
        catch (err) {
          console.error('关闭流时发生错误:', err)
        }
      }
    }
  }

  private getShardingCount(totalBytes: number): Sharding {
    const MB_SIZE = 1024 * 1024
    let partSize = 0

    if (totalBytes < 100 * MB_SIZE) {
      partSize = 5 * MB_SIZE
    }
    else if (totalBytes < 200 * MB_SIZE) {
      partSize = 10 * MB_SIZE
    }
    else if (totalBytes < 2000 * MB_SIZE) {
      partSize = 20 * MB_SIZE
    }
    else {
      partSize = 50 * MB_SIZE
    }

    const totalParts = Math.ceil(totalBytes / partSize)
    const parts = Array.from({ length: totalParts }, (_, i) => [
      i * partSize,
      Math.min((i + 1) * partSize - 1, totalBytes - 1),
    ])

    return {
      totalParts,
      parts,
    }
  }

  private md5(str: string): string {
    return crypto.createHash('md5').update(str).digest('hex')
  }

  private async mergeSharding(
    partFolderName: string,
    extension: string,
    parts: Array<number[]>,
  ): Promise<void> {
    const filePath = path.join(this.tempDir, `${partFolderName}.${extension}`)
    // 创建写入流
    const writeStream = fs.createWriteStream(filePath)

    // 按顺序合并分片
    for (const part of parts) {
      const tempFilePath = path.join(
        this.tempDir,
        partFolderName,
        `${part[0]}-${part[1]}`,
      )

      try {
        // 检查分片是否存在
        const tempFileExists = await this.isFileExists(tempFilePath)
        if (tempFileExists) {
          // 创建读取流并通过管道传输到写入流
          const readStream = fs.createReadStream(tempFilePath)
          await new Promise<void>((resolve, reject) => {
            readStream.pipe(writeStream, { end: false })
            readStream.on('end', resolve)
            readStream.on('error', (err) => {
              readStream.destroy()
              reject(err)
            })
          })
        }
      }
      catch (error) {
        console.error(`处理分片失败: ${part[0]}-${part[1]}`, error)
        // throw new Error(`处理分片失败: ${part[0]}-${part[1]}` + error);
        // 不要立即抛出错误，继续处理其他分片
      }
    }

    // 关闭写入流
    await new Promise<void>((resolve, reject) => {
      writeStream.end()
      writeStream.on('finish', resolve)
      writeStream.on('error', reject)
    })
  }

  private async cleanupShardingFolder(folderPath: string): Promise<void> {
    try {
      // 递归删除文件夹及其内容
      await fs.promises.rm(folderPath, { recursive: true, force: true })
      console.log(`清理分片文件夹成功: ${folderPath}`)
    }
    catch (error) {
      console.error(`清理分片文件夹失败: ${folderPath}`, error)
      throw new Error(`清理分片文件夹失败: ${folderPath} + ${error}`)
    }
  }

  public async download(
    url: string,
    headers?: Record<string, string>,
    controller?: AbortController,
    callbacks?: {
      onProgress?: (progress: DownloadProgress) => Promise<void>
      onComplete?: (filePath: string) => Promise<void>
      onError?: (error: Error) => Promise<void>
    },
  ): Promise<void> {
    let progressTimer: NodeJS.Timeout | undefined
    let downloadedBytes = 0
    let currentSecondBytes = 0
    let lastProgressTime = 0
    let totalBytes = 0

    if (!controller) {
      controller = new AbortController()
    }

    try {
      const res = await this.getFileInfo(url, headers, controller)
      totalBytes = res.totalBytes

      progressTimer = setInterval(() => {
        callbacks?.onProgress?.({
          totalBytes,
          downloadedBytes,
          speedBytes: currentSecondBytes,
        })
        currentSecondBytes = 0
        lastProgressTime = Date.now()
      }, 1000)

      // 创建临时目录
      const tempDirExists = await this.isFileExists(this.tempDir)
      if (!tempDirExists) {
        await fs.promises.mkdir(this.tempDir, { recursive: true })
      }
      const partFolderName = this.md5(url)

      // 支持分片下载
      if (res.isSupportSharding) {
        // 判断分片文件夹是否存在
        const partFolderPath = path.join(this.tempDir, partFolderName)
        const partFolderExists = await this.isFileExists(partFolderPath)
        if (!partFolderExists) {
          await fs.promises.mkdir(partFolderPath, { recursive: true })
        }
        const sharding = this.getShardingCount(res.totalBytes)

        // 遍历分片范围
        for (const part of sharding.parts) {
          const tempFilePath = path.join(
            partFolderPath,
            `${part[0]}-${part[1]}`,
          )
          const tempFileExists = await this.isFileExists(tempFilePath)
          if (tempFileExists) {
            const stats = await fs.promises.stat(tempFilePath)
            downloadedBytes += stats.size
          }

          // 顺序下载每个分片
          await this.downloadFilePart(
            url,
            part[0],
            part[1],
            tempFilePath,
            0,
            controller,
            (bytes) => {
              currentSecondBytes += bytes
              downloadedBytes = downloadedBytes + bytes
            },
            headers,
          )
        }

        console.log('所有分片下载完成，开始合并文件')

        // 清理定时器
        if (progressTimer) {
          clearInterval(progressTimer)
          progressTimer = undefined
        }

        await this.mergeSharding(
          partFolderName,
          res.extension,
          sharding.parts,
        )

        // 最后一次进度更新
        callbacks?.onProgress?.({
          totalBytes,
          downloadedBytes,
          speedBytes: currentSecondBytes / (Date.now() - lastProgressTime),
        })

        callbacks?.onComplete?.(
          path.join(this.tempDir, `${partFolderName}.${res.extension}`),
        )

        if (this.deleteTempFile) {
          await this.cleanupShardingFolder(partFolderPath)
        }
      }
      else {
        // 不支持分片下载的情况
        const filePath = path.join(
          this.tempDir,
          `${partFolderName}.${res.extension}`,
        )

        // 删除文件
        if (await this.isFileExists(filePath)) {
          await fs.promises.unlink(filePath)
        }

        await this.downloadFilePart(
          url,
          -1,
          -1,
          filePath,
          0,
          controller,
          (bytes) => {
            currentSecondBytes += bytes
            downloadedBytes = downloadedBytes + bytes
          },
          headers,
        )

        // 清理定时器
        if (progressTimer) {
          clearInterval(progressTimer)
          progressTimer = undefined
        }

        // 确保非分片下载也显示100%进度
        callbacks?.onProgress?.({
          totalBytes,
          downloadedBytes,
          speedBytes: currentSecondBytes / (Date.now() - lastProgressTime),
        })
        callbacks?.onComplete?.(filePath)
      }
    }
    catch (error) {
      if (progressTimer) {
        clearInterval(progressTimer)
        progressTimer = undefined
      }

      const finalError
        = error instanceof Error ? error : new Error(`下载失败${error}`)

      if (controller.signal.aborted) {
        const abortError = new Error('下载已取消')
        callbacks?.onError?.(abortError)
        throw abortError
      }

      callbacks?.onError?.(finalError)
      throw finalError
    }
    finally {
      // 清理资源
      if (progressTimer) {
        clearInterval(progressTimer)
        progressTimer = undefined
      }
    }
  }

  /**
   * 获取下载链接的文件大小
   * @param url 下载链接
   * @param customHeaders 可选的请求头
   * @returns 文件大小（字节）
   */
  public async getFileSize(
    url: string,
    customHeaders?: Record<string, string>,
    controller?: AbortController,
  ): Promise<number> {
    try {
      const response = await fetch({
        url,
        headers: {
          range: 'bytes=0-0',
          ...this.headers,
          ...customHeaders,
        },
        abortController: controller,
      })
      let fileSize = 0
      // 首先尝试从 content-range 获取文件大小
      const contentRange = response.headers['content-range']
      if (contentRange && !Array.isArray(contentRange)) {
        fileSize = Number.parseInt(contentRange.split('/')[1] ?? '0')
      }

      // 如果 content-range 不存在或解析失败，尝试从 content-length 获取
      if (!fileSize) {
        const contentLength = response.headers['content-length']
        if (contentLength && !Array.isArray(contentLength)) {
          fileSize = Number.parseInt(contentLength)
        }
      }

      // 确保响应流被正确处理
      response.on('data', () => { })
      response.on('end', () => { })

      return fileSize
    }
    catch (error) {
      throw new Error(`获取文件大小失败：${error}`)
    }
  }
}

export default FileDownloader
