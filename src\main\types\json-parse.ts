/**
 * 解析后的JSON数据接口定义
 */
export interface ParsedJsonData {
  direct?: boolean
  url?: string
  formats?: Array<{
    acodec?: string
    vcodec?: string
    resolution?: string
    language?: string
    url: string
    width?: number
    height?: number
    fps?: number
    filesize?: number
    filesize_approx?: number
    http_headers?: Record<string, string>
    format_id?: string
    abr?: number
    cookies?: string
  }>
  http_headers?: Record<string, string>
  cookies?: string
}

/**
 * 音频格式接口定义
 */
export interface AudioFormat {
  vcodec?: string
  abr?: number
  acodec?: string
  language?: string
  resolution?: string
  url: string
  http_headers?: Record<string, string>
  format_id?: string
}

/**
 * 视频格式接口定义
 */
export interface VideoFormat extends AudioFormat {
  vcodec: string
  height?: number
  width?: number
  fps?: number
  tbr?: number
  asr?: number
  filesize?: number
  dynamic_range?: string
  color_space?: string
  color_primaries?: string
  color_transfer?: string
  pixel_format?: string
}

/**
 * URL项接口定义
 */
export interface UrlItem {
  url: string
  headers?: Record<string, string>
  info?: Record<string, string>
}
