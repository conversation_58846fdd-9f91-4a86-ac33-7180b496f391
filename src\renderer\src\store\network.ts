import type { MediaResource } from '@main/types'
import { client, handlers } from '@/client'
import { create } from 'zustand'

export type SnifferResource = MediaResource & Partial<{ isDownloaded: boolean, title: string }>

interface NetworkStore {
  /** 搜索内容 */
  search: string
  /** 是否是首页 */
  isHome: boolean
  /** webview访问网址 */
  url: string
  /** 网站标题 */
  title: string
  /** 是否加载中 */
  loading: boolean
  /** 是否是DOMReady */
  isDomReady: boolean
  /** 网址列表 */
  siteList: { url: string, title: string, mainDomain: string }[]
  /** 是否显示嗅探按钮 */
  isSniffingBtn: boolean
  /** 是否显示嗅探抽屉 */
  isSniffingDrawer: boolean
  /** 资源嗅探列表 */
  resourceSnifferList: SnifferResource[]
  /** 资源嗅探监听 */
  unlisten: (() => void) | undefined
  /** 窗口打开监听 */
  unlistenWindowOpen: (() => void) | undefined
  setSearch: (search: string) => void
  setIsHome: (isHome: boolean) => void
  setTitle: (title: string) => void
  setUrl: (url: string) => void
  setLoading: (loading: boolean) => void
  setIsDomReady: (isDomReady: boolean) => void
  setIsSniffingBtn: (isSniffingBtn: boolean) => void
  setIsSniffingDrawer: (isSniffingDrawer: boolean) => void
  /** 获取网址列表 */
  getSiteList: () => Promise<void>
  /** 添加网址 */
  addSite: (url: string, title?: string) => Promise<void>
  /** 删除网址 */
  deleteSite: (url: string) => Promise<void>
  /** 初始化资源嗅探 */
  initResourceSniffer: (id: number) => Promise<void>
  /** 资源嗅探监听 */
  listenResourceSniffer: () => void
  /** 清除资源嗅探监听 */
  clearListenResourceSniffer: () => void
  /** 窗口打开监听 */
  listenWindowOpen: () => void
  /** 清除窗口打开监听 */
  clearListenWindowOpen: () => void
  /** 下载资源 */
  downloadResource: (resource: SnifferResource) => Promise<void>
  /** 清除嗅探资源 */
  clearSniffedResource: () => Promise<void>
}

export const useNetworkStore = create<NetworkStore>((set, get) => ({
  search: '',
  isHome: true,
  url: '',
  title: '',
  loading: false,
  isDomReady: false,
  siteList: [],
  isSniffingBtn: true,
  isSniffingDrawer: false,
  resourceSnifferList: [],
  unlisten: undefined,
  unlistenWindowOpen: undefined,
  setSearch(search: string) {
    set(() => ({ search }))
  },
  setIsHome(isHome: boolean) {
    set(() => ({ isHome }))
  },
  setTitle(title: string) {
    set(() => ({ title }))
  },
  setUrl(url: string) {
    set(() => ({ url }))
  },
  setLoading(loading: boolean) {
    set(() => ({ loading }))
  },
  setIsDomReady(isDomReady: boolean) {
    set(() => ({ isDomReady }))
  },
  setIsSniffingBtn(isSniffingBtn: boolean) {
    set(() => ({ isSniffingBtn }))
  },
  setIsSniffingDrawer(isSniffingDrawer: boolean) {
    set(() => ({ isSniffingDrawer }))
  },
  async getSiteList() {
    const data = await client.getUrlBookmarks()
    set(() => ({ siteList: data }))
  },
  async addSite(url: string, title?: string) {
    await client.addUrlBookmark({ url, title })
    get().getSiteList()
  },
  async deleteSite(url: string) {
    await client.deleteUrlBookmark({ url })
    get().getSiteList()
  },
  async initResourceSniffer(id: number) {
    await client.setupResourceSniffer({ id })
  },
  listenResourceSniffer() {
    if (get().unlisten) {
      return
    }
    const unlistenFunc = handlers.onResourceSniffed.listen((data) => {
      const flag = get().resourceSnifferList.some(item => item.url === data.url)
      if (!flag) {
        set(() => ({ resourceSnifferList: [{ ...data, title: get().title }, ...get().resourceSnifferList] }))
      }
    })
    set(() => ({ unlisten: unlistenFunc }))
  },
  clearListenResourceSniffer() {
    if (get().unlisten) {
      get().unlisten?.()
      set(() => ({ unlisten: undefined }))
    }
  },
  listenWindowOpen() {
    if (get().unlistenWindowOpen) {
      return
    }
    const unlistenFunc = handlers.onWindowOpenIntercept.listen((url) => {
      console.log('listenWindowOpen', url)
      if (url === get().url) {
        set(() => ({ url: '' }))
        setTimeout(() => {
          set(() => ({ url }))
        }, 10)
      }
      else {
        set(() => ({ url }))
      }
    })
    set(() => ({ unlistenWindowOpen: unlistenFunc }))
  },
  clearListenWindowOpen() {
    if (get().unlistenWindowOpen) {
      get().unlistenWindowOpen?.()
      set(() => ({ unlistenWindowOpen: undefined }))
    }
  },
  async downloadResource(data: SnifferResource) {
    const result = await client.downloadSniffedResources({ snifferMediaDownloadInfo: { url: data.url, title: data.title, fileExt: data.fileExt, httpHeaders: data.requestHeaders } })
    if (result.success) {
      const newData = get().resourceSnifferList.map(item => item.id !== data.id ? item : { ...item, isDownloaded: true })
      set(() => ({ resourceSnifferList: newData }))
    }
  },
  async clearSniffedResource() {
    set(() => ({ resourceSnifferList: [] }))
  },
}))
