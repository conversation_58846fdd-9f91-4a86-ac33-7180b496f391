import type {
  AudioFormat,
  AudioTrack,
  Bitrate,
  DownloadConfigStore,
  Quality,
  Subtitle,
  VideoFormat,
} from '@/store/download'
import type { Platform } from '@/utils/environment'
import type { SettingStoreType } from '@main/types/setting'

export function validatePort(port: string) {
  return Number(port) > 0 && Number(port) < 65535
}

export function validateHost(host: string) {
  const hostPattern
    = /^(?:(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}|(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}|xn--[a-zA-Z0-9]+)$/
  return hostPattern.test(host)
}

export function mapDownloadConfigToSettings(
  downloadConfig: DownloadConfigStore,
  settings: SettingStoreType,
): SettingStoreType {
  const {
    downloadType,
    audioFormat,
    audioTracks,
    bitrate,
    isDownloadThumbnail,
    resolution,
    subtitles,
    videoFormat,
    platform,
  } = downloadConfig

  const settingDto: Partial<SettingStoreType> = {
    downloadType,
    isDownloadThumbnail,
    audioTracks,
  }

  if (downloadType === 'video') {
    settingDto.subtitles = subtitles
    settingDto.videoConfig = {
      resolution,
      format: { format: videoFormat, platform },
    }
  }
  else {
    settingDto.audioConfig = {
      bitrate,
      format: { format: audioFormat, platform },
    }
  }

  return { ...settings, ...settingDto }
}

export function mapSettingsToDownloadConfig(settings: SettingStoreType) {
  const {
    downloadType,
    videoConfig,
    audioConfig,
    isDownloadThumbnail,
    subtitles,
    audioTracks,
  } = settings as Partial<SettingStoreType>

  const downloadConfig: Partial<DownloadConfigStore> = {}

  if (downloadType) {
    downloadConfig.downloadType = downloadType
  }

  if (isDownloadThumbnail !== undefined) {
    downloadConfig.isDownloadThumbnail = isDownloadThumbnail
  }

  if (audioTracks) {
    downloadConfig.audioTracks = audioTracks as AudioTrack[]
  }

  if (downloadType === 'audio') {
    if (audioConfig?.format?.format) {
      downloadConfig.audioFormat = audioConfig?.format.format as AudioFormat
    }
    if (audioConfig?.format?.platform) {
      downloadConfig.platform = audioConfig?.format.platform as Platform
    }
    if (audioConfig?.bitrate) {
      downloadConfig.bitrate = audioConfig.bitrate as Bitrate
    }
  }
  else {
    if (videoConfig?.format?.format) {
      downloadConfig.videoFormat = videoConfig?.format.format as VideoFormat
    }
    if (videoConfig?.format?.platform) {
      downloadConfig.platform = videoConfig?.format.platform as Platform
    }
    if (videoConfig?.resolution) {
      downloadConfig.resolution = videoConfig?.resolution as Quality
    }
    if (subtitles) {
      downloadConfig.subtitles = subtitles as Subtitle[]
    }
  }

  return downloadConfig
}
