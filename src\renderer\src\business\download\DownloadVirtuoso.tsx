import type { TaskType } from '@/store/task'
import type { SelectTask } from '@main/lib/db/schema'
import type { DownloadProgressData } from '@main/types/download'
import type { AuthSite } from '@main/types/setting'
import type { Ref } from 'react'
import type { ItemProps, VirtuosoHandle } from 'react-virtuoso'
import DownloadTaskEmptySvgIcon from '@/assets/download_empty.svg?react'
import { client } from '@/client'
import { Notice } from '@/components/Notice'
import { useSettings } from '@/contexts/SettingsContext'
import { useAuthSite } from '@/hooks/auth-site'
import { useTaskStore } from '@/store/task'
import { forwardRef, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { Virtuoso } from 'react-virtuoso'
import TaskItem from './TaskItem'

function Item({ item, ...props }: ItemProps<TaskType>) {
  return <div {...props} className="mx-4 first:mt-4 last:pb-4" />
}

function DownloadVirtuoso(_: any, ref: Ref<VirtuosoHandle>) {
  const { t } = useTranslation()
  const { openSettings } = useSettings()
  const {
    tasks,
    deleteTask,
    openTaskFolder,
    retryTask,
  } = useTaskStore()
  const { addCustomSite, loginSite } = useAuthSite()

  const handleAuth = async (task: SelectTask & DownloadProgressData) => {
    try {
      const authUrl = await addCustomSite(task.url)
      if (authUrl) {
        await loginSite({ authUrl } as AuthSite)
      }
    }
    catch {
      Notice.error(t('messages.authFailed'))
    }
  }

  const List = useMemo(() => forwardRef<HTMLDivElement>((props, ref) => (
    <div {...props} ref={ref} className="space-y-3" />
  )), [])

  if (tasks.length === 0) {
    return (
      <div className="flex flex-col justify-center items-center h-full">
        <DownloadTaskEmptySvgIcon className="scale-75" />
        <section className="text-[#6B7280]">
          <p>{t('download.emptyState.step1')}</p>
          <p>{t('download.emptyState.step2')}</p>
        </section>
      </div>
    )
  }

  return (
    <Virtuoso
      ref={ref}
      className="grow"
      totalCount={tasks.length}
      data={tasks}
      components={{ List, Item }}
      itemContent={(_, item) => {
        return (
          <TaskItem
            key={item.id}
            task={item}
            onDelete={deleteTask}
            onOpen={openTaskFolder}
            onRetry={retryTask}
            onAuth={handleAuth}
            onOpenSettings={openSettings}
          />
        )
      }}
    />
  )
}

export default forwardRef(DownloadVirtuoso)
