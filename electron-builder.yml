appId: com.snapany.desktop
productName: SnapAny
directories:
  output: release
  buildResources: public
files:
  - out/**/*
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
  - from: public/bin
    to: public/bin
    filter:
      - ffmpeg
      - ffprobe
      - yt-dlp
      - snapfile
      - '!node_modules/better-sqlite3/deps/**'
      - '!node_modules/better-sqlite3/src/**'
      - '!node_modules/better-sqlite3/build/Release/*.pdb'
      - build/better_sqlite3.node
  - from: drizzle
    to: drizzle
    filter:
      - '**/*'
      - '!**/data.db'
afterSign: scripts/notarize.js
win:
  target:
    - target: nsis
      arch:
        - x64
  icon: public/icon.ico
  publisherName: GYS
  certificateSha1: c8b442899b202fb7360a9bbe739ea7e5c9c92eec
  signAndEditExecutable: true
  rfc3161TimeStampServer: http://timestamp.comodoca.com
  timeStampServer: http://timestamp.comodoca.com
  files:
    - from: public/bin
      to: public/bin
      filter:
        - ffmpeg.exe
        - ffprobe.exe
        - yt-dlp.exe
        - snapfile.exe
  asarUnpack:
    - public/bin/ffmpeg.exe
    - public/bin/ffprobe.exe
    - public/bin/yt-dlp.exe
    - public/bin/snapfile.exe
    - drizzle
mac:
  target:
    - target: dmg
  icon: public/icon.icns
  hardenedRuntime: true
  gatekeeperAssess: false
  entitlements: extra/entitlements.mac.plist
  entitlementsInherit: extra/entitlements.mac.plist
  cscLink: ./extra/certificates/证书.p12
  cscKeyPassword: gys-tech
  category: public.app-category.utilities
  extendInfo:
    CFBundleIdentifier: com.snapany.desktop
  identity: A0A4EFE8A6A06D52FE631E901F7F4B3C0A1FFA03
  files:
    - from: public/bin
      to: public/bin
      filter:
        - ffmpeg
        - ffprobe
        - yt-dlp
        - snapfile
  asarUnpack:
    - public/bin/ffmpeg
    - public/bin/ffprobe
    - public/bin/yt-dlp
    - public/bin/snapfile
    - drizzle
  binaries:
    - Contents/Resources/app.asar.unpacked/public/bin/ffmpeg
    - Contents/Resources/app.asar.unpacked/public/bin/ffprobe
    - Contents/Resources/app.asar.unpacked/public/bin/yt-dlp
    - Contents/Resources/app.asar.unpacked/public/bin/snapfile
  darkModeSupport: true
  artifactName: SnapAny_${version}_${arch}.dmg
nsis:
  oneClick: false
  perMachine: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: SnapAny
  installerIcon: public/icon.ico
  uninstallerIcon: public/icon.ico
  artifactName: SnapAny_${version}_${arch}.${ext}
  deleteAppDataOnUninstall: true
compression: maximum
asar: true
dmg:
  sign: true
publish: null
extraResources:
  - from: src/common/locales
    to: locales
  - from: node_modules/better-sqlite3/build/Release/
    to: better-sqlite3
    filter:
      - better_sqlite3.node
asarUnpack:
  - public/bin/*
  - node_modules/better-sqlite3/*
  - node_modules/sqlite3/*
