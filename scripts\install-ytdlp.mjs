import { execSync } from 'node:child_process'
import fs from 'node:fs'
import https from 'node:https'
import path from 'node:path'
import process from 'node:process'

import { __dirname, downloadFileHelper } from './helper.mjs'

// GitHub API URL 获取最新发布版本信息
const GITHUB_API_URL
  = 'https://api.github.com/repos/yt-dlp/yt-dlp/releases/latest'

// 获取平台配置
function getPlatformConfig() {
  const platform = process.platform
  // const arch = process.arch;

  if (platform === 'win32') {
    return {
      binaryName: 'yt-dlp.exe',
      assetName: 'yt-dlp.exe',
    }
  }
  else if (platform === 'darwin') {
    return {
      binaryName: 'yt-dlp',
      assetName: 'yt-dlp_macos',
    }
  }
  else {
    throw new Error(`不支持的平台: ${platform}`)
  }
}

// 检查文件是否存在且可执行
function checkBinary(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return false
    }

    // 在 Windows 上不检查执行权限
    if (process.platform !== 'win32') {
      try {
        fs.accessSync(filePath, fs.constants.X_OK)
      }
      catch {
        return false
      }
    }

    // 检查文件大小是否大于 1MB (1048576 bytes)
    const stats = fs.statSync(filePath)
    if (stats.size < 1048576) {
      return false
    }

    return true
  }
  catch {
    return false
  }
}

// 获取最新发布版本的下载 URL
function getLatestReleaseUrl(assetName) {
  return new Promise((resolve, reject) => {
    const options = {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/vnd.github.v3+json',
      },
    }

    https
      .get(GITHUB_API_URL, options, (response) => {
        let data = ''

        response.on('data', (chunk) => {
          data += chunk
        })

        response.on('end', () => {
          try {
            if (response.statusCode === 200) {
              const releaseInfo = JSON.parse(data)
              const asset = releaseInfo.assets.find(
                asset => asset.name === assetName,
              )
              if (asset) {
                resolve(asset.browser_download_url)
              }
              else {
                // 如果找不到资产，使用直接下载链接
                const directUrl = `https://github.com/yt-dlp/yt-dlp/releases/latest/download/${assetName}`
                resolve(directUrl)
              }
            }
            else {
              // 如果 API 请求失败，使用直接下载链接
              const directUrl = `https://github.com/yt-dlp/yt-dlp/releases/latest/download/${assetName}`
              resolve(directUrl)
            }
          }
          catch (error) {
            reject(new Error(`解析发布信息失败: ${error.message}`))
          }
        })
      })
      .on('error', () => {
        // 如果 API 请求出错，使用直接下载链接
        const directUrl = `https://github.com/yt-dlp/yt-dlp/releases/latest/download/${assetName}`
        resolve(directUrl)
      })
  })
}

async function main() {
  let destPath
  try {
    console.log('📦 开始安装 yt-dlp')
    const config = getPlatformConfig()

    const publicDir = path.join(__dirname, '..', 'public')
    const binDir = path.join(publicDir, 'bin')
    destPath = path.join(binDir, config.binaryName)

    // 检查文件是否已存在且可用
    if (checkBinary(destPath)) {
      console.log('✅ yt-dlp 已安装且可用')
      process.exit(0)
      return
    }

    // 创建目录
    if (!fs.existsSync(binDir)) {
      fs.mkdirSync(binDir, { recursive: true })
    }

    // 获取并下载最新版本
    console.log('⏬ 正在下载 yt-dlp...')
    const downloadUrl = await getLatestReleaseUrl(config.assetName)
    await downloadFileHelper(downloadUrl, destPath)

    // 设置执行权限
    if (process.platform !== 'win32') {
      execSync(`chmod +x "${destPath}"`)
    }

    // 验证安装
    if (!checkBinary(destPath)) {
      throw new Error('下载的文件无效或不完整')
    }
    // 安装成功显示文件大小
    console.log(
      '✅ yt-dlp 安装成功',
      `${(fs.statSync(destPath).size / 1024 / 1024).toFixed(2)} MB`,
    )
    process.exit(0)
  }
  catch (error) {
    console.error('❌ 安装失败:', error.message)
    // 清理下载的文件
    if (destPath && fs.existsSync(destPath)) {
      fs.unlinkSync(destPath)
    }
    process.exit(1)
  }
}

main()
