{
  "compilerOptions": {
    "target": "ES2020",
    "jsx": "react-jsx",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "useDefineForClassFields": true,
    "baseUrl": ".",
    "module": "ESNext",
    /* Bundler mode */
    "moduleResolution": "bundler",
    "paths": {
      "@/*": ["src/renderer/src/*"],
      "@renderer/*": ["src/renderer/src/*"],
      "@preload/*": ["src/preload/*"],
      "@common/*": ["src/common/*"],
      "@main/*": ["src/main/*"]
    },
    "resolveJsonModule": true,
    "typeRoots": ["./node_modules/@types", "./src/preload", "./src/main/types"],
    "allowImportingTsExtensions": true,
    /* Linting */
    "strict": true,
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noEmit": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ],
  "include": ["src/renderer/src", "src/common", "src/main", "src/preload"]
}
