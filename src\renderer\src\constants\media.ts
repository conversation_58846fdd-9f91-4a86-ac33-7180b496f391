import type { FormatPlatformMap } from '@/types/setting'

// 字幕语言
export const SUBTITLE_LANGUAGES = [
  // 英语 (1397百万)
  { value: 'en', label: 'English' },
  { value: 'en-GB', label: 'English(GB)' },
  { value: 'en-US', label: 'English(US)' },
  // 中文 (1159.2百万，简体+繁体)
  { value: 'zh', label: '中文' },
  { value: 'zh-CN', label: '中文(中国)' },
  { value: 'zh-HK', label: '中文(香港粵語)' },
  { value: 'zh-Hans', label: '中文(简体)' },
  { value: 'zh-Hant', label: '中文(繁体字)' },
  { value: 'zh-SG', label: '中文(新加坡)' },
  { value: 'zh-TW', label: '中文(薹灣話)' },
  // 粤语 (约8百万)
  { value: 'yue', label: '廣東話' },
  // 印地语 (342百万)
  { value: 'hi', label: 'हिन्दी' },
  // 西班牙语 (441.6百万)
  { value: 'es', label: 'Español' },
  { value: 'es-419', label: 'Español(Latinoamérica)' },
  { value: 'es-MX', label: 'Español(México)' },
  { value: 'es-US', label: 'Español(Estados Unidos)' },
  // 法语 (154.56百万)
  { value: 'fr', label: 'Français' },
  { value: 'fr-BE', label: 'Français(Belgique)' },
  { value: 'fr-CA', label: 'Français(Canada)' },
  // 俄语 (110百万)
  { value: 'ru', label: 'Русский' },
  // 印尼语 (98.9百万)
  { value: 'id', label: 'Bahasa Indonesia' },
  // 孟加拉语 (77.7百万)
  { value: 'bn', label: 'বাংলা' },
  // 葡萄牙语 (250百万)
  { value: 'pt', label: 'Português' },
  { value: 'pt-BR', label: 'Português(Brasil)' },
  // 德语 (121.44百万)
  { value: 'de', label: 'Deutsch' },
  { value: 'de-AT', label: 'Deutsch(Österreich)' },
  { value: 'de-CH', label: 'Deutsch(Schweiz)' },
  { value: 'de-DE', label: 'Deutsch(Deutschland)' },
  // 日语 (114.9百万)
  { value: 'ja', label: '日本語' },
  // 韩语 (48百万)
  { value: 'ko', label: '한국어' },
  // 越南语 (79.8百万)
  { value: 'vi', label: 'Tiếng Việt' },
  // 土耳其语 (77.3百万)
  { value: 'tr', label: 'Türkçe' },
  // 意大利语 (52百万)
  { value: 'it', label: 'Italiano' },
  // 阿拉伯语 (168.1百万)
  { value: 'ar', label: 'العربية' },
  // 波兰语 (约44百万)
  { value: 'pl', label: 'Język polski' },
  // 荷兰语 (约29百万)
  { value: 'nl', label: 'Nederlands' },
  // 泰语 (约27百万)
  { value: 'th', label: 'ไทย' },
  // 波斯语 (约25百万)
  { value: 'fa', label: 'فارسی' },
  // 乌克兰语 (约24百万)
  { value: 'uk', label: 'Українська мова' },
  // 罗马尼亚语 (约17百万)
  { value: 'ro', label: 'Limba română' },
  // 马来语 (约15百万)
  { value: 'ms', label: 'بهاس ملايو' },
  // 乌尔都语 (约14百万)
  { value: 'ur', label: 'اردو' },
  // 泰米尔语 (约12百万)
  { value: 'ta', label: 'தமிழ்' },
  // 希伯来语 (约9百万)
  { value: 'he', label: 'עברית' },
  // 马拉地语 (约7百万)
  { value: 'mr', label: 'मराठी' },
  // 菲律宾语 (约6百万)
  { value: 'fil', label: 'Wikang Filipino' },
  // 匈牙利语 (约6百万)
  { value: 'hu', label: 'Magyar' },
  // 希腊语 (约5百万)
  { value: 'el', label: 'Ελληνικά' },
  // 捷克语 (约5百万)
  { value: 'cs', label: 'Čeština' },
  // 瑞典语 (约5百万)
  { value: 'sv', label: 'Svenska' },
  // 丹麦语 (约4百万)
  { value: 'da', label: 'dansk' },
  // 芬兰语 (约4百万)
  { value: 'fi', label: 'Suomi' },
  // 斯洛伐克语 (约3百万)
  { value: 'sk', label: 'Slovenčina' },
  // 挪威语 (约3百万)
  { value: 'no', label: 'Norsk' },
  // 克罗地亚语 (约2百万)
  { value: 'hr', label: 'Hrvatski jezik' },
  // 塞尔维亚语 (约2百万)
  { value: 'sr', label: 'Српски језик' },
  { value: 'sr-Latn', label: 'Srpski' },
  // 保加利亚语 (约2百万)
  { value: 'bg', label: 'Български език' },
  // 立陶宛语 (约1百万)
  { value: 'lt', label: 'Lietuvių kalba' },
  // 斯洛文尼亚语 (约1百万)
  { value: 'sl', label: 'Slovenščina' },
  // 爱沙尼亚语 (约0.5百万)
  { value: 'et', label: 'Eesti keel' },
  // 拉脱维亚语 (约0.5百万)
  { value: 'lv', label: 'Latviešu valoda' },
  // 以下语言无明确数据，按原始顺序排列
  { value: 'af', label: 'Afrikaans' },
  { value: 'be', label: 'Беларуская мова' },
  { value: 'ca', label: 'català' },
  { value: 'fo', label: 'Føroyskt' },
  { value: 'gl', label: 'Gaelgo' },
  { value: 'gu', label: 'ગુજરાતી' },
  { value: 'hy', label: 'Հայերեն' },
  { value: 'is', label: 'íslenska' },
  { value: 'km', label: 'ភាសាខ្មែរ' },
  { value: 'kn', label: 'ಕನ್ನಡ' },
  { value: 'lo', label: 'ພາສາລາວ' },
  { value: 'mk', label: 'Македонски јазик' },
  { value: 'ml', label: 'മലയാളം' },
  { value: 'mn', label: 'Монгол хэл' },
  { value: 'mt', label: 'Malti' },
  { value: 'my', label: 'ဗမာစာ' },
  { value: 'ne', label: 'नेपाली' },
  { value: 'sd', label: 'सिधी' },
  { value: 'si', label: 'සිංහල' },
  { value: 'sq', label: 'Gjuha shqipe' },
  { value: 'uz', label: 'Ўзбек' },
  { value: 'yi', label: 'יידיש' },
  { value: 'zu', label: 'isiZulu' },
] as const

// 音轨语言
export const AUDIO_TRACK_LANGUAGES = [
  { value: 'en', label: 'English' },
  { value: 'zh', label: '中文' },
  { value: 'es', label: 'Español' },
  { value: 'fr', label: 'Français' },
  { value: 'de', label: 'Deutsch' },
  { value: 'ja', label: '日本語' },
  { value: 'ko', label: '한국어' },
  { value: 'ru', label: 'Русский' },
  { value: 'pt', label: 'Português' },
  { value: 'it', label: 'Italiano' },
  { value: 'nl', label: 'Nederlands' },
  { value: 'tr', label: 'Türkçe' },
  { value: 'pl', label: 'Polski' },
  { value: 'sv', label: 'Svenska' },
  { value: 'fi', label: 'Suomi' },
  { value: 'da', label: 'Dansk' },
  { value: 'no', label: 'Norsk' },
  { value: 'el', label: 'Ελληνικά' },
  { value: 'cs', label: 'Čeština' },
  { value: 'hu', label: 'Magyar' },
  { value: 'uk', label: 'Українská' },
  { value: 'ro', label: 'Română' },
  { value: 'ar', label: 'العربية' },
  { value: 'hi', label: 'हिन्दी' },
  { value: 'th', label: 'ไทย' },
  { value: 'vi', label: 'Tiếng Việt' },
  { value: 'id', label: 'Bahasa Indonesia' },
  { value: 'ms', label: 'Bahasa Melayu' },
  { value: 'fil', label: 'Filipino' },
  { value: 'he', label: 'עברית' },
  { value: 'fa', label: 'فارسی' },
  { value: 'bn', label: 'বাংলা' },
  { value: 'ta', label: 'தமிழ்' },
  { value: 'te', label: 'తెలుగు' },
  { value: 'ur', label: 'اردو' },
] as const

// 字幕语言
export const FILE_RENAME_LANGUAGES = [
  // 英语 (1397百万)
  { value: 'en', label: 'English' },
  { value: 'en-GB', label: 'English(GB)' },
  { value: 'en-US', label: 'English(US)' },
  // 中文 (1159.2百万，简体+繁体)
  { value: 'zh', label: '中文' },
  { value: 'zh-CN', label: '中文(中国)' },
  { value: 'zh-HK', label: '中文(香港粵語)' },
  { value: 'zh-Hans', label: '中文(简体)' },
  { value: 'zh-Hant', label: '中文(繁体字)' },
  { value: 'zh-SG', label: '中文(新加坡)' },
  { value: 'zh-TW', label: '中文(薹灣話)' },
  // 粤语 (约8百万)
  { value: 'yue', label: '廣東話' },
  // 印地语 (342百万)
  { value: 'hi', label: 'हिन्दी' },
  // 西班牙语 (441.6百万)
  { value: 'es', label: 'Español' },
  { value: 'es-419', label: 'Español(Latinoamérica)' },
  { value: 'es-MX', label: 'Español(México)' },
  { value: 'es-US', label: 'Español(Estados Unidos)' },
  // 法语 (154.56百万)
  { value: 'fr', label: 'Français' },
  { value: 'fr-BE', label: 'Français(Belgique)' },
  { value: 'fr-CA', label: 'Français(Canada)' },
  // 俄语 (110百万)
  { value: 'ru', label: 'Русский' },
  // 印尼语 (98.9百万)
  { value: 'id', label: 'Bahasa Indonesia' },
  // 孟加拉语 (77.7百万)
  { value: 'bn', label: 'বাংলা' },
  // 葡萄牙语 (250百万)
  { value: 'pt', label: 'Português' },
  { value: 'pt-BR', label: 'Português(Brasil)' },
  // 德语 (121.44百万)
  { value: 'de', label: 'Deutsch' },
  { value: 'de-AT', label: 'Deutsch(Österreich)' },
  { value: 'de-CH', label: 'Deutsch(Schweiz)' },
  { value: 'de-DE', label: 'Deutsch(Deutschland)' },
  // 日语 (114.9百万)
  { value: 'ja', label: '日本語' },
  // 韩语 (48百万)
  { value: 'ko', label: '한국어' },
  // 越南语 (79.8百万)
  { value: 'vi', label: 'Tiếng Việt' },
  // 土耳其语 (77.3百万)
  { value: 'tr', label: 'Türkçe' },
  // 意大利语 (52百万)
  { value: 'it', label: 'Italiano' },
  // 阿拉伯语 (168.1百万)
  { value: 'ar', label: 'العربية' },
  // 波兰语 (约44百万)
  { value: 'pl', label: 'Język polski' },
  // 荷兰语 (约29百万)
  { value: 'nl', label: 'Nederlands' },
  // 泰语 (约27百万)
  { value: 'th', label: 'ไทย' },
  // 波斯语 (约25百万)
  { value: 'fa', label: 'فارسی' },
  // 乌克兰语 (约24百万)
  { value: 'uk', label: 'Українська мова' },
  // 罗马尼亚语 (约17百万)
  { value: 'ro', label: 'Limba română' },
  // 马来语 (约15百万)
  { value: 'ms', label: 'بهاس ملايو' },
  // 乌尔都语 (约14百万)
  { value: 'ur', label: 'اردو' },
  // 泰米尔语 (约12百万)
  { value: 'ta', label: 'தமிழ்' },
  // 希伯来语 (约9百万)
  { value: 'he', label: 'עברית' },
  // 马拉地语 (约7百万)
  { value: 'mr', label: 'मराठी' },
  // 菲律宾语 (约6百万)
  { value: 'fil', label: 'Wikang Filipino' },
  // 匈牙利语 (约6百万)
  { value: 'hu', label: 'Magyar' },
  // 希腊语 (约5百万)
  { value: 'el', label: 'Ελληνικά' },
  // 捷克语 (约5百万)
  { value: 'cs', label: 'Čeština' },
  // 瑞典语 (约5百万)
  { value: 'sv', label: 'Svenska' },
  // 丹麦语 (约4百万)
  { value: 'da', label: 'dansk' },
  // 芬兰语 (约4百万)
  { value: 'fi', label: 'Suomi' },
  // 斯洛伐克语 (约3百万)
  { value: 'sk', label: 'Slovenčina' },
  // 挪威语 (约3百万)
  { value: 'no', label: 'Norsk' },
  // 克罗地亚语 (约2百万)
  { value: 'hr', label: 'Hrvatski jezik' },
  // 塞尔维亚语 (约2百万)
  { value: 'sr', label: 'Српски језик' },
  { value: 'sr-Latn', label: 'Srpski' },
  // 保加利亚语 (约2百万)
  { value: 'bg', label: 'Български език' },
  // 立陶宛语 (约1百万)
  { value: 'lt', label: 'Lietuvių kalba' },
  // 斯洛文尼亚语 (约1百万)
  { value: 'sl', label: 'Slovenščina' },
  // 爱沙尼亚语 (约0.5百万)
  { value: 'et', label: 'Eesti keel' },
  // 拉脱维亚语 (约0.5百万)
  { value: 'lv', label: 'Latviešu valoda' },
  // 以下语言无明确数据，按原始顺序排列
  { value: 'af', label: 'Afrikaans' },
  { value: 'be', label: 'Беларуская мова' },
  { value: 'ca', label: 'català' },
  { value: 'da', label: 'Dansk' },
  { value: 'fo', label: 'Føroyskt' },
  { value: 'gl', label: 'Gaelgo' },
  { value: 'gu', label: 'ગુજરાતી' },
  { value: 'hy', label: 'Հայերեն' },
  { value: 'is', label: 'íslenska' },
  { value: 'km', label: 'ភាសាខ្មែរ' },
  { value: 'kn', label: 'ಕನ್ನಡ' },
  { value: 'lo', label: 'ພາສາລາວ' },
  { value: 'mk', label: 'Македонски јазик' },
  { value: 'ml', label: 'മലയാളം' },
  { value: 'mn', label: 'Монгол хэл' },
  { value: 'mt', label: 'Malti' },
  { value: 'my', label: 'ဗမာစာ' },
  { value: 'ne', label: 'नेपाली' },
  { value: 'pl', label: 'Polski' },
  { value: 'ro', label: 'Română' },
  { value: 'sd', label: 'सिधी' },
  { value: 'si', label: 'සිංහල' },
  { value: 'sq', label: 'Gjuha shqipe' },
  { value: 'te', label: 'తెలుగు' },
  { value: 'uk', label: 'Українská' },
  { value: 'uz', label: 'Ўзбек' },
  { value: 'yi', label: 'יידיש' },
  { value: 'zu', label: 'isiZulu' },
] as const

// 默认分辨率
export const DEFAULT_QUALITIES = [
  { label: '8K(4320p)', value: 4320 },
  { label: '4K(2160p)', value: 2160 },
  { label: '2K(1440p)', value: 1440 },
  { label: '1080p', value: 1080 },
  { label: '720p', value: 720 },
  { label: '480p', value: 480 },
  { label: '360p', value: 360 },
  { label: '240p', value: 240 },
] as const

// 默认比特率
export const DEFAULT_BITRATE = [
  { label: '320kbps', value: 320 },
  { label: '256kbps', value: 256 },
  { label: '192kbps', value: 192 },
  { label: '128kbps', value: 128 },
  { label: '64kbps', value: 64 },
] as const

export const VIDEO_FORMATS = [
  { label: 'MP4', value: 'mp4' },
  { label: 'MKV', value: 'mkv' },
] as const

export const AUDIO_FORMATS = [
  { label: 'MP3', value: 'mp3' },
  { label: 'M4A', value: 'm4a' },
  { label: 'OGG', value: 'ogg' },
] as const

export const PLATFORMS = [
  { label: 'Windows', value: 'windows' },
  { label: 'macOS', value: 'macos' },
  { label: 'Linux', value: 'linux' },
  { label: 'iOS', value: 'ios' },
  { label: 'Android', value: 'android' },
] as const

// 添加格式映射关系
export const FORMAT_PLATFORM_MAP: FormatPlatformMap = {
  video: {
    windows: 'mp4',
    macos: 'mp4',
    linux: 'mkv',
    ios: 'mp4',
    android: 'mp4',
  },
  audio: {
    windows: 'mp3',
    macos: 'm4a',
    linux: 'ogg',
    ios: 'm4a',
    android: 'mp3',
  },
}
