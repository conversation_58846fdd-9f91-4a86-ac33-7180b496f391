import type { PathComboBoxRef } from '@renderer/components/file/PathComboBox'
import type { ChangeEvent } from 'react'
import { client } from '@/client'
import { Notice } from '@/components/Notice'
import { SUPPORTED_LANGUAGES } from '@/constants/language'
import { useSnapany } from '@/hooks/snapany'
import PathComboBox from '@renderer/components/file/PathComboBox'
import { Label, Select, ToggleSwitch } from 'flowbite-react'
import { Folder } from 'flowbite-react-icons/outline'
import { useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'

const DEFAULT_SYSTEM_VALUE = 'system'

function SettingGeneralPanel() {
  const { t, i18n } = useTranslation()

  const pathComboBoxRef = useRef<PathComboBoxRef>(null)

  // 软件语言选项
  const languageOptions = useMemo(
    () =>
      [{ value: DEFAULT_SYSTEM_VALUE, label: t('settings.system') }].concat(
        SUPPORTED_LANGUAGES,
      ),
    [t],
  )

  const { settings, patchSetting } = useSnapany()

  const languageHandle = async (e: ChangeEvent<HTMLSelectElement>) => {
    const selectValue = e.target.value
    const lang = selectValue === DEFAULT_SYSTEM_VALUE ? await client.getSystemLanguage() : selectValue
    await patchSetting({ language: selectValue })
    i18n.changeLanguage(lang)
  }

  const changeDownloadPath = async () => {
    const result = await client.selectFileDir()
    if (result.success && result.path) {
      await patchSetting({ downloadPath: result.path })
    }
  }

  const validShowPath = async () => {
    pathComboBoxRef.current?.setShowPath(settings?.downloadPath ?? '')
    Notice.error(t('errors.notImplemented'))
  }

  const openFolder = async () => {
    const path = settings?.downloadPath
    if (path) {
      await client.openFile({ filePath: path })
    }
  }

  return (
    <div>
      <div className="flex flex-col gap-y-2">
        <Label htmlFor="settingFileUpload">{t('settings.saveTo')}</Label>

        <PathComboBox
          id="settingFileUpload"
          handleValidPath={validShowPath}
          path={settings?.downloadPath}
          handleOpenFolder={openFolder}
          handleChangeDownloadPath={changeDownloadPath}
          ref={pathComboBoxRef}
          rightIcon={Folder}
          actionText={t('settings.changeFolderBrowser')}
        />
      </div>

      <div className="flex flex-col gap-y-2 py-4">
        <Label htmlFor="settingLanguage">{t('settings.language')}</Label>

        <Select
          id="settingLanguage"
          required
          defaultValue={settings?.language}
          onChange={languageHandle}
        >
          {languageOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Select>
      </div>
      <div className="flex items-center justify-between mt-2">
        <Label htmlFor="settingSubtitle">{t('settings.embedSubtitlesInVideoFile')}</Label>
        <ToggleSwitch
          id="settingSubtitle"
          color="blue"
          checked={settings?.embedSubtitle}
          onChange={() => patchSetting({ embedSubtitle: !settings?.embedSubtitle })}
        />
      </div>
    </div>
  )
}

export default SettingGeneralPanel
