import path from 'node:path'
import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    environment: 'node', // 或者 'node'，取决于你的测试类型
    include: ['src/main/**/*.test.ts'],
    setupFiles: ['./vitest.setup.ts'], // 添加设置文件
    deps: {
      inline: ['electron'], // 内联electron依赖
    },
  },
  resolve: {
    alias: {
      '@main': path.resolve(__dirname, 'src/main'),
    },
  },
})
