import path from 'node:path'
import { isDev } from '@main/constants/env'
import Database from 'better-sqlite3'
import { drizzle } from 'drizzle-orm/better-sqlite3'
import { migrate } from 'drizzle-orm/better-sqlite3/migrator'
import { app } from 'electron'

const dbPath = isDev
  ? path.join(__dirname, '../../drizzle/data.db')
  : path.join(app.getPath('userData'), 'data.db')

const migrationsPath = isDev
  ? path.join(__dirname, '../../drizzle')
  : path.join(process.resourcesPath, 'app.asar.unpacked', 'drizzle')

const sqlite = new Database(dbPath, {
  verbose: isDev ? console.log : undefined,
})

export const db = drizzle(sqlite)

export async function initDatabase(): Promise<void> {
  try {
    await migrate(db, { migrationsFolder: migrationsPath })
    console.log('数据库迁移成功', migrationsPath)
  }
  catch (error) {
    console.error('数据库迁移失败:', error)
    throw error
  }
}
