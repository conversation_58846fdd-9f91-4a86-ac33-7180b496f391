import type { ConvertFileMediaInfoItem, VideoAudioConverProgressData } from '@main/types'
import { client, handlers } from '@/client'
import { Notice } from '@/components/Notice'
import { mergeArraysByKey } from '@/utils/merge'
import { create } from 'zustand'

export type ConvertStatus = 'waiting' | 'converting' | 'completed'
export type OutputFormatType = { label: string, value: string, key: string, extra?: string[] }

interface FormatStore {
  fileData: (ConvertFileMediaInfoItem & Partial<VideoAudioConverProgressData>)[]
  repeatDataItemKey: Set<unknown>
  /** 上传格式 */
  format?: 'video' | 'audio' | 'image'
  /** 转换状态 */
  convertStatus: ConvertStatus
  /** 输出格式对象 */
  outputFormat: OutputFormatType
  /** 监听 */
  unlisten: (() => void) | undefined
  /** 设置输出格式 */
  setOutputFormat: (format: OutputFormatType) => void
  handleUploadFile: (files: File[]) => void
  /** 删除文件 */
  deleteDataItem: (filePath: string) => void
  /** 停止转换 */
  stopConvertTask: () => Promise<void>
  /** 清空数据 */
  clearAllData: () => void
  /** 开始转换 */
  startConvertTask: () => Promise<void>
  /** 监听转换 */
  listenConvert: () => void
  /** 清除监听 */
  clearListenConvert: () => void
}

export const useFormatStore = create<FormatStore>((set, get) => ({
  fileData: [],
  repeatDataItemKey: new Set(),
  format: undefined,
  convertStatus: 'waiting',
  outputFormat: undefined,
  unlisten: undefined,
  setOutputFormat(format: OutputFormatType) {
    set(() => ({ outputFormat: format }))
  },
  async handleUploadFile(files) {
    if (files && files.length > 0) {
      try {
        const filePaths: string[] = []
        // 使用预加载脚本中暴露的API
        for (const file of files) {
          const filePath = window.api.getFilePathFromFile(file)
          filePaths.push(filePath ?? '')
        }
        const result = await client.getMediaFormatConvertInfo({ filePaths, format: get().format })
        if (result?.success) {
          const { replaceSetKey, data } = mergeArraysByKey(get().fileData, result.data, 'filePath')
          set(() => ({ fileData: data, format: result.targetMediaType, repeatDataItemKey: replaceSetKey, convertStatus: 'waiting' }))
        }
      }
      catch (error) {
        console.error('上传文件失败:', error)
        Notice.error('上传文件失败')
      }
    }
    setTimeout(() => {
      set(() => ({ repeatDataItemKey: new Set() }))
    }, 2000)
  },
  deleteDataItem(filePath) {
    const newData = get().fileData.filter(item => item.filePath !== filePath)
    set(() => ({ fileData: newData }))
  },
  async stopConvertTask() {
    await client.stopConvert()
  },
  clearAllData() {
    set(() => ({ fileData: [], repeatDataItemKey: new Set(), format: undefined, convertStatus: 'waiting' }))
  },
  async startConvertTask() {
    const filePaths = get().fileData.map(item => item.filePath)
    await client.convertMediaFile({ filePaths, outputFormat: get().outputFormat.value, extraParams: get().outputFormat.extra })
    set(() => ({ convertStatus: 'converting' }))
  },
  listenConvert() {
    if (get().unlisten) {
      return
    }
    const unlistenFunc = handlers.onVideoAudioConverProgress.listen((data) => {
      const newData = get().fileData.map((item) => {
        if (item.filePath === data.id) {
          return {
            ...item,
            ...data,
          }
        }
        return item
      })
      const isCompleted = !newData.some(item => item.status === 'waiting' || item.status === 'converting')
      set(() => ({ fileData: newData, convertStatus: isCompleted ? 'completed' : get().convertStatus }))
    })
    set(() => ({ unlisten: unlistenFunc }))
  },
  clearListenConvert() {
    if (get().unlisten) {
      get().unlisten?.()
      set(() => ({ unlisten: undefined }))
    }
  },
}))
