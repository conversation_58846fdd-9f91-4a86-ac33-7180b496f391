import { execSync, spawn } from 'node:child_process'
import fs from 'node:fs'
import https from 'node:https'
import path from 'node:path'
import process from 'node:process'
import { fileURLToPath } from 'node:url'

/**
 *
 * @param {string} name
 * @param {string} command
 * @param {string[]} args
 * @param {import("child_process").SpawnOptionsWithoutStdio | undefined} options
 * @returns {{ _promise: Promise<undefined>, process: import("child_process").ChildProcess }}
 */
export function spawnPromise(name, command, args, options) {
  let p
  const _promise = new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      ...options,
      // 防止环境变量被覆盖
      env: process.env,
    })

    p = child

    child.stderr.on('data', (data) => {
      console.log(`【${name}】stderr: ${data?.slice(0, 20)}...`)
    })

    child.on('close', (code) => {
      if (code === 0) {
        resolve(undefined)
      }
      else {
        reject(new Error(`${name} exited with code ${code}`))
      }
    })

    process.on('SIGINT', () => {
      child.kill()
    })
    process.on('exit', () => {
      child.kill()
    })
    process.on('SIGTERM', () => {
      child.kill()
    })
  })

  return {
    _promise,
    process: p,
  }
}
/**
 *
 * @param {string} sourceFile
 * @param {string} targetFile
 * @param {string} name
 * @param {string} arch
 */
export function copyFileHelper(sourceFile, targetFile, name, arch) {
  // 检查源文件是否存在
  if (!fs.existsSync(sourceFile)) {
    throw new Error(`找不到 ${name} 文件`)
  }

  // 如果目标文件存在则删除
  if (fs.existsSync(targetFile)) {
    fs.unlinkSync(targetFile)
  }

  // 复制文件
  fs.copyFileSync(sourceFile, targetFile)

  // 设置执行权限
  if (process.platform === 'darwin') {
    execSync(`chmod +x "${targetFile}"`)
  }

  // 验证文件大小
  const sourceStats = fs.statSync(sourceFile)
  const targetStats = fs.statSync(targetFile)

  if (sourceStats.size !== targetStats.size) {
    throw new Error(`${name} 文件复制不完整`)
  }

  console.log(`✅ 成功复制 ${arch} 版本的 ${name}`)
  console.log(`文件大小: ${(targetStats.size / 1024 / 1024).toFixed(2)} MB`)
}

/**
 * 检查文件是否存在且有效
 * @param {string} filePath
 * @returns {boolean}
 */
export function checkBinaryHelper(filePath) {
  if (!filePath) {
    return false
  }

  try {
    if (!fs.existsSync(filePath)) {
      return false
    }

    // 检查文件大小是否大于 500KB (512000 bytes)
    const stats = fs.statSync(filePath)
    if (stats.size < 512000) {
      return false
    }

    return true
  }
  catch {
    return false
  }
}
/**
 * 下载文件
 * @param {string} url
 * @param {string} dest
 */
export function downloadFileHelper(url, dest) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(dest)

    const options = {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      },
    }

    const handleResponse = (response) => {
      if (response.statusCode === 301 || response.statusCode === 302) {
        // 处理重定向
        https
          .get(response.headers.location, options, handleResponse)
          .on('error', reject)
        return
      }

      if (response.statusCode !== 200) {
        reject(new Error(`下载失败，状态码: ${response.statusCode}`))
        return
      }

      response.pipe(file)

      file.on('finish', () => {
        file.close(() => resolve())
      })

      file.on('error', (err) => {
        fs.unlink(dest, () => reject(err))
      })
    }

    https.get(url, options, handleResponse).on('error', reject)
  })
}

export const __dirname = path.dirname(fileURLToPath(import.meta.url))
