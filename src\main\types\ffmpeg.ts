/**
 * FFmpeg 相关类型定义
 */

/**
 * FFprobe 流信息接口
 */
export interface FFprobeStream {
  index: number
  codec_type?: string
  codec_name?: string
  duration?: string
  width?: number
  height?: number
  r_frame_rate?: string
  channels?: number
  sample_rate?: number
  nb_frames?: string | number
  disposition?: {
    attached_pic?: number
  }
  tags?: {
    language?: string
  }
}

/**
 * FFprobe 数据接口
 */
export interface FFprobeData {
  streams: FFprobeStream[]
  format: {
    duration?: string | number
    [key: string]: unknown
  }
}

/**
 * 媒体流信息接口
 */
export interface StreamInfo {
  file: string
  index: number
  codec: string
  language: string
  duration?: string
  resolution?: string
  fps?: string
  channels?: number
  sample_rate?: number
  type?: string
}

/**
 * 媒体处理信息接口
 */
export interface ProcessMediaInfo {
  video: StreamInfo[]
  audio: StreamInfo[]
  subtitle: StreamInfo[]
  image: string[]
}

/**
 * 媒体信息
 */
export interface MediaInfo {
  mediaType: 'video' | 'audio' | 'image' | 'subtitle' | 'other'
  resolutionWidth?: number
  resolutionHeight?: number
  duration?: number
  bitrate?: number
  extension?: string
  filePath?: string
  fileSize?: number
}

/**
 * 合并媒体流结果
 */
export interface MergeResult {
  success: boolean
  outputPath?: string
  error?: string
}

/**
 * 媒体流选择信息接口
 */
export interface MediaStreamSelection {
  name: string
  input: MediaStreamSelectionInput[]
}

/**
 * 媒体流选择信息接口
 */
export interface MediaStreamSelectionInput {
  filePath: string
  ids: string[]
}

/**
 * 媒体流信息列表
 */
export interface FileMediaInfoItem {
  name: string
  filePath: string
  children: {
    id: string
    codec_type: string
    duration: number | string
    codec_name: string
  }[]
}

/**
 * 音视频合并进度
 */
export interface VideoAudioMergeProgressData {
  status: 'merging' | 'success' | 'error'
  progress?: number
  outputPath?: string
  outputName?: string
  error?: string
}

/**
 * 音视频转换进度
 */
export interface VideoAudioConverProgressData {
  id: string
  status: ConvertTaskStatus
  progress?: number
  outputPath?: string
  outputName?: string
  outputExt?: string
  error?: string
}

/**
 * 转换任务状态
 */
export type ConvertTaskStatus = 'waiting' | 'converting' | 'completed' | 'failed'

export interface ConvertFileMediaInfoItem {
  name: string
  filePath: string
  extname: string
  duration: number
  width: number
  height: number
  fileType: 'video' | 'audio' | 'image'
}

// 转换任务类型
export interface ConvertTask {
  id: string // 文件路径作为唯一ID
  filePath: string
  outputFormat: string
  outputDir: string
  // 额外参数
  extraParams?: string[]
  status: ConvertTaskStatus
  error?: string
  outputPath?: string
}
