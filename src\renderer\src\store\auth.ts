import { client, handlers } from '@/client'
import { create } from 'zustand'

interface AuthStore {
  // 授权URL相关状态
  authUrl: string
  currentUrl: string

  // 事件监听器
  unlistenAuthUrl?: () => void

  // 设置方法
  setAuthUrl: (url: string) => void
  setCurrentUrl: (url: string) => void

  // 事件监听管理
  listenAuthEvents: () => void
  clearAuthListeners: () => void

  // 主动获取 URL
  fetchAuthUrl: () => Promise<void>
}

export const useAuthStore = create<AuthStore>((set, get) => ({
  // 初始状态
  authUrl: '',
  currentUrl: '',

  // 设置方法
  setAuthUrl: (url: string) => set({ authUrl: url }),
  setCurrentUrl: (url: string) => set({ currentUrl: url }),

  // 主动获取最新的授权 URL
  fetchAuthUrl: async () => {
    try {
      // 尝试通过API获取URL，如果API不存在则忽略错误
      try {
        const url = await client.getAuthUrl()
        console.log('主动获取到授权 URL:', url)
        if (url) {
          set({ authUrl: url })
        }
      }
      catch (apiErr) {
        console.warn('getAuthUrl API 可能尚未实现:', apiErr)
      }
    }
    catch (err) {
      console.error('获取授权 URL 失败:', err)
    }
  },

  // 监听授权相关事件
  listenAuthEvents: () => {
    // 先清理现有监听器，确保重新注册
    get().clearAuthListeners()

    console.log('开始注册授权事件全局监听器')

    console.log('授权事件全局监听器注册完成')
  },

  // 清理所有监听器
  clearAuthListeners: () => {
    const { unlistenAuthUrl } = get()

    console.log('准备清理授权事件监听器')

    if (unlistenAuthUrl) {
      unlistenAuthUrl()
      console.log('已清理 authUrl 监听器')
    }

    set({
      unlistenAuthUrl: undefined,
    })

    console.log('授权事件监听器清理完成')
  },
}))
