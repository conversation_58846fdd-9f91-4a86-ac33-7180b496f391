import type { DownloadConfigStore } from '@renderer/store/download'
import { client } from '@/client'
import i18n from '@/utils/i18n'
import { mapDownloadConfigToSettings } from '@/utils/setting'
import { useLockFn } from 'ahooks'
import { useEffect } from 'react'
import useSWR from 'swr'

export function useSnapany() {
  const {
    data: aboutInfo,
    mutate: mutateAboutInfo,
    // isLoading: isLoadingAboutInfo,
  } = useSWR('snapanyAboutInfo', () => client?.getAboutInfo())

  const {
    data: settings,
    mutate: mutateSettings,
    // isLoading: isLoadingSettings,
  } = useSWR('getSettings', () => client?.getSetting())

  useEffect(() => {
    const changeLanguage = async () => {
      const lang = settings?.language === 'system' ? await client.getSystemLanguage() : settings?.language
      i18n.changeLanguage(lang)
    }
    changeLanguage()
  }, [settings?.language])

  const patchSetting = useLockFn(async (partial: Partial<typeof settings>) => {
    if (!settings) {
      return
    }
    const newSettings = {
      ...settings,
      ...partial,
    }
    await client.saveSetting(newSettings)
    mutateSettings()
  })

  const updateDownloadConfig = useLockFn(
    async (downloadConfig: DownloadConfigStore) => {
      if (!settings) {
        return
      }
      const updatedSettings = mapDownloadConfigToSettings(
        downloadConfig,
        settings,
      )
      // 更新设置代码
      await client.saveSetting(updatedSettings)
      mutateSettings()
    },
  )

  return {
    aboutInfo,
    mutateAboutInfo,
    settings,
    mutateSettings,
    patchSetting,
    // isLoading: isLoadingAboutInfo || isLoadingSettings,
    updateDownloadConfig,
  }
}
