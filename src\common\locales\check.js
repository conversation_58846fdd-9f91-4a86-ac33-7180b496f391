#!/usr/bin/env node

const fs = require('node:fs')
const path = require('node:path')

/**
 * 获取JSON对象中的所有键（包括嵌套键），以点号分隔表示层级
 * @param {object} obj - JSON对象
 * @param {string} prefix - 键前缀
 * @returns {string[]} - 所有键的数组
 */
function getAllKeys(obj, prefix = '') {
  let keys = []

  for (const key in obj) {
    const newKey = prefix ? `${prefix}.${key}` : key
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      keys = keys.concat(getAllKeys(obj[key], newKey))
    }
    else {
      keys.push(newKey)
    }
  }

  return keys
}

/**
 * 比较两个键集合，找出差异
 * @param {Set} setA - 第一个键集合
 * @param {Set} setB - 第二个键集合
 * @returns {object} - 包含两个集合各自特有的键
 */
function compareSets(setA, setB) {
  const onlyInA = [...setA].filter(x => !setB.has(x))
  const onlyInB = [...setB].filter(x => !setA.has(x))

  return {
    onlyInA,
    onlyInB,
  }
}

/**
 * 获取语言文件的路径
 * @returns {string[]} - 所有语言文件的路径
 */
function getLocaleFiles() {
  const localesDir = __dirname
  return fs.readdirSync(localesDir)
    .filter(file => file.endsWith('.json'))
    .map(file => path.join(localesDir, file))
}

/**
 * 打印带颜色的文本
 * @param {string} text - 要打印的文本
 * @param {string} color - 颜色代码
 */
function printColor(text, color) {
  const colors = {
    red: '\x1B[31m',
    green: '\x1B[32m',
    yellow: '\x1B[33m',
    blue: '\x1B[34m',
    magenta: '\x1B[35m',
    cyan: '\x1B[36m',
    reset: '\x1B[0m',
  }

  console.log(`${colors[color] || ''}${text}${colors.reset}`)
}

/**
 * 格式化键列表输出
 * @param {string[]} keys - 键列表
 * @returns {string} - 格式化后的输出
 */
function formatKeysList(keys) {
  if (keys.length === 0) {
    return '  无'
  }

  return keys.map(key => `  - ${key}`).join('\n')
}

/**
 * 检查特定语言文件
 */
function checkSpecificLocale(locale) {
  try {
    const baseLocale = 'en'
    const baseJson = JSON.parse(fs.readFileSync(path.join(__dirname, `${baseLocale}.json`), 'utf8'))
    const localeJson = JSON.parse(fs.readFileSync(path.join(__dirname, `${locale}.json`), 'utf8'))

    const baseKeys = new Set(getAllKeys(baseJson))
    const localeKeys = new Set(getAllKeys(localeJson))

    const baseVsLocale = compareSets(baseKeys, localeKeys)

    // 计算完成率
    const totalBaseKeys = baseKeys.size
    const completedKeys = totalBaseKeys - baseVsLocale.onlyInA.length
    const completionRate = (completedKeys / totalBaseKeys * 100).toFixed(2)

    // 只有当没有缺少的键，也没有多出的键时才显示详细信息
    if (baseVsLocale.onlyInA.length !== 0 || baseVsLocale.onlyInB.length !== 0) {
      printColor(`===== ${locale} =====`, 'cyan')
      printColor(`完成率: ${completionRate}% (${completedKeys}/${totalBaseKeys})`, 'cyan')

      if (baseVsLocale.onlyInA.length > 0) {
        printColor(`缺少的键:`, 'red')
        console.log(formatKeysList(baseVsLocale.onlyInA))
      }

      if (baseVsLocale.onlyInB.length > 0) {
        printColor(`多出的键:`, 'yellow')
        console.log(formatKeysList(baseVsLocale.onlyInB))
      }

      console.log() // 添加空行
    }

    return {
      locale,
      missingKeys: baseVsLocale.onlyInA,
      extraKeys: baseVsLocale.onlyInB,
      completionRate: Number.parseFloat(completionRate),
    }
  }
  catch (error) {
    console.error(`处理${locale}文件时出错:`, error.message)
    return null
  }
}

/**
 * 计算统计摘要
 */
function calculateSummary(results) {
  // 按完成率排序
  results.sort((a, b) => b.completionRate - a.completionRate)

  printColor('===== 语言完成率摘要 =====', 'magenta')

  results.forEach((result) => {
    const color = result.completionRate >= 90
      ? 'green'
      : result.completionRate >= 70 ? 'yellow' : 'red'

    printColor(`${result.locale}: ${result.completionRate}% (缺${result.missingKeys.length}个键) (多${result.extraKeys.length}个键)`, color)
  })

  // 计算平均完成率
  const avgCompletionRate = results.reduce((sum, r) => sum + r.completionRate, 0) / results.length
  printColor(`\n平均完成率: ${avgCompletionRate.toFixed(2)}%`, 'cyan')
}

/**
 * 主函数
 */
function main() {
  // 获取所有语言文件路径
  const localeFiles = getLocaleFiles()
  const baseLocale = 'en'

  // 对每个语言文件进行比较
  const results = []
  localeFiles.forEach((file) => {
    const locale = path.basename(file, '.json')

    // 跳过基准语言文件
    if (locale === baseLocale) {
      return
    }

    const result = checkSpecificLocale(locale)
    if (result) {
      results.push(result)
    }
  })

  // 生成统计摘要
  if (results.length > 0) {
    calculateSummary(results)
  }

  printColor('\n检查完成!', 'green')
}

// 执行主函数
main()
