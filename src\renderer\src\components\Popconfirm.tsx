import type { ButtonProps } from 'flowbite-react'
import type { MouseEvent<PERSON>andler, ReactNode, Ref } from 'react'
import { Button, Popover } from 'flowbite-react'
import { forwardRef, useImperativeHandle, useState } from 'react'
import { useTranslation } from 'react-i18next'

interface PopconfirmProps {
  okText?: string
  okButtonProps?: Omit<ButtonProps, 'onClick'>
  onConfirm?: MouseEventHandler<HTMLButtonElement>
  cancelText?: string
  cancelButtonProps?: Omit<ButtonProps, 'onClick'>
  onCancel?: MouseEventHandler<HTMLButtonElement>
  children: ReactNode
  disabled?: boolean
  title: ReactNode
  placement?: 'top' | 'bottom' | 'left' | 'right'
}

function Popconfirm({
  okText,
  cancelText,
  children,
  title,
  okButtonProps,
  cancelButtonProps,
  onCancel,
  onConfirm,
  disabled,
  placement = 'bottom',
}: PopconfirmProps, ref: Ref<{ openChange: (v: boolean) => void }>) {
  const { t } = useTranslation()

  const [open, setOpen] = useState(false)

  const handleOpenChange = (v: boolean) => {
    if (disabled) {
      return
    }
    setOpen(v)
  }

  useImperativeHandle(ref, () => ({
    openChange: (v: boolean) => {
      handleOpenChange(v)
    },
  }))

  return (
    <Popover
      open={disabled ? false : open}
      onOpenChange={handleOpenChange}
      placement={placement}
      content={(
        <div className="px-8 py-4 flex flex-col gap-4 bg-white">
          <main className=" flex items-center justify-center text-sm">
            {title}
          </main>
          <footer className="flex items-center justify-center gap-6">
            <Button
              className="text-sm"
              color="light"
              {...cancelButtonProps}
              onClick={(e) => {
                e.stopPropagation()
                setOpen(false)
                onCancel?.(e)
              }}
            >
              {cancelText ?? t('common.cancel')}
            </Button>
            <Button
              className="text-sm"
              color="blue"
              {...okButtonProps}
              onClick={onConfirm}
            >
              {okText ?? t('common.ok')}
            </Button>
          </footer>
        </div>
      )}
    >
      {/* 套一层 div 的原因是 flowbite-react 源码存在 ref 引用 target。 */}
      <div>{children}</div>
    </Popover>
  )
}

export default forwardRef(Popconfirm)
