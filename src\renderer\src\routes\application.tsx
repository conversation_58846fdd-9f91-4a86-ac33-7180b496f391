import type { ReactElement } from 'react'
import type { RouteObject } from 'react-router-dom'
import DownloadPage from '@renderer/pages/Download'
import FormatPage from '@renderer/pages/Format'
import MergePage from '@renderer/pages/Merge'
import {
  TbDownload,
  TbFold,
  TbSwitchHorizontal,
  TbWorldSearch,
} from 'react-icons/tb'
import { Navigate } from 'react-router-dom'

type IRoute = RouteObject & Partial<{ key: string, icon: ReactElement }>

export const applicationMenuRouter: IRoute[] = [
  {
    key: 'application.menu.download',
    icon: <TbDownload />,
    path: '/download',
    element: <DownloadPage />,
  },
  {
    key: 'application.menu.network',
    icon: <TbWorldSearch />,
    path: '/network',
    element: <></>,
  },
  {
    key: 'application.menu.format',
    icon: <TbSwitchHorizontal />,
    path: '/format',
    element: <FormatPage />,
  },
  {
    key: 'application.menu.merge',
    icon: <TbFold />,
    path: '/merge',
    element: <MergePage />,
  },
]

const routers: RouteObject[] = applicationMenuRouter
  .concat([
    {
      path: '/',
      element: <Navigate to="/download" />,
    },
  ])

export default routers
