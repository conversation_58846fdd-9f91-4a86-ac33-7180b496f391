// 任务状态
export const taskStatus = {
  extracting: 'extracting', // 提取中
  readyDownload: 'readyDownload', // 准备下载
  downloading: 'downloading', // 下载中
  pendingConversion: 'pendingConversion', // 等待转换
  converting: 'converting', // 转换中
  completed: 'completed', // 已完成
  failed: 'failed', // 失败
} as const

// 错误状态
export const errorStatusEnum = {
  extractError: 'extractError', // 解析错误
  downloadError: 'downloadError', // 下载错误
  convertError: 'convertError', // 转换错误
  moveError: 'moveError', // 移动错误
  unsupportedUrl: 'unsupportedUrl', // 不支持的URL
  interrupted: 'interrupted', // 中断
} as const

// 错误消息
export const errorMessageEnum = {
  extractError: 'extractError', // 解析错误
  downloadError: 'downloadError', // 下载错误
  convertError: 'convertError', // 转换错误
  moveError: 'moveError', // 移动错误
  unsupportedUrl: 'unsupportedUrl', // 不支持的URL
  needLogin: 'needLogin', // 需要登录
  interrupted: 'interrupted', // 中断
  cancel: 'cancel', // 取消
  timeout: 'timeout', // 超时
  noVideoFormats: 'noVideoFormats', // 没有视频格式
  serverError: 'serverError', // 服务器错误
  videoNotAccess: 'videoNotAccess', // 视频无法访问
  needPurchase: 'needPurchase', // 需要购买
  diskFull: 'diskFull', // 磁盘已满
  permissionDenied: 'permissionDenied', // 文件权限不足
} as const

// 操作
export const actionEnum = {
  login: 'login', // 登录
} as const
