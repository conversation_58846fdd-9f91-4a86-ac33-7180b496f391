import { useTaskStore } from '@/store/task'
import { Button, Modal } from 'flowbite-react'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TbExclamationCircleFilled, TbX } from 'react-icons/tb'
import { useLocation } from 'react-router-dom'

function CheckTaskModal() {
  const { t } = useTranslation()
  const location = useLocation()
  const [showModal, setShowModal] = useState(false)
  const { hasIncompleteTask, setHasIncompleteTask, fetchTask, interruptedTasks, continueTasks } = useTaskStore()
  const isNotAside = useMemo(() => {
    return ['/auth'].includes(location.pathname)
  }, [location.pathname])

  useEffect(() => {
    // 授权窗口不弹弹窗
    if (hasIncompleteTask && !isNotAside) {
      setShowModal(true)
    }
  }, [hasIncompleteTask])

  const handleClose = () => {
    setShowModal(false)
  }

  const closeModal = async () => {
    await interruptedTasks()
    await fetchTask()
    setHasIncompleteTask(false)
    handleClose()
  }

  const continueDownload = async () => {
    continueTasks()
    setHasIncompleteTask(false)
    handleClose()
  }

  return (
    <Modal show={showModal} size="lg">
      <Modal.Body className="relative p-5">
        <TbX className="absolute top-3 right-3 w-6 h-6 cursor-pointer text-gray-400" onClick={closeModal} />
        <div className="flex flex-col items-center mt-7">
          <TbExclamationCircleFilled className="w-6 h-6 mb-4 text-gray-400" />
          <p className="max-w-75 text-base text-gray-500 text-center mb-5">
            {t('taskModal.tip2')}
          </p>
          <div className="flex gap-4">
            <Button color="light" onClick={closeModal}>
              {t('taskModal.cancelDownload')}
            </Button>
            <Button onClick={continueDownload}>{t('taskModal.download')}</Button>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  )
}

export default CheckTaskModal
