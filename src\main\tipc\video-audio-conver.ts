import SettingService from '@main/service/setting'
import VideoAudioConverService from '@main/service/video-audio-conver'
import { t } from './instance'

export const videoAudioConverRoute = {
  /**
   * 获取需转换的文件信息
   * @param filePathArray 文件路径数组
   * @param format 转换类型
   * @returns
   */
  getMediaFormatConvertInfo: t.procedure.input<{ filePaths: string[], format?: 'video' | 'audio' | 'image' }>().action(async ({ input }) => {
    const { filePaths, format } = input

    if (!filePaths || filePaths.length === 0) {
      return {
        success: false,
        data: [],
      }
    }
    const validFileInfos = await VideoAudioConverService.getFilesMediaInfo(filePaths)

    // 如果format为空，则使用第一个有效文件的类型作为format
    const targetMediaType = format || validFileInfos[0]?.fileType

    if (!targetMediaType) {
      return {
        success: false,
        data: validFileInfos,
      }
    }

    // 过滤出指定类型的文件信息
    const filteredFiles = validFileInfos.filter(info => info.fileType === targetMediaType)

    return {
      success: filteredFiles.length > 0,
      data: filteredFiles,
      targetMediaType,
    }
  }),

  /**
   * 转换文件
   * @param filePaths 文件路径
   * @param outputFormat 输出格式
   * @returns
   */
  convertMediaFile: t.procedure.input<{ filePaths: string[], outputFormat: string, extraParams?: string[] }>().action(async ({ input }) => {
    const { filePaths, outputFormat, extraParams } = input
    // 调用合并函数
    const setting = SettingService.getSetting()
    const outputDir = setting.downloadPath

    VideoAudioConverService.convertMediaFiles(filePaths, outputFormat, outputDir, extraParams)
    return '开始转换'
  }),

  /**
   * 停止转换
   */
  stopConvert: t.procedure.action(async () => {
    VideoAudioConverService.stopCurrentConvertTask()
  }),
}
