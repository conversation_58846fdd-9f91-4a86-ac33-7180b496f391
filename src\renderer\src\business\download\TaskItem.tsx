import type { TaskType } from '@/store/task'
import type { SimpleResult } from '@common/types/electron-bridge'
import { AUDIO_FORMATS } from '@/constants/media'
import { useTaskStore } from '@/store/task'
import { calcDownloadByteVo } from '@/utils/download'
import { formatDuration, formatFileSize } from '@/utils/video'
import { DOWNLOAD_FORMAT_ENUM } from '@common/constants/download'
import { errorMessageEnum, errorStatusEnum, taskStatus } from '@main/constants/status'
import TaskOpenFile from '@renderer/business/download/TaskOpenFile'
import {
  MediaInfoItem,
  MediaListItem,
  MediaShowResolution,
} from '@renderer/client/media-compose'
import ImageSecureLoad from '@renderer/components/image/SecureLoad'
import Popconfirm from '@renderer/components/Popconfirm'
import classNames from 'classnames'
import { Tooltip } from 'flowbite-react'
import { memo, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { HiOutlineTrash } from 'react-icons/hi'
import {
  TbAlignBoxBottomCenter,
  TbClock,
  TbDeviceDesktop,
  TbDeviceFloppy,
  TbExclamationMark,
  TbFileDescription,
  TbPlayerStop,
  TbReload,
} from 'react-icons/tb'
import MoreActions from './MoreActions'
import TaskPlay from './TaskPlay'

const DOWNLOAD_FORMATS = Object.values(DOWNLOAD_FORMAT_ENUM)

export interface TaskItemProps {
  task: TaskType
  onDelete?: (task: TaskItemProps['task']) => void
  onRetry?: (task: TaskItemProps['task']) => void
  onOpen?: (task: TaskItemProps['task']) => Promise<SimpleResult>
  onAuth?: (task: TaskItemProps['task']) => void
  onOpenSettings?: () => void
}

function TaskItem({ task, onDelete, onOpen, onRetry, onAuth, onOpenSettings }: TaskItemProps) {
  const { t } = useTranslation()
  const { stopRecordingLive } = useTaskStore()
  const { id, thumbnail, text, url, taskStatus: status, extension, fileSize, bitrate, resolutionWidth, resolutionHeight, duration, downloadedSize, totalSize, percent, speed, eta, errorMessage, errorStatus, errorAction, isLive } = task || {}
  const isCompleted = status === taskStatus.completed
  const isError = !!errorMessage
  const isLiveRecording = isLive && status === taskStatus.downloading

  const progress = useMemo(() => {
    if (isCompleted) {
      return 100
    }
    const progress = Number(downloadedSize! / totalSize!) * 100 || percent!
    return progress > 0 && progress < 100 ? progress : 0
  }, [isCompleted, isLiveRecording, downloadedSize, totalSize, percent])

  const statusNode = useMemo(() => {
    if (!status) {
      return
    }
    if (status === taskStatus.failed && errorMessage === errorMessageEnum.cancel) {
      switch (errorStatus) {
        case errorStatusEnum.extractError:
          return t('errors.parseError')
        case errorStatusEnum.downloadError:
          return t('errors.downloadError')
        case errorStatusEnum.convertError:
          return t('errors.mergeError')
      }
    }
    if (errorMessage === errorMessageEnum.interrupted || errorMessage === errorMessageEnum.cancel) {
      switch (status) {
        case taskStatus.extracting:
          return t('errors.parseInterruptedError')
        case taskStatus.downloading:
          return t('errors.downloadInterruptedError')
        case taskStatus.pendingConversion:
          return t('errors.pendingConversionInterruptedError')
        case taskStatus.converting:
          return t('errors.mergeInterruptedError')
      }
    }
    if (isError) {
      switch (errorMessage) {
        case errorMessageEnum.extractError:
          return t('errors.parseError')
        case errorMessageEnum.downloadError:
          return t('errors.downloadError')
        case errorMessageEnum.convertError:
          return t('errors.mergeError')
        case errorMessageEnum.moveError:
          return t('errors.moveError')
        case errorMessageEnum.unsupportedUrl:
          return t('errors.unsupportedUrl')
        case errorMessageEnum.noVideoFormats:
          return t('errors.noVideoFormats')
        case errorMessageEnum.timeout:
          return t('errors.timeout')
        case errorMessageEnum.needPurchase:
          return t('errors.needPurchase')
        case errorMessageEnum.serverError:
          return t('errors.serverError')
        case errorMessageEnum.videoNotAccess:
          return t('errors.videoNotAccess')
        case errorMessageEnum.diskFull:
          return t('errors.diskFull')
        case errorMessageEnum.permissionDenied:
          return (
            <p>
              {t('errors.permissionDenied')}
              {' '}
              <span
                className="text-blue-600 cursor-pointer"
                onClick={() => onOpenSettings?.()}
              >

                {t('errors.changeFolder')}

              </span>
            </p>
          )
        case errorMessageEnum.needLogin:
          return (
            <p>
              {t('errors.needLoginToDownload')}
              {errorAction && (
                <span
                  className="ml-2 text-blue-600 cursor-pointer"
                  onClick={() => onAuth?.(task)}
                >
                  {t('taskActions.nowLogIn')}
                </span>
              )}
            </p>
          )
        case errorMessageEnum.interrupted:
          return t('errors.interrupted')
        case errorMessageEnum.cancel:
          return t('taskStatus.cancelled')
        default:
          return errorMessage
      }
    }
    switch (status) {
      case taskStatus.extracting:
        return t('taskStatus.retrievingInformation')
      case taskStatus.downloading:
        return t('taskStatus.downloading')
      case taskStatus.pendingConversion:
        return t('taskStatus.pendingConversion')
      case taskStatus.converting:
        return t('taskStatus.converting')
      default:
        return t('taskStatus.preparingToDownload')
    }
  }, [status, t, task, onAuth, onOpenSettings, errorAction, errorMessage, errorStatus, isError])

  const renderCompletedDescription = () => {
    const isMusic = AUDIO_FORMATS.map(({ value }) => value as string).includes(extension!)

    return (
      <>
        {extension && (
          <MediaInfoItem
            className="w-[60px]"
            icon={<TbFileDescription />}
            content={extension.toUpperCase()}
          />
        )}
        <MediaInfoItem
          className="w-[90px]"
          icon={<TbDeviceFloppy />}
          content={formatFileSize(fileSize! || totalSize!)}
        />
        {DOWNLOAD_FORMATS.includes(extension as DOWNLOAD_FORMAT_ENUM) && (isMusic
          ? (
              bitrate && typeof bitrate === 'number' && !Number.isNaN(bitrate) && (
                <MediaInfoItem
                  className="w-[100px]"
                  icon={<TbAlignBoxBottomCenter />}
                  content={`${Math.floor(bitrate / 1000)} kbps`}
                />
              )
            )
          : (
              resolutionWidth && resolutionHeight && typeof resolutionWidth === 'number' && typeof resolutionHeight === 'number' && !Number.isNaN(resolutionWidth) && !Number.isNaN(resolutionHeight) && (
                <MediaInfoItem
                  className="w-[100px]"
                  icon={<TbDeviceDesktop />}
                  content={(
                    <MediaShowResolution
                      width={resolutionWidth}
                      height={resolutionHeight}
                    />
                  )}
                />
              )
            ))}
        {duration! > 0 && (
          <MediaInfoItem
            icon={<TbClock />}
            content={formatDuration(duration!)}
          />
        )}
      </>
    )
  }

  const renderUncompletedDescription = () => {
    return (
      <div
        className={classNames({
          'text-[#ff4d4f]': isError,
        })}
      >
        <div className="flex gap-6 flex-wrap text-xs">
          <span>{statusNode}</span>
          {!isError && status === taskStatus.downloading && (
            <>
              {isLiveRecording
                ? (
                  // 直播录制时只显示已下载大小
                    <span>
                      {calcDownloadByteVo(Number(downloadedSize))}
                    </span>
                  )
                : (
                  // 普通下载显示完整信息
                    <>
                      <span>
                        {calcDownloadByteVo(Number(downloadedSize))}
                        {' '}
                        /
                        {' '}
                        {calcDownloadByteVo(Number(totalSize))}
                      </span>
                      <span>{calcDownloadByteVo(Number(speed), '/s')}</span>
                      {eta && (
                        <span>
                          {t('taskActions.timeLeft')}
                          {' '}
                          {eta}
                        </span>
                      )}
                    </>
                  )}
            </>
          )}
        </div>
      </div>
    )
  }

  const renderAction = () => {
    const iconStyle = 'cursor-pointer text-[40px] p-2'

    return (
      <>
        {isCompleted
          ? (
              <TaskOpenFile
                task={task}
                onOpen={onOpen}
                onDelete={onDelete}
                className={iconStyle}
              />
            )
          : (
              // 处理错误状态 - 包括直播录制中的错误
              (isError && status !== taskStatus.failed)
                ? (
                    <Tooltip content={t('taskActions.retry')}>
                      <TbReload className={iconStyle} onClick={() => onRetry?.(task)} />
                    </Tooltip>
                  )
                : (
                    // 直播录制时显示停止录制按钮
                    isLiveRecording
                      ? (
                          <Tooltip content={t('taskActions.stopRecording')}>
                            <TbPlayerStop
                              className={`${iconStyle} text-black fill-black`}
                              onClick={() => stopRecordingLive(task)}
                            />
                          </Tooltip>
                        )
                      : (
                          <Popconfirm
                            disabled={isCompleted || isError}
                            placement="top"
                            title={t('dialogs.deleteDownloading.fileIsDownloading')}
                            onConfirm={() => onDelete?.(task)}
                            okButtonProps={{
                              color: 'failure',
                            }}
                            okText={t('dialogs.deleteDownloading.delete')}
                          >
                            <Tooltip content={t('taskActions.delete')}>
                              <HiOutlineTrash
                                className={iconStyle}
                                onClick={() => (isCompleted || isError) && onDelete?.(task)}
                              />
                            </Tooltip>
                          </Popconfirm>
                        )
                  )
            )}
        <MoreActions task={task} onDelete={onDelete} />
      </>
    )
  }

  return (
    <MediaListItem key={id} progress={isLiveRecording ? 0 : progress}>
      <main className="flex gap-4 items-center">
        <ImageSecureLoad
          url={thumbnail || ''}
          showSuffix={isLive || (!DOWNLOAD_FORMATS.includes(extension as DOWNLOAD_FORMAT_ENUM) && !!extension)}
          suffix={isLive ? 'LIVE' : extension?.toUpperCase()}
          loading={status === taskStatus.extracting && !isError}
          isLive={isLive}
        >
          {isCompleted
            && DOWNLOAD_FORMATS.includes(extension as DOWNLOAD_FORMAT_ENUM) && (
            <TaskPlay task={task} onDelete={onDelete} />
          )}
          {isError && (
            <div
              className="absolute inset-0 bg-black/30 rounded flex items-center justify-center"
            >
              <TbExclamationMark className="text-red-600 w-7 h-7 stroke-2" />
            </div>
          )}
        </ImageSecureLoad>
        <div className="flex flex-col justify-between h-full gap-y-1">
          <section className="table table-fixed w-full">
            <p className="text-sm text-ellipsis overflow-hidden whitespace-nowrap">
              {text || url}
            </p>
          </section>
          <section className="flex gap-x-[24px] text-gray-700 text-sm flex-wrap">
            {isCompleted
              ? renderCompletedDescription()
              : renderUncompletedDescription()}
          </section>
        </div>
      </main>
      <aside className="flex items-center [&>div]:hover:bg-gray-100 [&>div]:hover:rounded-lg">
        {renderAction()}
      </aside>
    </MediaListItem>
  )
}

export default memo(TaskItem)
