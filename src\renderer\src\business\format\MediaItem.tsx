import { client } from '@/client'
import Popconfirm from '@/components/Popconfirm'
import Progress from '@/components/Progress'
import { useFormatStore } from '@/store/format'
import { formatDuration } from '@/utils/video'
import { isMac } from '@main/constants/env'
import { Spinner, Tooltip } from 'flowbite-react'
import { ExclamationCircle, FolderOpen } from 'flowbite-react-icons/outline'
import { memo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TbTrash } from 'react-icons/tb'

function MediaItem(props) {
  const { t } = useTranslation()
  const { name, width, height, duration, extname, filePath, status, progress, outputPath, outputExt, error, onDelete, isHighlight } = props || {}
  const { convertStatus, format } = useFormatStore()
  const [isOpenFailed, setIsOpenFailed] = useState(false)

  return (
    <div className={`group h-11 flex items-start gap-2 box-content py-1 px-4 bg-white ${isHighlight ? 'animate-highlight' : ''}`}>
      <div className="w-full flex items-center justify-between py-2">
        <div className="w-0 flex-1 flex items-center gap-2">
          <Tooltip content={name} theme={{ target: 'truncate' }}>
            <span className="truncate text-xs text-gray-900">{name}</span>
          </Tooltip>
        </div>
        <div className="flex-shrink-0 grid grid-cols-7 gap-2 min-w-[220px] flex-[0_0_320px] items-center">
          <span className="text-xs text-gray-900 text-center col-span-2">{format === 'image' ? (width && height) ? `${width}x${height}` : '--' : formatDuration(duration)}</span>
          <span className="text-xs text-gray-900 text-center col-span-2">{extname}</span>
          <span className="text-xs text-gray-900 text-center col-span-2">{outputExt || '--'}</span>
          <div className="flex items-center justify-center">
            {convertStatus === 'waiting' && onDelete && (
              <Tooltip content={t('taskActions.delete')}>
                <TbTrash
                  className="w-7 h-7 p-1 text-gray-500 cursor-pointer rounded hover:bg-gray-100"
                  onClick={() => onDelete(filePath)}
                />
              </Tooltip>
            )}
            {status === 'waiting' && (
              <Spinner size="sm" className="fill-gray-500" />
            )}
            {status === 'converting' && (progress
              ? <Progress percent={progress} type="circle" strokeWidth={2} size={18} />
              : <Spinner size="sm" className="fill-blue-700" />
            )}
            {status === 'completed' && (
              <Popconfirm
                placement="top"
                title={(
                  <div className="max-w-[181px] text-center">
                    {t('dialogs.fileDeleted.fileHasBeenDeletedOrMoved')}
                  </div>
                )}
                okText={t('dialogs.fileDeleted.remove')}
                disabled={!isOpenFailed}
                onConfirm={() => onDelete?.(filePath)}
              >
                <Tooltip
                  content={
                    isMac ? t('taskActions.showInFinder') : t('taskActions.showInFolder')
                  }
                >
                  <FolderOpen
                    className="w-7 h-7 p-1 text-gray-500 cursor-pointer rounded hover:bg-gray-100"
                    onClick={(e) => {
                      if (isOpenFailed) {
                        return
                      }
                      client.openFileDir({ filePath: outputPath }).then(({ success }) => {
                        if (!success) {
                          setIsOpenFailed(true)
                          setTimeout(() => {
                            e.target?.dispatchEvent(
                              new MouseEvent('click', { bubbles: true }),
                            )
                          }, 0)
                        }
                      })
                    }}
                  />
                </Tooltip>
              </Popconfirm>
            )}
            {error && (
              <Tooltip content={error} theme={{ target: 'truncate' }}>
                <ExclamationCircle className="w-5 h-5 text-red-700 cursor-pointer" />
              </Tooltip>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default memo(MediaItem)
