{"version": "6", "dialect": "sqlite", "id": "36fc0a28-cba7-4a9d-a1c2-217890fe98f2", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"task": {"name": "task", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "thumbnail": {"name": "thumbnail", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "request_headers": {"name": "request_headers", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "extension": {"name": "extension", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_path": {"name": "file_path", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "resolution_width": {"name": "resolution_width", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "resolution_height": {"name": "resolution_height", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "bitrate": {"name": "bitrate", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "task_status": {"name": "task_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "error_status": {"name": "error_status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "error_action": {"name": "error_action", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "temp_task": {"name": "temp_task", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}