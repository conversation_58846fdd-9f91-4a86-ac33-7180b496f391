import type { FileMediaInfoItem, VideoAudioMergeProgressData } from '@main/types/ffmpeg'
import { client, handlers } from '@/client'
import { Notice } from '@/components/Notice'
import { findValidIndex, handleMediaInfoData, mergeArraysByKey, mergeMediaData } from '@/utils/merge'
import { create } from 'zustand'

interface MergeStore {
  fileData: FileMediaInfoItem[]
  checkedIndex?: number
  progressData?: VideoAudioMergeProgressData
  /** 相同filePath元素的filePath */
  repeatDataItemKey: Set<unknown>
  unlisten?: () => void
  /** 设置选中项index */
  setCheckedIndex: (v: number) => void
  /** 处理上传文件 */
  handleUploadFile: (files: File[]) => Promise<void>
  /** 清空文件 */
  clearAllData: () => void
  /** 删除 */
  deleteDataItem: (filePath: string, id: string) => void
  /** 合并 */
  handleMerge: () => Promise<void>
  /** 监听合并 */
  listenMerge: () => void
  /** 清除监听合并 */
  clearListenMerge: () => void
  /** 取消合并 */
  cancelMerge: () => Promise<void>
  /** 清除合并进度数据 */
  clearProgressData: () => void
}

export const useMergeStore = create<MergeStore>((set, get) => ({
  fileData: [],
  checkedIndex: undefined,
  progressData: undefined,
  repeatDataItemKey: new Set(),
  unlisten: undefined,
  setCheckedIndex(v) {
    set(() => ({ checkedIndex: v }))
  },
  async handleUploadFile(files) {
    if (files && files.length > 0) {
      try {
        const filePaths: string[] = []
        // 使用预加载脚本中暴露的API
        for (const file of files) {
          const filePath = window.api.getFilePathFromFile(file)
          filePaths.push(filePath ?? '')
        }
        const result = await client?.getFilesMediaInfo(filePaths)
        if (result?.success) {
          const fileData = handleMediaInfoData(result?.data)
          const { replaceSetKey, data } = mergeArraysByKey(get().fileData, fileData, 'filePath')
          const index = findValidIndex(get().checkedIndex, data)
          set(() => ({ fileData: data, checkedIndex: index, progressData: undefined, repeatDataItemKey: replaceSetKey }))
        }
      }
      catch (error) {
        console.error('上传文件失败:', error)
        Notice.error('上传文件失败')
      }
    }
    setTimeout(() => {
      set(() => ({ repeatDataItemKey: new Set() }))
    }, 2000)
  },
  clearAllData() {
    set(() => ({ fileData: [], checkedIndex: undefined, progressData: undefined, repeatDataItemKey: new Set() }))
  },
  deleteDataItem(filePath, id) {
    const newData = get().fileData.map((item) => {
      if (item.filePath === filePath) {
        const children = item.children.filter(i => i.id !== id)
        return { ...item, children }
      }
      return item
    })
    const fileData = handleMediaInfoData(newData)
    const index = findValidIndex(get().checkedIndex, fileData)
    set(() => ({ fileData, checkedIndex: index }))
  },
  async handleMerge() {
    set(() => ({ progressData: { status: 'merging', progress: 0 } }))
    const data = mergeMediaData(get().fileData, get().checkedIndex!)
    client?.mergeMedia(data)
  },
  listenMerge() {
    if (get().unlisten) {
      return
    }
    const unlistenFunc = handlers.onVideoAudioMergeProgress.listen((data) => {
      set(() => ({ progressData: data }))
      if (data.status === 'success') {
        set(() => ({ fileData: [], checkedIndex: undefined, repeatDataItemKey: new Set() }))
      }
    })
    set(() => ({ unlisten: unlistenFunc }))
  },
  clearListenMerge() {
    if (get().unlisten) {
      get().unlisten?.()
      set(() => ({ unlisten: undefined }))
    }
  },
  async cancelMerge() {
    await client.stopMergeTask()
    set(() => ({ progressData: undefined }))
  },
  clearProgressData() {
    set(() => ({ progressData: undefined }))
  },
}))
