```mermaid
stateDiagram-v2
    [*] --> 初始化: 渲染进程创建任务

    初始化 --> 解析中: initDownloadTask()

    解析中 --> 下载中: 解析URL成功
    解析中 --> 失败: 解析失败

    下载中 --> 合并中: 所有文件下载完成
    下载中 --> 失败: 下载出错

    合并中 --> 移动文件: 合并转换完成
    合并中 --> 失败: 合并失败

    移动文件 --> 已完成: 文件移动成功
    移动文件 --> 失败: 移动失败

    已完成 --> [*]: 清理临时文件
    失败 --> [*]: 记录错误信息

    note right of 解析中
        使用yt-dlp解析URL
        获取视频信息和下载链接
    end note

    note right of 下载中
        下载音频、视频等资源
        实时发送下载进度
    end note

    note right of 合并中
        合并音视频文件
        处理标题、语言等元数据
    end note

    note right of 已完成
        更新本地存储状态
        UI显示下载完成
    end note

    note right of 失败
        向渲染进程发送错误信息
        更新任务状态为失败
    end note
```
