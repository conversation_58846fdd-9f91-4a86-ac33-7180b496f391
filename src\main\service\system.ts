import type { RendererHandlers } from '@main/renderer-handlers'
import type { UpdateInfo, UpgradeContent } from '@main/types'
import { spawn } from 'node:child_process'
import fs from 'node:fs/promises'
import path from 'node:path'
import { getRendererHandlers } from '@egoist/tipc/main'
import { isMac } from '@main/constants/env'
import { logError } from '@main/lib'
import { checkFileExists, compareSoftwareVersions, ensureDirectoryExists, getSoftwareInfo } from '@main/utils'
import FileDownloader from '@main/utils/file-downloader'
import { getMainWindow } from '@main/window'
import { app, shell } from 'electron'

class SystemService {
  constructor() { }

  /**
   * 获取下载链接
   * @param updateInfo 更新信息
   * @returns 适合当前系统的下载链接
   */
  private getDownloadUrl(updateInfo: UpdateInfo): string {
    if (process.platform === 'darwin') {
      // 检查是否为 Apple Silicon
      const isArm64 = process.arch === 'arm64'
      return isArm64
        ? updateInfo.downloadUrls.macAppleSilicon
        : updateInfo.downloadUrls.macIntel
    }
    else if (process.platform === 'win32') {
      return updateInfo.downloadUrls.windows
    }
    throw new Error('不支持的操作系统')
  }

  /**
   * 获取软件最新版本
   * @returns 软件最新版本
   */
  public async getSoftwareLatestVersion(): Promise<{
    hasUpdate: boolean
    currentVersion?: string
    latestVersion?: string
    normalUpgradeVersion?: string
    updateType?: 'force' | 'optional'
    upgradeContent?: UpgradeContent
    downloadUrl?: string
    error?: string
  }> {
    try {
      const updateInfo = await getSoftwareInfo()
      const currentVersion = app.getVersion()
      // 获取更新内容
      const upgradeContent = updateInfo.upgradeContent

      let updateType: 'force' | 'optional' = 'optional'
      let hasUpdate = false

      // 如果当前版本<=强制更新版本，则强制更新
      if (compareSoftwareVersions(currentVersion, updateInfo.forcedUpgradeVersion) <= 0) {
        updateType = 'force'
        hasUpdate = true
      }
      // 如果当前版本<=建议更新版本，则提示更新
      else if (compareSoftwareVersions(currentVersion, updateInfo.normalUpgradeVersion) <= 0) {
        updateType = 'optional'
        hasUpdate = true
      }

      return {
        hasUpdate,
        currentVersion,
        latestVersion: updateInfo.latestVersion,
        normalUpgradeVersion: updateInfo.normalUpgradeVersion,
        updateType,
        upgradeContent,
        downloadUrl: this.getDownloadUrl(updateInfo),
      }
    }
    catch (error) {
      console.error('检查更新失败:', error)
      return {
        hasUpdate: false,
        error: error instanceof Error ? error.message : '检查更新失败',
      }
    }
  }

  public async downloadSoftware(downloadUrl: string): Promise<void> {
    const tempDir = path.join(app.getPath('temp'), 'updates')
    try {
      await fs.access(tempDir)
    }
    catch {
      // 目录不存在，创建它
      const data = await ensureDirectoryExists(tempDir)
      if (!data.success) {
        logError('下载软件二进制依赖包创建临时目录失败', { error: data.error })
        return
      }
    }

    const fileName = path.basename(downloadUrl)
    const finalFilePath = path.join(tempDir, fileName)
    // 下载器
    const fileDownloader = new FileDownloader()
    fileDownloader.setTempDir(tempDir)
    fileDownloader.setDeleteTempFile(true)
    // 发送渲染进程回调
    const handlers = getRendererHandlers<RendererHandlers>(getMainWindow().webContents)
    fileDownloader.download(downloadUrl, {}, null, {
      onProgress: async (progress) => {
        console.log('软件更新下载进度:', progress)
        handlers.onSoftwareUpdateProgress.send({
          success: true,
          totalSize: progress.totalBytes,
          downloadedSize: progress.downloadedBytes,
        })
      },
      onComplete: async (filePath) => {
        try {
          console.log('软件更新下载完成临时路径:', filePath)
          console.log('软件更新下载完成最终路径:', finalFilePath)
          await fs.rename(filePath, finalFilePath)
          await fs.chmod(finalFilePath, 0o755)
          handlers.onSoftwareUpdateProgress.send({
            success: true,
            filePath: finalFilePath,
          })
        }
        catch (error) {
          console.error('软件更新下载失败:', error)
          handlers.onSoftwareUpdateProgress.send({
            success: false,
          })
        }
      },
      onError: async (error) => {
        console.error('软件更新下载失败:', error)
        handlers.onSoftwareUpdateProgress.send({
          success: false,
        })
      },
    })
  }

  public async checkSoftwarePackageExists(version: string): Promise<{
    exists: boolean
    filePath: string
  }> {
    try {
      const tempDir = path.join(app.getPath('temp'), 'updates')
      const suffix = isMac
        ? process.arch === 'arm64'
          ? 'arm64.dmg'
          : 'x64.dmg'
        : 'x64.exe'
      const installerName = `SnapAny_${version}_${suffix}`
      const installerPath = path.join(tempDir, installerName)

      const exists = await checkFileExists(installerPath)
      return {
        exists,
        filePath: exists ? installerPath : '',
      }
    }
    catch (error) {
      console.warn('检查本地安装包失败:', error)
      return {
        exists: false,
        filePath: '',
      }
    }
  }

  public async installSoftware(filePath: string): Promise<void> {
    try {
      if (filePath) {
        // 如果有文件路径，则安装更新
        if (process.platform === 'darwin') {
          // macOS
          await shell.openPath(filePath)
          // 等待一小段时间确保安装包被打开
          setTimeout(() => app.quit(), 1000)
        }
        else if (process.platform === 'win32') {
          const child = spawn(filePath, [], {
            detached: true,
            stdio: ['ignore', 'ignore', 'ignore'],
          })

          // 等待进程启动
          child.unref()
          setTimeout(() => app.quit(), 1000)
        }
      }
      else {
        // 如果没有文件路径直接退出
        app.quit()
      }
    }
    catch (error) {
      console.error('安装更新失败:', error)
      // 出错时延迟退出，让用户看到错误信息
      setTimeout(() => app.quit(), 2000)
    }
  }
}

export default new SystemService()
