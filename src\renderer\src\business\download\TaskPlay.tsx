import { client } from '@/client'
import Popconfirm from '@/components/Popconfirm'
import { useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TbPlayerPlayFilled } from 'react-icons/tb'

function TaskPlay({ onDelete, task }) {
  const { t } = useTranslation()
  const [isOpenFailed, setIsOpenFailed] = useState(false)
  const popconfirmRef = useRef<{ openChange: (v: boolean) => void }>(null)

  return (

    <div
      className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 group-hover:bg-black/50 transition-all duration-300 rounded cursor-pointer flex items-center justify-center"
      onClick={(e) => {
        if (isOpenFailed) {
          popconfirmRef.current?.openChange(true)
          return
        }
        client?.openFile({ filePath: task.filePath }).then(({ success }) => {
          if (!success) {
            setIsOpenFailed(true)
            setTimeout(() => {
              // 再模拟一次点击已达到非受控 Popconfirm 效果
              e.target?.dispatchEvent(
                new MouseEvent('click', { bubbles: true }),
              )
            }, 0)
          }
        })
      }}
    >
      <Popconfirm
        ref={popconfirmRef}
        placement="top"
        title={(
          <div className="max-w-[181px] text-center">
            {t('dialogs.fileDeleted.fileHasBeenDeletedOrMoved')}
          </div>
        )}
        okText={t('dialogs.fileDeleted.remove')}
        disabled={!isOpenFailed}
        onConfirm={() => onDelete?.(task)}
      >
        <TbPlayerPlayFilled className="text-white text-2xl" />
      </Popconfirm>
    </div>
  )
}

export default TaskPlay
