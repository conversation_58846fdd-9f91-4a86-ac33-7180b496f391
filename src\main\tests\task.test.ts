import { taskStatus } from '@main/constants/status'
import TaskService from '@main/service/task'
import { expect, it } from 'vitest'

it('save task by urls', async () => {
  const urls = ['https://www.xiaohongshu.com/discovery/item/6416c25c0000000012032d9c?source=webshare&xhsshare=pc_web&xsec_token=AB1tOXXYWBvEwwjDJWB3xav72OQTIzy5FNCeL1Ef3Reu4=&xsec_source=pc_share']
  const taskList = await TaskService.saveTaskByUrls(urls)
  const expectList = [
    {
      id: expect.any(String),
      text: urls[0],
      url: urls[0],
      taskStatus: taskStatus.extracting,
      thumbnail: null,
      requestHeaders: null,
      extension: null,
      fileSize: null,
      filePath: null,
      resolutionWidth: null,
      resolutionHeight: null,
      bitrate: null,
      errorStatus: null,
      errorMessage: null,
      errorAction: null,
      tempTask: null,
      createdAt: expect.any(Number),
      updatedAt: expect.any(Number),
    },
  ]
  expect(taskList).toEqual(expectList)
})
