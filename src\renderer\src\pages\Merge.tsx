import MediaGroup from '@/business/merge/MediaGroup'
import { client } from '@/client'
import { DownloadConfig } from '@/client/download-compose'
import Progress from '@/components/Progress'
import { PLATFORMS, VIDEO_FORMATS } from '@/constants/media'
import { useSnapany } from '@/hooks/snapany'
import { useDownloadStore } from '@/store/download'
import { useMergeStore } from '@/store/merge'
import { isWin } from '@main/constants/env'
import { Button, Dropdown } from 'flowbite-react'
import { CheckCircle, ExclamationCircle, Folder, Plus } from 'flowbite-react-icons/outline'
import { useCallback, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { GrClearOption } from 'react-icons/gr'
import { TbFileUpload, TbFold } from 'react-icons/tb'
import { Virtuoso } from 'react-virtuoso'

function Header() {
  const { t } = useTranslation()

  return (
    <div className="flex items-center justify-between border-b border-gray-200 bg-white py-3 px-4 text-xs text-gray-500 font-semibold rounded-t-lg">
      <span>{t('merge.fileName')}</span>
      <div className="grid grid-cols-5 gap-2 min-w-[220px] flex-[0_0_260px]">
        <span className="text-center col-span-2">{t('merge.duration')}</span>
        <span className="text-center col-span-2">{t('merge.format')}</span>
        <span></span>
      </div>
    </div>
  )
}

function Item({ item, ...props }) {
  return <div {...props} className="border-b-1 border-gray-300 last:border-0 last:[&>div]:rounded-b-lg" />
}

function MergePage() {
  const { t } = useTranslation()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { updateDownloadConfig } = useSnapany()
  const {
    platform,
    videoFormat,
    changePlatform,
    changeVideoFormat,
  } = useDownloadStore()
  const {
    fileData,
    checkedIndex,
    progressData,
    repeatDataItemKey,
    setCheckedIndex,
    handleUploadFile,
    clearAllData,
    deleteDataItem,
    handleMerge,
    cancelMerge,
    clearProgressData,
  } = useMergeStore()

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    const files = Array.from(e.dataTransfer.files || [])
    handleUploadFile(files)
  }, [fileData.length])

  const handleFileChange = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || [])
      handleUploadFile(files)
    },
    [fileData.length],
  )

  const bindAfter = useCallback(
    (fn: () => void) => async () => {
      await fn()
      const latestDownloadConfig = useDownloadStore.getState()
      await updateDownloadConfig(latestDownloadConfig)
    },
    [updateDownloadConfig],
  )

  const handleRetry = () => {
    clearProgressData()
    handleMerge()
  }

  const handleView = () => {
    if (!progressData?.outputPath)
      return
    client.openFileDir({ filePath: progressData?.outputPath })
  }

  const hasVideo = useMemo(() => {
    if (fileData && fileData.length > 0) {
      let flag = false
      fileData.forEach((item) => {
        if (flag)
          return
        flag = item.children.some(i => i.codec_type === 'video')
      })
      return flag
    }
    return false
  }, [fileData])

  return (
    <div
      className="w-full h-full relative"
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {fileData.length > 0
        ? (
            <div className="w-full h-full flex flex-col">
              <div className="w-full h-full p-4 overflow-y-auto">
                <Virtuoso
                  className="rounded-lg"
                  totalCount={fileData.length}
                  data={fileData}
                  components={{ Header, Item }}
                  itemContent={(index, item) => {
                    return (
                      <MediaGroup
                        key={index}
                        isHighlight={repeatDataItemKey.has(item.filePath)}
                        checked={checkedIndex === index}
                        onChangeCheck={() => setCheckedIndex(index)}
                        fileMediaInfo={item}
                        onDelete={deleteDataItem}
                      />
                    )
                  }}
                />
              </div>
              <footer className="shrink-0 w-full flex items-center justify-between p-4 bg-white">
                <div className="flex items-center gap-4">
                  <Button outline color="blue" onClick={() => fileInputRef.current?.click()}>
                    <span className="flex items-center gap-2 text-sm font-medium">
                      <Plus className="w-4 h-4" />
                      {t('merge.addFiles')}
                    </span>
                  </Button>
                  <Button outline color="black" onClick={clearAllData}>
                    <span className="flex items-center gap-2 text-sm font-medium">
                      <GrClearOption className="w-4 h-4" />
                      {t('merge.clear')}
                    </span>
                  </Button>
                </div>
                <div className="flex items-center gap-4">
                  {hasVideo
                    ? (
                        <Dropdown
                          label={(
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-gray-500">
                                {platform ? t('download.for') : t('download.format')}
                              </span>
                              <span className="text-sm">
                                {platform
                                  ? PLATFORMS.find(p => p.value === platform)?.label
                                  : VIDEO_FORMATS.find(f => f.value === videoFormat)?.label}
                              </span>
                            </div>
                          )}
                          inline
                          theme={{
                            arrowIcon: 'ml-1 w-5 h-5',
                            inlineWrapper: 'flex items-center p-1 rounded cursor-pointer disabled:cursor-not-allowed hover:bg-gray-100',
                            content: 'focus:outline-none',
                          }}
                        >
                          {VIDEO_FORMATS.map(({ label, value }) => (
                            <DownloadConfig
                              key={value}
                              label={label}
                              showCheck={value === videoFormat}
                              onClick={bindAfter(() => changeVideoFormat(value))}
                            />
                          ))}
                          <Dropdown.Divider />
                          {PLATFORMS.map(({ label, value }) => (
                            <DownloadConfig
                              label={label}
                              key={value}
                              showCheck={value === platform}
                              onClick={bindAfter(() => changePlatform(value))}
                            />
                          ))}
                        </Dropdown>
                      )
                    : (
                        <div className="flex items-center gap-2">
                          <ExclamationCircle className="w-4.5 h-4.5 text-yellow-400" />
                          <span className="text-sm text-gray-900">{t('merge.needAddFile')}</span>
                        </div>
                      )}

                  <Button onClick={handleMerge} disabled={!hasVideo}>
                    <span className="flex items-center gap-2 text-sm font-medium">
                      <TbFold className="w-5 h-5" />
                      {t('merge.merge')}
                    </span>
                  </Button>
                </div>
              </footer>
            </div>
          )
        : (
            <div className="w-full h-full flex items-center justify-center">
              {progressData?.status === 'success'
                ? (
                    <div className="flex flex-col items-center gap-8 mx-8">
                      <div className="flex flex-col items-center gap-4">
                        <CheckCircle className="w-33 h-33 text-green-700" />
                        <p className="text-lg font-bold text-black">{t('merge.success')}</p>
                        <span className="text-base text-center text-gray-900">
                          {progressData?.outputName}
                        </span>
                      </div>
                      <div className="flex items-center gap-8">
                        <Button outline color="blue" onClick={() => fileInputRef.current?.click()}>
                          <span className="flex items-center gap-2">
                            <Plus className="w-4 h-4" />
                            {t('merge.selectFiles')}
                          </span>
                        </Button>
                        <Button onClick={handleView}>
                          <span className="flex items-center gap-2">
                            <Folder className="w-4 h-4" />
                            {isWin ? t('merge.viewInWin') : t('merge.viewInMac')}
                          </span>
                        </Button>
                      </div>
                    </div>
                  )
                : (
                    <div className="flex flex-col gap-3 items-center">
                      {/* <TbFileUpload className="w-23 h-23 text-gray-500 stroke-1" /> */}
                      <p className="text-sm font-medium text-gray-600">{t('merge.noData')}</p>
                      <Button className="w-fit" onClick={() => fileInputRef.current?.click()}>
                        <span className="flex items-center gap-2">
                          <Plus className="w-4 h-4" />
                          {t('merge.selectFiles')}
                        </span>
                      </Button>
                    </div>
                  )}

            </div>
          )}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="video/*,audio/*,.srt,.ass,.vtt"
        className="hidden"
        onChange={handleFileChange}
      />
      {progressData?.status === 'merging' && (
        <div className="absolute top-0 z-10 w-full h-full flex items-center justify-center bg-black/80">
          <div className="flex flex-col items-center">
            <div className="w-full">
              <Progress
                percent={progressData.progress}
                type="circle"
                size={100}
                strokeWidth={7}
                showInfo={(
                  <p className="text-lg text-white font-bold">
                    {Math.floor(progressData.progress!)}
                    %
                  </p>
                )}
              />
            </div>
            <p className="mt-4 mb-8 text-lg font-bold text-white">{t('merge.merging')}</p>
            <Button color="gray" onClick={cancelMerge}>{t('merge.cancel')}</Button>
          </div>
        </div>
      )}
      {progressData?.status === 'error' && (
        <div className="absolute top-0 z-10 w-full h-full flex items-center justify-center bg-black/60">
          <div className="flex flex-col items-center text-white">
            <ExclamationCircle className="w-33 h-33 text-red-700 stroke-2" />
            <p className="text-lg font-bold my-4">{t('merge.failed')}</p>
            <span className="text-sm text-center">{progressData.error}</span>
            <div className="mt-8 flex items-center gap-8">
              <Button color="gray" onClick={clearProgressData}>{t('merge.close')}</Button>
              <Button color="dark" onClick={handleRetry}>{t('merge.retry')}</Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default MergePage
