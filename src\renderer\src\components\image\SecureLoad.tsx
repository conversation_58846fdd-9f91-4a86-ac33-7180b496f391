import SkeletonImageSvgIcon from '@/assets/skeleton-image.svg?react'
import { Spinner } from 'flowbite-react'
import { useMemo } from 'react'

interface ImageSecureLoadProps {
  url?: string
  loading?: boolean
  showSuffix?: boolean
  suffix?: string
  isLive?: boolean
  children?: React.ReactNode
}

function ImageSecureLoad({
  url,
  loading,
  children,
  showSuffix,
  suffix,
  isLive,
}: ImageSecureLoadProps) {
  const content = useMemo(() => {
    // 一个是图片加载，一个是外部受控加载
    if (loading) {
      return (
        <Spinner
          size="xl"
          theme={{
            color: { info: 'fill-[#1C64F2]' },
            light: { off: { base: 'text-[#374151]' } },
          }}
        />
      )
    }

    if (url) {
      return (
        <div
          className="w-full h-full bg-contain bg-no-repeat bg-center rounded object-contain bg-gray-700"
          style={{
            backgroundImage: `url(${url})`,
          }}
        />
      )
    }

    if (isLive) {
      return (
        <div className="w-full h-full flex items-center justify-center rounded bg-gray-300 text-base text-gray-600">
          LIVE
        </div>
      )
    }

    if (showSuffix) {
      return (
        <div className="w-full h-full flex items-center justify-center rounded bg-gray-300 text-base text-gray-600">
          {suffix}
        </div>
      )
    }

    return <SkeletonImageSvgIcon className="w-12 h-12 fill-[#bfbfbf]" />
  }, [url, loading, showSuffix, suffix, isLive])

  return (
    <div className="relative group shrink-0 w-[128px] h-[72px] bg-[#D9D9D9] flex items-center justify-center rounded">
      {content}
      {children}
    </div>
  )
}

export default ImageSecureLoad
