import type { AudioFormat, VideoFormat } from '@/store/download'
import type { Platform } from '@/utils/environment'

export interface AuthSite {
  name: string
  url: string
  authUrl: string
  isAuthorized: boolean
  enableDelete?: boolean
}

export interface HostAlias {
  hostname: string
  redirect: string
}

export interface FormatPlatformMap {
  video: Record<Platform, VideoFormat>
  audio: Record<Platform, AudioFormat>
}
