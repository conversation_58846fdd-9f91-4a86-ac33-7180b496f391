import type { DownloadItem } from '@main/types'
import type { ExtFilter, MediaResource, RegexFilter, SnifferMediaDownloadInfo, TypeFilter } from '@main/types/sniffer'
import type { RendererHandlers } from '../renderer-handlers'
import { getRendererHandlers } from '@egoist/tipc/main'
import { defaultExtFilters, defaultRegexFilters, defaultTypeFilters } from '@main/constants/sniffer'
import { taskStatus } from '@main/constants/status'
import { settingStore, snifferStore } from '@main/lib/store'
import { getMainWindow } from '@main/window'
import { webContents } from 'electron'
import { v4 as uuidv4 } from 'uuid'
import taskService from './task'

/**
 * 资源嗅探服务
 */
class ResourceSnifferService {
  // 存储所有嗅探到的媒体资源
  private mediaResources: Map<string, MediaResource> = new Map()

  // 存储URL到资源ID的映射，用于去重
  private urlToResourceId: Map<string, string> = new Map()

  // 存储请求头信息，用于后续匹配响应
  private requestHeadersMap: Map<number, any> = new Map()

  // 是否已初始化
  private initialized = false

  // 文件扩展名过滤规则
  private extFilters: ExtFilter[] = [...defaultExtFilters]

  // MIME类型过滤规则
  private typeFilters: TypeFilter[] = [...defaultTypeFilters]

  // 正则表达式过滤规则
  private regexFilters: RegexFilter[] = [...defaultRegexFilters]

  /**
   * 初始化资源嗅探服务
   */
  public init(): void {
    if (this.initialized) {
      return
    }

    // 从设置中加载过滤规则
    this.loadFilterRules()

    this.initialized = true
    console.log('资源嗅探服务已初始化')
  }

  /**
   * 为特定的WebContents设置资源嗅探
   * @param webContentsId WebContents的ID
   */
  public setupForWebContents(webContentsId: number): void {
    const targetWebContents = webContents.fromId(webContentsId)
    if (!targetWebContents) {
      console.error(`找不到ID为${webContentsId}的WebContents`)
      return
    }

    this.setupSessionListeners(targetWebContents.session)

    // 设置window.open拦截
    this.setupWindowOpenIntercept(targetWebContents)

    console.log(`已为WebContents(ID: ${webContentsId})设置资源嗅探和window.open拦截`)
  }

  /**
   * 设置window.open拦截
   * @param targetWebContents 目标WebContents
   */
  private setupWindowOpenIntercept(targetWebContents: Electron.WebContents): void {
    // 设置webContents的setWindowOpenHandler处理程序
    targetWebContents.setWindowOpenHandler((details) => {
      console.log(`拦截到window.open请求: ${details.url}`)

      try {
        // 通知渲染进程有新的window.open请求
        this.notifyWindowOpenIntercept(
          details.url,
        )
      }
      catch (error) {
        console.error('通知window.open拦截失败:', error)
      }

      // 阻止默认的window.open行为
      return { action: 'deny' }
    })

    console.log(`已为WebContents(ID: ${targetWebContents.id})设置window.open拦截`)
  }

  /**
   * 为会话设置请求监听器
   * @param session 要监听的会话
   */
  private setupSessionListeners(session: Electron.Session): void {
    // 监听请求发送前，保存请求头信息
    session.webRequest.onBeforeSendHeaders((details, callback) => {
      if (details.requestHeaders) {
        this.requestHeadersMap.set(details.id, {
          requestHeaders: details.requestHeaders,
          url: details.url,
          method: details.method,
          timestamp: Date.now(),
          resourceType: details.resourceType,
        })
      }
      callback({ requestHeaders: details.requestHeaders })
    })

    // 监听响应头，分析资源类型
    session.webRequest.onHeadersReceived((details, callback) => {
      try {
        this.processResponse(details)
      }
      catch (error) {
        console.error('资源嗅探处理错误:', error)
      }

      callback({ responseHeaders: details.responseHeaders })
    })

    // 请求完成或失败时清理请求头信息
    session.webRequest.onCompleted((details) => {
      this.requestHeadersMap.delete(details.id)
    })

    session.webRequest.onErrorOccurred((details) => {
      this.requestHeadersMap.delete(details.id)
    })
  }

  /**
   * 处理响应数据
   * @param details 响应详情
   */
  private processResponse(details: Electron.OnHeadersReceivedListenerDetails): void {
    const requestInfo = this.requestHeadersMap.get(details.id)
    const url = details.url

    // 过滤本地地址请求
    if (this.isLocalRequest(url)) {
      return
    }

    // 应用正则表达式过滤规则
    const regexResult = this.applyRegexFilters(url)
    if (regexResult.isBlocked) {
      return
    }

    // 获取资源基本信息
    const normalizedHeaders = this.normalizeResponseHeaders(details.responseHeaders)
    const contentType = normalizedHeaders['content-type']
    const contentLength = normalizedHeaders['content-length']
    const contentDisposition = normalizedHeaders['content-disposition']

    // 解析URL获取文件名和扩展名
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    let fileName = pathname.split('/').pop() || ''
    let fileExt = fileName.split('.').pop()?.toLowerCase() || ''

    // 如果正则过滤规则指定了扩展名，则使用该扩展名
    if (regexResult.matchedExt) {
      fileExt = regexResult.matchedExt
    }

    // 从Content-Disposition中提取文件名
    let dispositionMatch = null
    if (contentDisposition) {
      dispositionMatch = contentDisposition.match(/filename=["']?([^"']+)["']?/)
      if (dispositionMatch && dispositionMatch[1]) {
        fileName = dispositionMatch[1]
        const extFromDisposition = fileName.split('.').pop()?.toLowerCase()
        if (extFromDisposition) {
          fileExt = extFromDisposition
        }
      }
    }

    // 3. 文件扩展名检查
    const extResult = this.checkFileExtension(fileExt, Number.parseInt(contentLength) || 0)

    // 4. MIME类型检查
    const mimeResult = this.checkMimeType(contentType, Number.parseInt(contentLength) || 0)

    // 如果contentType是video/或者audio/，则将文件扩展名设置为contentType中的内容
    if (contentType.startsWith('video/') || contentType.startsWith('audio/')) {
      fileExt = contentType.split('/')[1]
    }

    // 5. 特殊处理：请求类型为media的资源
    const isMediaRequest = details.resourceType === 'media'
    // 特殊处理条件，不记录ts格式
    const isSpecialFormatRequest = fileExt === 'ts'
    // 如果资源通过任一过滤条件，则记录
    if ((extResult || mimeResult || isMediaRequest) && !isSpecialFormatRequest) {
      const mediaInfo: MediaResource = {
        url: details.url,
        contentType,
        contentLength: Number.parseInt(contentLength) || null,
        fileName,
        fileExt,
        timestamp: Date.now(),
        requestHeaders: this.formatRequestHeaders(requestInfo?.requestHeaders || {}),
        responseHeaders: details.responseHeaders,
        method: details.method,
        resourceType: details.resourceType,
        referrer: requestInfo?.url || '',
        tabId: details.webContentsId,
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      }

      // 存储媒体资源，测试嗅探时可打开，使用getSniffedResources获取，clearSniffedResources清空
      this.mediaResources.set(mediaInfo.id, mediaInfo)

      // URL去重处理：检查是否已存在相同URL的资源
      const existingResourceId = this.urlToResourceId.get(mediaInfo.url)
      if (existingResourceId && existingResourceId !== mediaInfo.id) {
        // 如果存在相同URL的资源且ID不同，删除旧资源
        this.mediaResources.delete(existingResourceId)
        console.log('移除重复URL的旧资源:', existingResourceId)
      }
      // 更新URL到资源ID的映射
      this.urlToResourceId.set(mediaInfo.url, mediaInfo.id)

      // 输出日志
      // console.log('检测到媒体资源:', mediaInfo)

      // 通知渲染进程
      this.notifyRenderer(mediaInfo)
    }

    // 清理过期的请求头信息（5分钟前的）
    this.cleanupRequestHeaders()
  }

  /**
   * 判断是否为本地请求
   */
  private isLocalRequest(url: string): boolean {
    try {
      const parsedUrl = new URL(url)

      // 检查是否为localhost或127.0.0.1
      if (parsedUrl.hostname === 'localhost'
        || parsedUrl.hostname === '127.0.0.1'
        || parsedUrl.hostname.startsWith('192.168.')
        || parsedUrl.hostname.startsWith('10.')
        || parsedUrl.hostname === '[::1]') {
        return true
      }

      // 检查是否为file://协议
      if (parsedUrl.protocol === 'file:') {
        return true
      }

      // 检查特定的文件扩展名，通常为前端开发文件
      const fileExtensions = ['.js', '.css', '.html', '.ts', '.tsx', '.jsx', '.vue', '.map', '.json', '.wasm']
      const pathname = parsedUrl.pathname.toLowerCase()
      if (fileExtensions.some(ext => pathname.endsWith(ext))) {
        // 如果是特定扩展名，再检查路径中是否包含典型的前端目录名
        const frontendDirIndicators = ['/src/', '/assets/', '/dist/', '/public/', '/static/', '/js/', '/css/']
        if (frontendDirIndicators.some(dir => pathname.includes(dir))) {
          return true
        }
      }

      return false
    }
    catch (error) {
      console.error('解析URL失败:', error)
      return false
    }
  }

  /**
   * 应用正则表达式过滤规则
   * @returns 包含是否屏蔽和匹配的扩展名
   */
  private applyRegexFilters(url: string): { isBlocked: boolean, matchedExt: string } {
    for (const filter of this.regexFilters) {
      if (!filter.enabled)
        continue

      const regex = new RegExp(filter.pattern, filter.flags)
      if (regex.test(url)) {
        if (filter.isBlocking) {
          // 直接屏蔽该URL
          return { isBlocked: true, matchedExt: '' }
        }
        return { isBlocked: false, matchedExt: filter.specifiedExt }
      }
    }
    return { isBlocked: false, matchedExt: '' }
  }

  /**
   * 检查文件扩展名是否符合过滤规则
   */
  private checkFileExtension(fileExt: string, contentLength: number): boolean {
    for (const filter of this.extFilters) {
      if (!filter.enabled)
        continue

      if (filter.ext.toLowerCase() === fileExt.toLowerCase()) {
        // 如果没有大小限制或者文件大小符合要求
        // return (filter.minSize === 0 && contentLength > 0) || contentLength >= filter.minSize * 1024
        if (filter.minSize === 0) {
          return contentLength > 0
        }
        else {
          return contentLength >= filter.minSize * 1024
        }
      }
    }
    return false
  }

  /**
   * 检查MIME类型是否符合过滤规则
   */
  private checkMimeType(contentType: string, contentLength: number): boolean {
    if (!contentType)
      return false

    const lowerContentType = contentType.toLowerCase()

    for (const filter of this.typeFilters) {
      if (!filter.enabled)
        continue

      // 处理通配符模式
      if (filter.mime.endsWith('/*')) {
        const prefix = filter.mime.slice(0, -2)
        if (lowerContentType.startsWith(`${prefix}/`)) {
          return filter.minSize === 0 || contentLength >= filter.minSize * 1024
        }
      }
      // 精确匹配
      else if (lowerContentType === filter.mime.toLowerCase()) {
        return filter.minSize === 0 || contentLength >= filter.minSize * 1024
      }
    }

    return false
  }

  /**
   * 格式化请求头
   */
  private formatRequestHeaders(headers: Record<string, any>): Record<string, string> {
    const result: Record<string, string> = {}

    for (const key in headers) {
      if (typeof headers[key] === 'string') {
        result[key.toLowerCase()] = headers[key]
      }
    }

    return result
  }

  /**
   * 清理过期的请求头信息
   */
  private cleanupRequestHeaders(): void {
    const now = Date.now()
    this.requestHeadersMap.forEach((value, key) => {
      if (now - value.timestamp > 5 * 60 * 1000) {
        this.requestHeadersMap.delete(key)
      }
    })
  }

  /**
   * 从设置中加载过滤规则
   */
  private loadFilterRules(): void {
    try {
      const savedFilters = snifferStore.get('resourceSnifferFilters')

      if (savedFilters) {
        if (savedFilters.extFilters && Array.isArray(savedFilters.extFilters))
          this.extFilters = savedFilters.extFilters
        if (savedFilters.typeFilters && Array.isArray(savedFilters.typeFilters))
          this.typeFilters = savedFilters.typeFilters
        if (savedFilters.regexFilters && Array.isArray(savedFilters.regexFilters))
          this.regexFilters = savedFilters.regexFilters
      }

      console.log('成功从设置中加载过滤规则')
    }
    catch (error) {
      console.error('加载资源嗅探过滤规则失败:', error)
      // 如果加载失败，使用默认值
      this.extFilters = [...defaultExtFilters]
      this.typeFilters = [...defaultTypeFilters]
      this.regexFilters = [...defaultRegexFilters]
    }
  }

  /**
   * 保存过滤规则到设置
   */
  public saveFilterRules(): void {
    try {
      snifferStore.set('resourceSnifferFilters', {
        extFilters: this.extFilters,
        typeFilters: this.typeFilters,
        regexFilters: this.regexFilters,
      })
      console.log('过滤规则已保存到设置')
    }
    catch (error) {
      console.error('保存资源嗅探过滤规则失败:', error)
    }
  }

  /**
   * 通知渲染进程有新的媒体资源
   */
  private notifyRenderer(mediaInfo: MediaResource): void {
    try {
      const handlers = getRendererHandlers<RendererHandlers>(getMainWindow().webContents)
      // 通过handlers发送消息到渲染进程
      handlers.onResourceSniffed.send(mediaInfo)
    }
    catch (error) {
      console.error('通知渲染进程失败:', error)
    }
  }

  /**
   * 通知渲染进程有新的window.open请求
   * @param data window.open拦截数据
   */
  private notifyWindowOpenIntercept(url: string): void {
    try {
      const handlers = getRendererHandlers<RendererHandlers>(getMainWindow().webContents)
      // 通过handlers发送消息到渲染进程
      handlers.onWindowOpenIntercept.send(url)
      console.log('已通知渲染进程window.open拦截:', url)
    }
    catch (error) {
      console.error('通知渲染进程window.open拦截失败:', error)
    }
  }

  /**
   * 获取所有嗅探到的媒体资源
   */
  public getAllMediaResources(): MediaResource[] {
    return Array.from(this.mediaResources.values())
  }

  /**
   * 清除所有嗅探到的媒体资源
   */
  public clearAllMediaResources(): void {
    this.mediaResources.clear()
    this.urlToResourceId.clear()
  }

  /**
   * 更新文件扩展名过滤规则
   */
  public updateExtFilters(filters: ExtFilter[]): void {
    this.extFilters = filters
    this.saveFilterRules()
  }

  /**
   * 更新MIME类型过滤规则
   */
  public updateTypeFilters(filters: TypeFilter[]): void {
    this.typeFilters = filters
    this.saveFilterRules()
  }

  /**
   * 更新正则表达式过滤规则
   */
  public updateRegexFilters(filters: RegexFilter[]): void {
    this.regexFilters = filters
    this.saveFilterRules()
  }

  /**
   * 获取文件扩展名过滤规则
   */
  public getExtFilters(): ExtFilter[] {
    return this.extFilters
  }

  /**
   * 获取MIME类型过滤规则
   */
  public getTypeFilters(): TypeFilter[] {
    return this.typeFilters
  }

  /**
   * 获取正则表达式过滤规则
   */
  public getRegexFilters(): RegexFilter[] {
    return this.regexFilters
  }

  /**
   * 下载嗅探到的资源
   * @param snifferMediaDownload 需要下载的媒体资源信息
   * @returns 创建的任务ID数组
   */
  public async downloadSniffedResource(snifferMediaDownload: SnifferMediaDownloadInfo): Promise<string> {
    try {
      // 获取当前设置
      const setting = settingStore.store

      const { url, title, fileExt, httpHeaders } = snifferMediaDownload
      // 移除httpHeaders中的range，避免部分下载
      const headers = { ...httpHeaders }
      delete headers.range

      // 为每个资源创建一个唯一的任务ID
      const taskId = uuidv4()

      // 创建下载项
      const downloadItem: DownloadItem = {
        url,
        headers,
        ext: fileExt,
      }

      // 创建任务对象
      const task = {
        id: taskId,
        text: title,
        url,
        filePath: null,
        fileSize: null,
        taskStatus: taskStatus.readyDownload,
        thumbnail: null,
        extension: fileExt,
        resolutionWidth: null,
        resolutionHeight: null,
        bitrate: null,
        duration: null,
        errorStatus: null,
        errorMessage: null,
        errorAction: null,
        tempTask: JSON.stringify({
          text: title || url,
          thumbnail: null,
          items: [downloadItem],
          setting,
        }),
        requestHeaders: JSON.stringify(headers),
        createdAt: Date.now(),
        updatedAt: Date.now(),
      }

      // 保存任务到数据库
      await taskService.saveSnifferTask(task)

      // 直接开始下载，跳过解析步骤
      taskService.downloadWithSnapfile(taskId, [downloadItem], setting)

      return taskId
    }
    catch (error) {
      console.error('下载嗅探资源失败:', error)
      throw error
    }
  }

  /**
   * 将响应头的键名统一转换为小写
   */
  private normalizeResponseHeaders(headers: Record<string, string[]>): Record<string, string> {
    const normalized: Record<string, string> = {}

    for (const key in headers) {
      const lowerKey = key.toLowerCase()
      normalized[lowerKey] = headers[key]?.[0] || ''
    }

    return normalized
  }
}

// 导出单例
export default new ResourceSnifferService()
