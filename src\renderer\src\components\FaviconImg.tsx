import { encrypt } from '@/utils/browser-aes'
import { useEffect, useState } from 'react'

function FaviconImg({ domain, className, onError }: { domain: string, className?: string, onError?: () => void }) {
  const [favicon, setFavicon] = useState<string>('')

  const getFavicon = async () => {
    const payload = await encrypt(JSON.stringify({
      domain,
      size: 32,
    }), 'LEt9s53mN19Q0TS6')
    try {
      const response = await fetch(`https://api.snapany.com/desktop/favicon/${payload}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      })
      if (response.ok) {
        setFavicon(response.url)
      }
    }
    catch (error) {
      console.error('获取favicon失败:', error)
    }
  }

  useEffect(() => {
    if (domain) {
      getFavicon()
    }
  }, [domain])

  return (
    <>
      {favicon && (
        <img
          className={className}
          src={favicon}
          alt="favicon"
          onError={(e) => {
            e.currentTarget.style.display = 'none'
            onError?.()
          }}
        />
      )}
    </>
  )
}

export default FaviconImg
