{"name": "snapany-desktop", "productName": "snapany", "version": "0.8.1", "description": "SnapAny - 视频下载工具", "author": "gongyi<PERSON>hi", "license": "ISC", "homepage": "https://snapany.com", "keywords": [], "main": "out/main/main.js", "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "electron-vite dev", "build:mac": "pnpm build:mac-arm64 && pnpm build:mac-x64", "build:mac-x64": "node scripts/copy-binaries-x64.mjs && electron-vite build && electron-builder --mac --x64", "build:mac-arm64": "node scripts/copy-binaries-arm64.mjs && electron-vite build && electron-builder --mac --arm64", "build:win": "electron-vite build && electron-builder --win  --x64", "start": "node ./scripts/start.mjs", "clean": "rimraf dist dist-electron release", "build": "node ./scripts/build.mjs && electron-builder", "make:win": "node ./scripts/build.mjs  && electron-builder --win --x64", "make:mac": "npm run make:mac-x64 && npm run make:mac-arm64", "make:mac-x64": "npm run install-deps && node scripts/copy-binaries-x64.mjs && node ./scripts/build.mjs && electron-builder --mac --x64", "make:mac-arm64": "npm run install-deps && node scripts/copy-binaries-arm64.mjs && node ./scripts/build.mjs && electron-builder --mac --arm64", "make:win32": "node ./scripts/build.mjs && electron-builder --win --ia32", "make:win64": "npm run install-deps && node ./scripts/build.mjs && electron-builder --win --x64", "make:win-all": "npm run install-deps && node ./scripts/build.mjs && electron-builder --win --ia32 --x64", "install-deps": "node scripts/install-ytdlp.mjs && node scripts/install-ffmpeg.mjs && node scripts/install-ffprobe.mjs", "lint": "eslint", "lint:fix": "eslint --fix", "test": "vitest", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@aptabase/electron": "^0.3.1", "@egoist/tipc": "^0.3.2", "@electron-toolkit/preload": "^3.0.1", "@sentry/electron": "^5.9.0", "ahooks": "^3.8.4", "better-sqlite3": "^11.9.1", "classnames": "^2.5.1", "drizzle-kit": "^0.30.6", "drizzle-orm": "^0.41.0", "electron-log": "^5.4.1", "electron-store": "8.2.0", "file-type": "16.5.4", "flowbite-react": "^0.10.2", "flowbite-react-icons": "^1.3.0", "fluent-ffmpeg": "^2.1.3", "i18next": "^24.2.1", "i18next-browser-languagedetector": "^8.0.2", "linkifyjs": "^4.2.0", "mime-types": "^2.1.35", "node-machine-id": "^1.1.12", "normalize-url": "^8.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.4.0", "react-icons": "^5.4.0", "react-router-dom": "^7.2.0", "react-virtuoso": "^4.12.5", "swr": "^2.3.2", "tailwind-merge": "^3.2.0", "tldjs": "^2.3.1", "uuid": "^11.0.3", "zustand": "^5.0.3"}, "devDependencies": {"@antfu/eslint-config": "^4.11.0", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@electron/notarize": "^2.5.0", "@eslint-react/eslint-plugin": "^1.40.0", "@tailwindcss/vite": "^4.0.8", "@types/fluent-ffmpeg": "^2.1.27", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/tldjs": "^2.3.4", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "cross-env": "^7.0.3", "electron": "^32.3.3", "electron-builder": "^25.1.8", "electron-vite": "^3.1.0", "eslint": "^9.23.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "fs-extra": "^11.3.0", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "nodemon": "^3.1.9", "prettier": "^3.5.2", "rimraf": "^6.0.1", "sass": "^1.85.0", "tailwindcss": "^4.0.8", "typescript": "^5.7.2", "vite": "^6.1.1", "vite-plugin-electron": "^0.29.0", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.1.1"}, "pnpm": {"onlyBuiltDependencies": ["electron", "esbuild", "better-sqlite3"]}}