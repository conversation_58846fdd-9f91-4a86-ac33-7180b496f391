import type { DownloadAppModalRef } from '@/components/DownloadAppModal'
import { client, handlers } from '@/client'
import CheckTaskModal from '@/components/CheckTaskModal'
import CloseWindowTipModal from '@/components/CloseWindowTipModal'
import DownloadAppModal from '@/components/DownloadAppModal'
import DownloadAppTipModal from '@/components/DownloadAppTipModal'
import { SettingsContext } from '@/contexts/SettingsContext'
import { useFormatStore } from '@/store/format'
import { useMergeStore } from '@/store/merge'
import { useTaskStore } from '@/store/task'
import { useUpdateStore } from '@/store/update'
import { ApplicationIconWrap } from '@renderer/client/application-compose'
import SimpleModal from '@renderer/components/modal/SimpleModal'
import { CONTACT_SOCIAL } from '@renderer/constants/contact'
import LayoutItem from '@renderer/layouts/LayoutItem'
import SettingLayout from '@renderer/layouts/SettingLayout'
import NetworkPage from '@renderer/pages/Network'
import routers, { applicationMenuRouter } from '@renderer/routes/application'
import { Button } from 'flowbite-react'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { LuSettings } from 'react-icons/lu'
import { useLocation, useRoutes } from 'react-router-dom'

function ApplicationLayout() {
  const { t } = useTranslation()
  const location = useLocation()
  const [showSettings, setShowSettings] = useState(false)

  const openSettings = () => {
    setShowSettings(true)
  }
  const downloadAppRef = useRef<DownloadAppModalRef>(null)

  // 获取各种 store 中的方法
  const {
    isNeedOpen,
    isFinishDownload,
    isLaterInstall,
    isDownloadError,
    checkForUpdate,
    checkPackageExist,
    listenUpdate,
    unlisten,
    installApp,
  } = useUpdateStore()

  const { fetchTask, checkHasUnfinishedTask, setIsOpenCloseWindowModal, closeWindow, registerSync, clearSync } = useTaskStore()
  const { listenConvert, clearListenConvert } = useFormatStore()
  const { listenMerge, clearListenMerge } = useMergeStore()

  const routerElements = useRoutes(routers)

  const checkAppUpdate = async () => {
    await checkForUpdate()
    await checkPackageExist()
  }

  const checkUnfinishedTask = async () => {
    await fetchTask()
    checkHasUnfinishedTask()
  }

  // 组件挂载时初始化各种监听和检查
  useEffect(() => {
    console.log('Application 组件已加载，初始化全局监听和检查')

    checkAppUpdate()
    checkUnfinishedTask()

    // 注册各种事件监听
    listenUpdate()
    registerSync()
    listenConvert()
    listenMerge()

    // 注册应用关闭事件监听
    const unlistenClose = handlers.onAppClose.listen((message) => {
      setIsOpenCloseWindowModal(message)
      if (!message) {
        closeWindow()
      }
    })

    // 组件卸载时清理所有监听器
    return () => {
      console.log('Application 组件卸载，清理全局监听')
      unlisten?.()
      clearListenMerge()
      unlistenClose?.()
      clearSync()
      clearListenConvert()
    }
  }, [])

  // 监听更新提示弹窗
  useEffect(() => {
    if (isNeedOpen) {
      downloadAppRef.current?.setShowModal(true)
    }
  }, [isNeedOpen])

  // 监听下载完成或失败的状态
  useEffect(() => {
    if (isFinishDownload || isDownloadError) {
      downloadAppRef.current?.setShowModal(false)
    }
  }, [isFinishDownload, isDownloadError])

  if (!routerElements) {
    return null
  }

  return (
    <SettingsContext.Provider value={{ openSettings }}>
      <div className="flex h-screen w-full overflow-hidden">
        <aside className="w-[128px] flex flex-col shrink-0 bg-white gap-1 pb-3">
          <nav>
            {applicationMenuRouter.map(
              router =>
                router.path
                && router.key && (
                  <LayoutItem icon={router.icon} to={router.path} key={router.key}>
                    {t(router.key)}
                  </LayoutItem>
                ),
            )}
          </nav>
          <footer className="gap-1.5 grow justify-center items-end flex flex-wrap px-4 content-end">
            <div>{isLaterInstall && <Button onClick={installApp}>{t('update.upgradeNow')}</Button>}</div>
            <div className="flex items-center grow gap-1">
              {CONTACT_SOCIAL.map(({ key, icon: Icon, link }) => (
                <ApplicationIconWrap
                  key={key}
                  content={t(key)}
                  onClick={() => client.openExternalLink({ url: link })}
                >
                  <Icon />
                </ApplicationIconWrap>
              ))}
              <ApplicationIconWrap content={t('menu.settings')} onClick={() => setShowSettings(true)}>
                <LuSettings />
              </ApplicationIconWrap>
            </div>
          </footer>
        </aside>
        <main className="bg-[#F3F4F6] grow">
          {routerElements}
          <div className={`w-full h-full ${location.pathname === '/network' ? 'block' : 'hidden'}`}>
            <NetworkPage />
          </div>
        </main>

        <SimpleModal open={showSettings} zIndex={100} onClose={() => setShowSettings(false)}>
          <SettingLayout />
        </SimpleModal>
        <DownloadAppModal ref={downloadAppRef} />
        <DownloadAppTipModal />
        <CheckTaskModal />
        <CloseWindowTipModal />
      </div>
    </SettingsContext.Provider>
  )
}

export default ApplicationLayout
