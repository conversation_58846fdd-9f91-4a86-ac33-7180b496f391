import type { RendererHandlers } from '@main/renderer-handlers'
import type { TaskStatus, TempTask } from '@main/types'
import path from 'node:path'
import { getRendererHandlers } from '@egoist/tipc/main'
import { errorMessageEnum, taskStatus } from '@main/constants/status'
import { logError, logInfo } from '@main/lib/logger'
import { settingStore } from '@main/lib/store'
import snapfileService from '@main/service/snapfile'
import taskService, { tempTaskMap } from '@main/service/task'
import ytDlpService from '@main/service/yt-dlp'
import { checkDirectoryExists, checkFileExists, forceRemoveWithExec } from '@main/utils'
import { getMainWindowOrCreate } from '@main/window'
import { app } from 'electron'
import { t } from './instance'

export const taskRoute = {
  // 开始下载任务
  startDownload: t.procedure
    .input<{ urls: string[] }>()
    .action(async ({ input }) => {
      logInfo('开始下载任务', {
        urls: input.urls,
        downloadPath: settingStore.path,
      })
      const taskList = await taskService.saveTaskByUrls(input.urls)
      const setting = settingStore.store
      // JSON处理 获取需要下载的下载项列表
      for (const task of taskList) {
        const asyncTasks = async () => {
          const ytdlpResp = await taskService.parseTask(task)
          if (!ytdlpResp) {
            return
          }
          const downloadItems = await taskService.getNeedDownloadItems(task, ytdlpResp, setting)
          taskService.downloadWithSnapfile(task.id, downloadItems, setting)
        }
        asyncTasks()
      }
      return taskList
    }),
  // 恢复任务下载
  resumeDownload: t.procedure
    .input<{ taskId: string }>()
    .action(async ({ input }) => {
      const { taskId } = input
      const handlers = getRendererHandlers<RendererHandlers>(getMainWindowOrCreate().webContents)
      try {
        const task = await taskService.getTaskById(taskId)
        handlers.onDownloadProgress.send({
          ...task,
          taskStatus: task.taskStatus as TaskStatus,
          taskId: task.id,
          eta: null,
          totalSize: task.fileSize,
          downloadedSize: null,
          speed: null,
          errorAction: null,
          errorStatus: null,
          errorMessage: null,
          isLive: task.isLive || false,
        })
        const tempTask = JSON.parse(task.tempTask) as TempTask
        if (task.taskStatus === taskStatus.extracting) {
          const asyncTask = async () => {
            const ytdlpResp = await taskService.parseTask(task)
            const downloadItems = await taskService.getNeedDownloadItems(task, ytdlpResp, tempTask.setting)
            taskService.downloadWithSnapfile(task.id, downloadItems, tempTask.setting)
          }
          asyncTask()
        }
        else if (task.taskStatus !== taskStatus.failed) {
          taskService.downloadWithSnapfile(task.id, tempTask.items, tempTask.setting)
        }
      }
      catch (error) {
        console.warn('恢复任务下载失败', error)
      }
    }),
  // 获取任务列表
  getTaskList: t.procedure
    .action(async () => {
      const taskList = await taskService.getTaskList()
      return taskList
    }),
  // 删除任务
  deleteTask: t.procedure
    .input<{ taskId: string }>()
    .action(async ({ input }) => {
      try {
        const { taskId } = input
        // 始终尝试终止yt-dlp进程，无论任务是否存在
        ytDlpService.cancelYtDlpProcess(taskId)

        // 删除文件
        const task = await taskService.getTaskById(taskId)
        if (task) {
          await taskService.deleteTask(taskId)
          if (task.taskStatus !== taskStatus.completed
            && task.taskStatus !== taskStatus.failed) {
            snapfileService.cancelTask(taskId)
          }
          // 删除临时任务
          if (task.tempTask) {
            try {
              const tempTask = JSON.parse(task.tempTask) as TempTask
              const filePath = path.join(tempTask.setting.downloadPath, `.${app.getName()}`, taskId)
              const exist = await checkDirectoryExists(filePath)
              if (exist) {
                await forceRemoveWithExec(filePath)
              }
              tempTaskMap.delete(taskId)
            }
            catch (err) {
              logError('解析tempTask失败', {
                taskId,
                error: err instanceof Error ? err.message : String(err),
                stack: err instanceof Error ? err.stack : undefined,
              })
            }
          }
        }
      }
      catch (err) {
        logError('删除任务失败', {
          taskId: input.taskId,
          error: err instanceof Error ? err.message : String(err),
          stack: err instanceof Error ? err.stack : undefined,
        })
      }
    }),
  // 删除所有任务
  deleteTaskList: t.procedure
    // isDeleteFile:是否删除文件
    // isDeleteDownloading:是否删除下载中的任务（包含提取中、下载中、转换中、准备下载）
    .input<{ isDeleteFile: boolean, isDeleteDownloading: boolean }>()
    .action(async ({ input }) => {
      try {
        const taskList = await taskService.deleteTaskList(input.isDeleteDownloading)
        for (const task of taskList) {
          try {
            // 无论任务状态如何，都尝试取消yt-dlp进程
            ytDlpService.cancelYtDlpProcess(task.id)

            if (task.taskStatus !== taskStatus.completed) {
              snapfileService.cancelTask(task.id)
            }

            // if (input.isDeleteFile && task.filePath) {
            //   // 将task.filePath按照逗号分割成数组
            //   const filePathList = task.filePath.split(',')
            //   for (const filePath of filePathList) {
            //     if (filePath && await checkFileExists(filePath)) {
            //       await forceRemoveWithExec(filePath)
            //     }
            //   }
            // }
            if (input.isDeleteFile && task.filePath) {
              if (await checkFileExists(task.filePath)) {
                await forceRemoveWithExec(task.filePath)
              }
            }

            // 删除临时目录
            if (task.tempTask) {
              try {
                const tempTask = JSON.parse(task.tempTask) as TempTask
                const filePath = path.join(tempTask.setting.downloadPath, `.${app.getName()}`, task.id)
                const exist = await checkDirectoryExists(filePath)
                if (exist) {
                  await forceRemoveWithExec(filePath)
                }
                tempTaskMap.delete(task.id)
              }
              catch (err) {
                logError('解析tempTask失败', {
                  taskId: task.id,
                  error: err instanceof Error ? err.message : String(err),
                  stack: err instanceof Error ? err.stack : undefined,
                })
              }
            }
          }
          catch (err) {
            logError('处理任务失败', {
              taskId: task.id,
              error: err instanceof Error ? err.message : String(err),
              stack: err instanceof Error ? err.stack : undefined,
            })
            // 继续处理下一个任务，不中断整个流程
            continue
          }
        }
      }
      catch (err) {
        logError('批量删除任务失败', {
          error: err instanceof Error ? err.message : String(err),
          stack: err instanceof Error ? err.stack : undefined,
        })
      }
    }),
  // 中断任务
  interruptTasks: t.procedure
    .action(async () => {
      try {
        const taskList = await taskService.getInterruptTasks()
        for (const task of taskList) {
          try {
            // 无论任务状态如何，都尝试取消yt-dlp进程
            ytDlpService.cancelYtDlpProcess(task.id)
            snapfileService.cancelTask(task.id)

            await taskService.updateTask(task.id, {
              errorMessage: errorMessageEnum.cancel,
            })
          }
          catch (err) {
            logError('中断任务失败', {
              taskId: task.id,
              error: err instanceof Error ? err.message : String(err),
              stack: err instanceof Error ? err.stack : undefined,
            })
            // 继续处理下一个任务，不中断整个流程
            continue
          }
        }
      }
      catch (err) {
        logError('批量中断任务失败', {
          error: err instanceof Error ? err.message : String(err),
          stack: err instanceof Error ? err.stack : undefined,
        })
      }
    }),
  // 停止直播录制
  stopRecordingLive: t.procedure
    .input<{ taskId: string }>()
    .action(async ({ input }) => {
      const { taskId } = input
      try {
        // 调用 snapfile 服务停止直播录制
        const success = snapfileService.stopRecordingLive(taskId)
        if (!success) {
          logError('停止直播录制失败', {
            taskId,
            reason: 'snapfile进程未运行',
          })
          return { success: false, message: 'snapfile 进程未运行' }
        }
        logInfo('停止直播录制成功', { taskId })
        return { success: true }
      }
      catch (error) {
        logError('停止直播录制失败', {
          taskId,
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        })
        return { success: false, message: '停止直播录制失败' }
      }
    }),
}
