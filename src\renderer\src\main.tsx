import { isDev } from '@/utils/environment'
import { isMac } from '@main/constants/env'
import ApplicationLayout from '@renderer/layouts/Application'
import AuthLayout from '@renderer/layouts/AuthLayout'
import * as Sentry from '@sentry/electron/renderer'
import ReactDOM from 'react-dom/client'
import { I18nextProvider } from 'react-i18next'
import { HashRouter, Route, Routes } from 'react-router-dom'
import { CustomTheme } from './components/custom-theme'
import i18n from './utils/i18n'
import './index.css'

// 检测操作系统并添加类名
if (!isMac) {
  document.documentElement.classList.add('windows')
}

// 修改 Sentry 初始化配置
Sentry.init({
  dsn: 'https://<EMAIL>/4508499640909824',
  // 开发环境禁用
  enabled: !isDev,
})

ReactDOM.createRoot(document.getElementById('root')!).render(
  <I18nextProvider i18n={i18n}>
    <HashRouter>
      <CustomTheme>
        <Routes>
          {/* 认证相关路由使用 AuthLayout */}
          <Route path="auth" element={<AuthLayout />} />
          {/* 其他所有路由使用 ApplicationLayout */}
          <Route path="/*" element={<ApplicationLayout />} />
        </Routes>
      </CustomTheme>
    </HashRouter>
  </I18nextProvider>,
)
