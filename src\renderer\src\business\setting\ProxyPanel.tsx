import type { <PERSON><PERSON><PERSON>, FormEventHandler } from 'react'
import { client } from '@/client'
import { validateHost, validatePort } from '@/utils/setting'
import { PROXY_TYPE_ENUM } from '@common/constants/setting'
import { ApplicationRequireSpan } from '@renderer/client/application-compose'
import { Notice } from '@renderer/components/Notice'
import { useSnapany } from '@renderer/hooks/snapany'
import { Button, Select, Spinner, TextInput } from 'flowbite-react'
import { CheckCircle, CloseCircle } from 'flowbite-react-icons/solid'
import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

type ProxyType = 'system' | 'direct' | 'http' | 'https' | 'socks5'

type FormDataType = {
  host: string
  port: string
  username: string
  password: string
}

function SettingProxyPanel() {
  const { t } = useTranslation()
  const [connection, setConnection] = useState<{ success: boolean | string } | undefined>()
  const { settings, patchSetting } = useSnapany()

  const proxy = settings?.proxy
  const { type, host, password, port, username } = proxy ?? {}
  const [isSaveDisabled, setIsSaveDisabled] = useState(!!port)
  const [proxyType, setProxyType] = useState(type ?? PROXY_TYPE_ENUM.direct)
  const [formData, setFormData] = useState<FormDataType>({ host, port, username, password })

  const mainDisabled = useMemo(
    () => [PROXY_TYPE_ENUM.direct, PROXY_TYPE_ENUM.system].includes(proxyType as PROXY_TYPE_ENUM),
    [proxyType],
  )

  const handleChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value as PROXY_TYPE_ENUM
    setProxyType(value)
    setIsSaveDisabled(false)
    if ([PROXY_TYPE_ENUM.direct, PROXY_TYPE_ENUM.system].includes(value)) {
      patchSetting({
        proxy: {
          type: value as unknown as ProxyType,
        },
      }).then(() => {
        setIsSaveDisabled(true)
      }).catch(
        () => Notice.error(t('messages.saveFailed')),
      )
    }
  }

  const handleFormChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData({ ...formData, [name]: value })
    setIsSaveDisabled(false)
  }

  const onSubmit: FormEventHandler<HTMLFormElement> = (e) => {
    e.preventDefault()

    if (mainDisabled) {
      patchSetting({
        proxy: {
          type: proxyType as ProxyType,
        },
      }).catch(
        () => Notice.error(t('messages.saveFailed')),
      )

      return
    }

    const formData = new FormData(e.currentTarget)

    const host = formData.get('host') as string
    const port = formData.get('port') as string
    const username = formData.get('username') as string
    const password = formData.get('password') as string

    if (!host) {
      Notice.error(t('settings.proxyInfoMessage.pleaseEnterValidProxyHost'))
      return
    }

    if (!validateHost(host)) {
      Notice.error(t('settings.proxyInfoMessage.pleaseEnterValidProxyHost'))
      return
    }

    if (!port) {
      Notice.error(t('settings.proxyInfoMessage.pleaseEnterProxyPort'))
      return
    }

    if (!validatePort(port)) {
      Notice.error(t('settings.proxyInfoMessage.pleaseEnterValidProxyPort'))
      return
    }

    patchSetting({
      proxy: {
        type: proxyType as ProxyType,
        host,
        port,
        username,
        password,
      },
    }).then(() => {
      setIsSaveDisabled(true)
    }).catch(
      () => Notice.error(t('messages.saveFailed')),
    )
  }

  const handleTestConnection = async () => {
    setConnection({ success: 'test' })
    const res = await client.testProxyConnection({
      url: formData.host,
      port: formData.port as unknown as number,
      username: formData.username,
      password: formData.password,
      type: proxyType as 'http' | 'socks5',
    })
    setConnection({ success: res.success })
  }

  return (
    <form
      className="grid grid-cols-[auto_1fr] gap-x-6 gap-y-4 items-center relative"
      onSubmit={onSubmit}
    >
      <ApplicationRequireSpan>
        {t('settings.proxyType')}
      </ApplicationRequireSpan>

      <Select
        defaultValue={type ?? proxyType}
        onChange={handleChange}
      >
        <option value={PROXY_TYPE_ENUM.system}>
          {t('settings.usingSystemProxy')}
        </option>
        <option value={PROXY_TYPE_ENUM.http}>
          HTTP
          {' '}
          {t('settings.proxy')}
        </option>
        <option value={PROXY_TYPE_ENUM.socks5}>
          SOCKS5
          {' '}
          {t('settings.proxy')}
        </option>
        <option value={PROXY_TYPE_ENUM.direct}>
          {t('settings.notUsingProxy')}
        </option>
      </Select>

      {[PROXY_TYPE_ENUM.http, PROXY_TYPE_ENUM.socks5].includes(proxyType as PROXY_TYPE_ENUM) && (
        <>
          <ApplicationRequireSpan required>
            {t('settings.host')}
          </ApplicationRequireSpan>
          <TextInput
            value={formData.host}
            name="host"
            placeholder="127.0.0.1"
            disabled={mainDisabled}
            onChange={handleFormChange}
          />

          <ApplicationRequireSpan required>
            {t('settings.port')}
          </ApplicationRequireSpan>
          <TextInput
            name="port"
            type="number"
            min={1}
            max={65535}
            value={formData.port}
            placeholder="7890"
            disabled={mainDisabled}
            onChange={handleFormChange}
          />

          <ApplicationRequireSpan>{t('settings.login')}</ApplicationRequireSpan>
          <TextInput
            value={formData.username}
            onChange={handleFormChange}
            name="username"
            placeholder={t('settings.proxyInfoMessage.optional')}
            disabled={mainDisabled}
          />

          <ApplicationRequireSpan>{t('settings.password')}</ApplicationRequireSpan>
          <TextInput
            value={formData.password}
            onChange={handleFormChange}
            name="password"
            type="password"
            placeholder={t('settings.proxyInfoMessage.optional')}
            disabled={mainDisabled}
          />

          <span />
          <div className="w-full absolute top-74">
            <div className="flex justify-center gap-2.5">
              <Button color="blue" outline onClick={handleTestConnection} disabled={connection?.success === 'test'}>{t('settings.testConnection')}</Button>
              <Button className="w-[110px]" color="blue" type="submit" disabled={isSaveDisabled || connection?.success === false}>
                {isSaveDisabled ? t('settings.saved') : t('settings.save')}
              </Button>
            </div>
            {connection && (
              <div className="flex items-center justify-center gap-2 mt-2">
                {connection.success === 'test' && (
                  <>
                    <Spinner color="gray" className="w-3 h-3" />
                    <span className="text-xs text-gray-500">{t('settings.testConnectionTip')}</span>
                  </>
                )}
                {connection.success === true && (
                  <>
                    <CheckCircle className="w-4 h-4 text-green-700" />
                    <span className="text-xs text-green-700">{t('settings.testConnectionSuccess')}</span>
                  </>
                )}
                {connection.success === false && (
                  <>
                    <CloseCircle className="w-4 h-4 text-red-700" />
                    <span className="text-xs text-red-700">{t('settings.testConnectionFailed')}</span>
                  </>
                )}
              </div>
            )}
          </div>
        </>
      )}
    </form>
  )
}

export default SettingProxyPanel
