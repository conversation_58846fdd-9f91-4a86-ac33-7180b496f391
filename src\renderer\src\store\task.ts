import type { SimpleResult } from '@common/types/electron-bridge'
import type { SelectTask } from '@main/lib/db/schema'
import type { DownloadProgressData } from '@main/types/download'
import { client, handlers } from '@/client'
import { Notice } from '@/components/Notice'
import { errorMessageEnum, taskStatus } from '@main/constants/status'
import * as Sentry from '@sentry/electron/renderer'
import { t } from 'i18next'
import { create } from 'zustand'

export type TaskType = SelectTask & DownloadProgressData
export interface TaskStore {
  tasks: TaskType[]
  /** 有中断任务或者未完成的任务 */
  hasIncompleteTask: boolean
  /** 是否需要弹出关闭软件弹窗 */
  isOpenCloseWindowModal: boolean
  /** 是否是初始化 */
  isInit: boolean
  /** 是否需要弹出删除所有任务弹窗 */
  isRemoveAllModal: boolean
  /** 正在下载的任务数量 */
  downloadingTaskCount: number
  unlisten?: () => void
  registerSync: () => void
  clearSync: () => void
  /** 计算正在下载的任务数量 */
  computeDownloadingTaskCount: () => void
  setHasIncompleteTask: (v: boolean) => void
  setIsOpenCloseWindowModal: (v: boolean) => void
  setIsRemoveAllModal: (v: boolean) => void
  fetchTask: () => Promise<void>
  /** 清除正在进行的任务 */
  clearDoingTasks: () => void
  checkHasUnfinishedTask: () => void
  deleteTask: (task: TaskType) => void
  openTaskFolder: (task: TaskType) => Promise<SimpleResult>
  retryTask: (task: TaskType) => Promise<void>
  addTask: (url: string | string[]) => void
  interruptedTasks: () => Promise<void>
  continueTasks: () => Promise<void>
  closeWindow: () => Promise<void>
  stopRecordingLive: (task: TaskType) => Promise<void>
}

export const useTaskStore = create<TaskStore>((set, get) => ({
  tasks: [],
  hasIncompleteTask: false,
  isOpenCloseWindowModal: false,
  isInit: true,
  isRemoveAllModal: false,
  downloadingTaskCount: 0,
  unlisten: undefined,
  setHasIncompleteTask(v) {
    set(() => ({ hasIncompleteTask: v }))
  },
  setIsOpenCloseWindowModal(v) {
    set(() => ({ isOpenCloseWindowModal: v }))
  },
  setIsRemoveAllModal(v) {
    set(() => ({ isRemoveAllModal: v }))
  },
  async fetchTask() {
    const tasks = (await client.getTaskList()) as unknown as TaskType[]
    const dealTasks = tasks.map((item) => {
      if (get().isInit && !item.errorMessage && (item.taskStatus === taskStatus.extracting || item.taskStatus === taskStatus.readyDownload || item.taskStatus === taskStatus.downloading || item.taskStatus === taskStatus.pendingConversion || item.taskStatus === taskStatus.converting)) {
        return {
          ...item,
          errorMessage: errorMessageEnum.interrupted,
        }
      }
      return item
    })
    set(() => ({ tasks: dealTasks, isInit: false }))
    get().computeDownloadingTaskCount()
  },
  clearDoingTasks() {
    const newTasks = get().tasks.filter(item => item.errorMessage || (item.taskStatus !== taskStatus.extracting && item.taskStatus !== taskStatus.readyDownload && item.taskStatus !== taskStatus.downloading && item.taskStatus !== taskStatus.pendingConversion && item.taskStatus !== taskStatus.converting))
    set(() => ({ tasks: newTasks }))
  },
  checkHasUnfinishedTask() {
    console.log('checkHasUnfinishedTask', get().tasks)
    const hasIncompleteTask = get().tasks.some(
      item =>
        (!item.errorMessage && item.taskStatus !== 'completed') || item.errorMessage === errorMessageEnum.interrupted,
    )
    set(() => ({ hasIncompleteTask }))
  },
  async deleteTask(task) {
    try {
      const newTasks = get().tasks.filter(item => item.id !== task.id)
      set(() => ({ tasks: newTasks }))
      await client.deleteTask({ taskId: task.id })
    }
    catch (err) {
      console.log(err)
      get().fetchTask()
      Sentry.captureException(`删除文件失败:${err}`)
      Notice.error(t('messages.defaultError'))
    }
  },
  async openTaskFolder(task) {
    try {
      return await client.openFileDir({ filePath: task.filePath })
    }
    catch (err) {
      Sentry.captureException(`打开文件夹失败:${err}`)
      if (err instanceof Error && err.message.includes('ENOENT')) {
        Notice.error(t('errors.fileNotFound'))
      }
      else {
        Notice.error(t('errors.openFileLocationFailed'))
      }
      return { success: false }
    }
  },
  async retryTask(task) {
    try {
      await client.resumeDownload({ taskId: task.id })
    }
    catch (err) {
      Sentry.captureException(`重试失败:${err}`)
      Notice.error(t('errors.retryFailed'))
    }
  },

  async addTask(url: string | string[]) {
    const urls = Array.isArray(url) ? url : [url]
    const data = (await client.startDownload({ urls })) as unknown as TaskType[]
    set(state => ({ tasks: [...data, ...state.tasks] }))
  },
  computeDownloadingTaskCount() {
    const downloadingTaskCount = get().tasks.filter(item => !item.errorMessage && item.taskStatus !== 'completed').length
    set(() => ({ downloadingTaskCount }))
  },
  registerSync() {
    const { unlisten } = get()
    if (unlisten) {
      return
    }
    const unlistenFunction = handlers.onDownloadProgress.listen((message) => {
      console.log('message', message)
      const newTasks = get().tasks.map((item) => {
        if (item.id === message.taskId) {
          return {
            ...item,
            ...message,
          }
        }
        return item
      })
      set(() => ({ tasks: newTasks }))
      get().computeDownloadingTaskCount()
    })
    set(() => ({ unlisten: unlistenFunction }))
  },
  clearSync() {
    const { unlisten } = get()
    if (unlisten) {
      unlisten()
      set(() => ({ unlisten: undefined }))
    }
  },
  async interruptedTasks() {
    await client.interruptTasks()
  },
  // 继续所有未完成的任务
  async continueTasks() {
    for (const item of get().tasks) {
      if ((item.taskStatus === taskStatus.extracting
        || item.taskStatus === taskStatus.readyDownload
        || item.taskStatus === taskStatus.downloading
        || item.taskStatus === taskStatus.pendingConversion
        || item.taskStatus === taskStatus.converting)
      && (!item.errorMessage || item.errorMessage === errorMessageEnum.interrupted)) {
        console.log('continueTasks', item)
        await get().retryTask(item)
      }
    }
  },
  async closeWindow() {
    try {
      await client.closeWindow()
    }
    catch (error) {
      console.log(error)
    }
  },
  async stopRecordingLive(task) {
    try {
      await client.stopRecordingLive({ taskId: task.id })
    }
    catch (err) {
      Sentry.captureException(`停止录制失败:${err}`)
    }
  },
}))
