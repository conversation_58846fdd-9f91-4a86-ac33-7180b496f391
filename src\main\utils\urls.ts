import type { UrlItem } from '@main/types'
import tldjs from 'tldjs'

export function extractUrlsFromText(text: string): string[] {
  // 匹配URL的正则表达式：
  // 1. 支持http(s)和www开头的链接
  // 2. 排除以斜杠/结尾的URL
  // 3. 在中文标点、英文逗号句号、空格处终止
  const regex
    = /(https?:\/\/|www\.)[^\s\u4E00-\u9FA5，。！？、；："'（）【】《》,]+(?<!\/)/g
  const matches = text.match(regex) || []

  // 为www开头的链接添加https://前缀，并进行去重
  const uniqueUrls = new Set<string>()

  matches.forEach((url: string) => {
    // 去除末尾的句号
    url = url.endsWith('.') ? url.slice(0, -1) : url
    // 为www开头的链接添加https://前缀
    const normalizedUrl = url.startsWith('www.') ? `https://${url}` : url
    uniqueUrls.add(normalizedUrl)
  })

  // 转换为数组并返回去重后的结果
  return Array.from(uniqueUrls)
}

interface JsonData {
  http_headers?: Record<string, string>
  cookies?: string
}

/**
 * 获取HTTP请求头
 * @param obj 包含http_headers和cookies的对象
 * @returns HTTP请求头对象
 */
export function getHttpHeaders(obj: JsonData): Record<string, string> {
  const headers: Record<string, string> = {}

  // 确保obj存在
  if (!obj) {
    return headers
  }

  // 如果存在http_headers对象,直接合并
  if (obj.http_headers && typeof obj.http_headers === 'object') {
    Object.assign(headers, obj.http_headers)
  }

  // 处理cookies
  if (obj.cookies) {
    headers.Cookie = handleCookies(obj.cookies)
  }

  return headers
}

/**
 * 处理cookies字符串，执行以下操作:
 * 1. 去除每个cookie值两端的引号
 * 2. 去除cookie字符串中的多余空格
 * 3. 确保相同名称的cookie只保留最后一个值(去重)
 *
 * @param {string} cookies - 原始cookie字符串，格式如: 'key1="value1"; key2="value2"'
 * @returns {string} 处理后的标准化cookie字符串，格式如: 'key1=value1; key2=value2'
 *
 * @example
 * 返回: 'id=123; name=john doe; session=abc'
 * handleCookies('id="123"; name="john doe"; session="abc"');
 *
 * 处理重复项 (后值覆盖前值)
 * 返回: 'id=456; name=john'
 * handleCookies('id="123"; id="456"; name="john"');
 */
export function handleCookies(cookies: string) {
  // 将cookies字符串拆分并转换为对象, 确保去重
  const cookiesObj = cookies
    .split(';')
    .reduce((acc: Record<string, string>, cookie) => {
      const cookieStr = cookie.trim()
      // 找到第一个等号的位置作为分隔符
      const firstEqualIndex = cookieStr.indexOf('=')
      if (firstEqualIndex === -1)
        return acc

      // 分别获取cookie名称和值
      const key = cookieStr.slice(0, firstEqualIndex).trim()
      const value = cookieStr.slice(firstEqualIndex + 1).trim()

      // 只处理有值的cookie
      if (value) {
        // 移除值两端的引号(单引号或双引号)
        const cleanValue = value.replace(/^["']|["']$/g, '')
        acc[key] = cleanValue
      }
      return acc
    }, {})

  // 将对象转换回标准化的cookie字符串
  return Object.entries(cookiesObj)
    .map(([key, value]) => `${key}=${value}`)
    .join('; ')
}

/**
 * 检查URL是否重复
 * @param urlList URL列表
 * @param newUrl 新的URL
 * @returns 是否重复
 */
export function isUrlDuplicate(urlList: UrlItem[], newUrl: string): boolean {
  return urlList.some(item => item.url === newUrl)
}

/**
 * 获取URL的顶级主域名
 * @param url 需要获取顶级主域名的URL
 * @returns 顶级主域名
 */
export function getTopLevelMainDomain(url: string): string {
  return tldjs.getDomain(url).split('.')[0]
}
