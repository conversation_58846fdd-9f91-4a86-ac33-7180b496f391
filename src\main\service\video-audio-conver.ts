import type { RendererHandlers } from '@main/renderer-handlers'
import type { ConvertFileMediaInfoItem, ConvertTask, ConvertTaskStatus, VideoAudioConverProgressData } from '@main/types/ffmpeg'
import path from 'node:path'
import { getRendererHandlers } from '@egoist/tipc/main'
import { getFilePathMediaInfo, isImageFile } from '@main/utils'
import { getMainWindow } from '@main/window'
import ffmpeg from 'fluent-ffmpeg'
import fs from 'fs-extra'

// 跟踪当前的ffmpeg命令实例
let currentFFmpegCommand: ffmpeg.FfmpegCommand | null = null

class VideoAudioConverService {
  private taskQueue: ConvertTask[] = [] // 任务队列
  private isProcessing = false // 是否正在处理任务

  constructor() { }

  /**
   * 获取媒体文件信息
   * @param filePaths 媒体文件路径列表
   * @returns 媒体文件信息列表
   */
  async getFilesMediaInfo(filePaths: string[]): Promise<ConvertFileMediaInfoItem[]> {
    try {
      const fileInfoPromises = filePaths.map(async (filePath) => {
        try {
          const mediaInfo = await getFilePathMediaInfo(filePath)
          const isImage = await isImageFile(filePath)

          // 如果文件是图片，并且后缀不是mjpeg
          if ((isImage || mediaInfo.format.nb_frames === 1) && !filePath.endsWith('.mjpeg')) {
            return {
              duration: null,
              width: mediaInfo.streams.find(stream => stream.width)?.width,
              height: mediaInfo.streams.find(stream => stream.height)?.height,
              fileType: 'image' as const,
              filePath,
              name: path.basename(filePath, path.extname(filePath)),
              // 获取文件扩展名不包含点
              extname: path.extname(filePath).slice(1),
            }
          }

          // 检查是否有真实视频流（不是封面图片的视频流）
          const hasVideoStream = mediaInfo.streams.some(stream => stream.codec_type === 'video' && stream.disposition?.attached_pic !== 1)
          if (hasVideoStream) {
            return {
              // 如果转数字失败，则设置为null
              duration: Number.isNaN(Number(mediaInfo.format.duration)) ? null : Number(mediaInfo.format.duration),
              width: mediaInfo.streams.find(stream => stream.width)?.width,
              height: mediaInfo.streams.find(stream => stream.height)?.height,
              fileType: 'video' as const,
              filePath,
              name: path.basename(filePath, path.extname(filePath)),
              // 获取文件扩展名不包含点
              extname: path.extname(filePath).slice(1),
            }
          }

          // 检查是否有音频流
          const hasAudioStream = mediaInfo.streams.some(stream => stream.codec_type === 'audio')
          if (hasAudioStream) {
            return {
              // 如果转数字失败，则设置为null
              duration: Number.isNaN(Number(mediaInfo.format.duration)) ? null : Number(mediaInfo.format.duration),
              width: mediaInfo.streams.find(stream => stream.width)?.width,
              height: mediaInfo.streams.find(stream => stream.height)?.height,
              fileType: 'audio' as const,
              filePath,
              name: path.basename(filePath, path.extname(filePath)),
              // 获取文件扩展名不包含点
              extname: path.extname(filePath).slice(1),
            }
          }

          return null // 如果既不是图片，也没有视频或音频流，则返回null
        }
        catch {
          return null
        }
      })

      const fileInfos = await Promise.all(fileInfoPromises)
      // 过滤掉为null的元素
      const validFileInfos = fileInfos.filter(info => info !== null) as ConvertFileMediaInfoItem[]
      return validFileInfos
    }
    catch (error) {
      console.error('获取媒体信息失败:', error)
      return []
    }
  }

  /**
   * 转换文件
   * @param filePaths 文件路径列表
   * @param outputFormat 输出格式
   * @param outputDir 输出目录
   * @param extraParams 额外参数
   * @returns 转换结果
   */
  async convertMediaFiles(filePaths: string[], outputFormat: string, outputDir: string, extraParams?: string[]): Promise<string[]> {
    try {
      // 结束转换，等待完全停止
      await this.stopCurrentConvertTask()

      // 确保输出目录存在
      await fs.ensureDir(outputDir)

      // 将所有文件添加到转换队列
      for (const filePath of filePaths) {
        this.addTaskToQueue({
          id: filePath,
          filePath,
          outputFormat,
          outputDir,
          extraParams,
          status: 'waiting',
        })
      }

      // 开始处理队列中的任务
      this.processQueue()

      // 返回添加的任务ID列表
      return filePaths
    }
    catch (error) {
      console.error('添加转换任务失败:', error)
      return []
    }
  }

  /**
   * 添加任务到队列
   * @param task 转换任务
   */
  private addTaskToQueue(task: ConvertTask): void {
    // 检查任务是否已经在队列中
    const existingTaskIndex = this.taskQueue.findIndex(t => t.id === task.id)
    if (existingTaskIndex !== -1) {
      // 如果任务已存在且不是等待状态，不进行添加
      if (this.taskQueue[existingTaskIndex].status !== 'waiting') {
        return
      }
      // 如果是等待状态，更新任务
      this.taskQueue[existingTaskIndex] = task
    }
    else {
      // 否则添加新任务
      this.taskQueue.push(task)
    }

    // 发送任务添加通知
    this.sendTaskStatusUpdate(task.id, 'waiting')
  }

  /**
   * 处理队列中的任务
   */
  private async processQueue(): Promise<void> {
    // 如果已经在处理任务，则不再启动新的处理
    if (this.isProcessing) {
      return
    }

    this.isProcessing = true

    while (this.taskQueue.length > 0) {
      // 找到第一个等待中的任务
      const taskIndex = this.taskQueue.findIndex(task => task.status === 'waiting')
      if (taskIndex === -1) {
        break // 没有等待中的任务
      }

      const task = this.taskQueue[taskIndex]

      try {
        // 更新任务状态为转换中
        this.taskQueue[taskIndex].status = 'converting'
        this.sendTaskStatusUpdate(task.id, 'converting')

        // 准备输出路径
        const fileName = path.basename(task.filePath, path.extname(task.filePath))
        // 不再提前生成最终路径，只确定最终需要的基本信息
        const outputBasePath = path.join(task.outputDir, fileName)
        const outputExt = `.${task.outputFormat}`

        // 为临时文件生成随机名称，避免冲突
        const randomTempName = `${fileName}_${Date.now()}_${Math.floor(Math.random() * 10000)}`
        // 确保临时目录存在
        const tempDir = path.join(task.outputDir, '.snapany', 'conver_temp')
        await fs.ensureDir(tempDir)
        // 在转换开始前清理临时目录
        await this.cleanTempDir(tempDir)
        const outputTempPath = path.join(tempDir, `${randomTempName}${outputExt}`)

        // 创建FFmpeg命令
        const command = ffmpeg(task.filePath)
        currentFFmpegCommand = command

        // 设置输出编码
        if (task.extraParams) {
          command.outputOptions(task.extraParams)
        }

        // 检查是否是GIF转换为非WebM格式
        const inputExt = path.extname(task.filePath).toLowerCase()
        const outputFormat = task.outputFormat.toLowerCase()
        if (inputExt === '.gif' && outputFormat !== 'webp') {
          // 对GIF输入转换为非Webp格式的文件，添加-vframes 1参数，只取第一帧
          console.log('检测到GIF转换为非Webp格式，将只保留第一帧')
          command.outputOptions(['-vframes', '1'])
        }

        // 设置输出文件
        command.output(outputTempPath)

        // 执行转换操作
        const result = await this.executeFFmpegCommand(command, outputBasePath, outputTempPath, outputExt, task.id)

        // 更新任务状态
        if (result.success && result.outputPath) {
          this.taskQueue[taskIndex].status = 'completed'
          this.taskQueue[taskIndex].outputPath = result.outputPath
          this.sendTaskStatusUpdate(task.id, 'completed', undefined, result.outputPath)
        }
        else {
          this.taskQueue[taskIndex].status = 'failed'
          this.taskQueue[taskIndex].error = result.error
          this.sendTaskStatusUpdate(task.id, 'failed', result.error)
        }
      }
      catch (error) {
        // 更新任务状态为失败
        this.taskQueue[taskIndex].status = 'failed'
        this.taskQueue[taskIndex].error = error.message
        this.sendTaskStatusUpdate(task.id, 'failed', error.message)
        console.error(`转换文件 ${task.filePath} 失败:`, error)
      }
    }

    // 处理完毕，重置处理状态
    this.isProcessing = false
    currentFFmpegCommand = null
  }

  /**
   * 发送任务状态更新
   * @param taskId 任务ID
   * @param status 任务状态
   * @param error 错误信息
   * @param outputPath 输出路径
   */
  private sendTaskStatusUpdate(
    taskId: string,
    status: ConvertTaskStatus,
    error?: string,
    outputPath?: string,
  ): void {
    const mainWindow = getMainWindow()
    const handlers = getRendererHandlers<RendererHandlers>(mainWindow?.webContents)

    if (handlers && handlers.onVideoAudioConverProgress && handlers.onVideoAudioConverProgress.send) {
      const updateData: VideoAudioConverProgressData = {
        id: taskId,
        status,
      }

      if (error) {
        updateData.error = error
      }

      if (outputPath) {
        updateData.outputPath = outputPath
        updateData.outputExt = path.extname(outputPath).slice(1)
      }

      handlers.onVideoAudioConverProgress.send(updateData)
    }
  }

  /**
   * 停止当前正在进行的转换任务和所有待转换任务
   * @returns 停止结果，包含成功状态和可能的错误信息
   */
  async stopCurrentConvertTask(): Promise<{ success: boolean, error?: string }> {
    try {
      if (currentFFmpegCommand) {
        await new Promise<void>((resolve) => {
          const command = currentFFmpegCommand as ffmpeg.FfmpegCommand

          const originalErrorHandler = command.listeners('error')[0]
          command.removeAllListeners('error')

          command.on('error', (err) => {
            if (err.message && err.message.includes('SIGKILL')) {
              console.log('FFmpeg进程已成功终止')
              resolve()
            }
            else if (originalErrorHandler) {
              originalErrorHandler(err)
              resolve()
            }
            else {
              resolve()
            }
          })

          command.kill('SIGKILL')
        })
        currentFFmpegCommand = null
      }

      // 需要清理的task
      const tasksToCleanDir = this.taskQueue.map(task => task.outputDir)

      this.taskQueue = []
      this.isProcessing = false

      // 清理所有临时目录
      await this.cleanAllTempDirs(tasksToCleanDir)

      console.log('已停止所有转换任务并清空队列')

      return {
        success: true,
      }
    }
    catch (error) {
      console.error('停止转换任务时出错:', error)
      return {
        success: false,
        error: `停止任务失败: ${error.message}`,
      }
    }
  }

  /**
   * 清理所有临时目录
   * @private
   */
  private async cleanAllTempDirs(outputDirs: string[]): Promise<void> {
    try {
      // 对每个输出目录进行清理
      for (const outputDir of outputDirs) {
        const tempDir = path.join(outputDir, '.snapany', 'conver_temp')
        await this.cleanTempDir(tempDir)
      }

      console.log('所有临时目录已清理')
    }
    catch (error) {
      console.error('清理所有临时目录失败:', error)
    }
  }

  /**
   * 清理临时目录
   * @private
   * @param tempDir 临时目录路径
   */
  private async cleanTempDir(tempDir: string): Promise<void> {
    try {
      // 确保目录存在
      await fs.ensureDir(tempDir)
      // 读取目录中的所有文件
      const files = await fs.readdir(tempDir)
      // 删除所有文件
      for (const file of files) {
        await fs.remove(path.join(tempDir, file))
      }
      console.log('临时目录已清理:', tempDir)
    }
    catch (error) {
      console.error('清理临时目录失败:', error)
    }
  }

  /**
   * 执行FFmpeg命令
   * @private
   * @param command FFmpeg命令实例
   * @param outputBasePath 输出文件基本路径（不含扩展名）
   * @param outputTempPath 临时输出文件路径
   * @param outputExt 输出文件扩展名
   * @param taskId 任务ID
   * @returns 执行结果
   */
  private async executeFFmpegCommand(
    command: ffmpeg.FfmpegCommand,
    outputBasePath: string,
    outputTempPath: string,
    outputExt: string,
    taskId: string,
  ): Promise<{ success: boolean, outputPath?: string, error?: string }> {
    return new Promise((resolve) => {
      command
        .on('start', (commandLine) => {
          console.log('转换开始:', commandLine)
        })
        .on('progress', (progress) => {
          console.log('转换进度:', progress.percent)
          // 更新进度，但不改变状态
          const mainWindow = getMainWindow()
          const handlers = getRendererHandlers<RendererHandlers>(mainWindow?.webContents)

          if (handlers && handlers.onVideoAudioConverProgress && handlers.onVideoAudioConverProgress.send) {
            handlers.onVideoAudioConverProgress.send({
              id: taskId,
              status: 'converting',
              progress: progress.percent,
            })
          }
        })
        .on('error', async (err) => {
          console.error('FFmpeg错误:', err)
          // 清除当前命令引用
          currentFFmpegCommand = null
          // 清理临时目录
          const tempDir = path.dirname(outputTempPath)
          await this.cleanTempDir(tempDir)

          // 提取真正的错误信息
          let errorMessage = err.message
          // 检查错误信息是否符合FFmpeg标准格式
          if (typeof errorMessage === 'string' && errorMessage.includes('ffmpeg exited with code')) {
            // 提取最后一个冒号后的内容作为真正的错误信息
            const lastColonIndex = errorMessage.lastIndexOf(':')
            if (lastColonIndex !== -1) {
              errorMessage = errorMessage.substring(lastColonIndex + 1).trim()
            }
          }

          resolve({
            success: false,
            error: errorMessage,
          })
        })
        .on('end', async () => {
          console.log('FFmpeg处理完成')
          // 清除当前命令引用
          currentFFmpegCommand = null

          // 在这里确定最终的输出路径
          let finalOutputPath = `${outputBasePath}${outputExt}`
          let counter = 1

          // 如果文件已存在，则添加编号
          while (await fs.pathExists(finalOutputPath)) {
            finalOutputPath = `${outputBasePath}(${counter})${outputExt}`
            counter++
          }

          try {
            // 移动临时文件到最终位置
            await fs.move(outputTempPath, finalOutputPath, { overwrite: false })
            // 清理临时目录
            const tempDir = path.dirname(outputTempPath)
            await this.cleanTempDir(tempDir)

            resolve({
              success: true,
              outputPath: finalOutputPath,
            })
          }
          catch (error) {
            console.error('移动文件失败:', error)
            // 清理临时目录
            const tempDir = path.dirname(outputTempPath)
            await this.cleanTempDir(tempDir)

            resolve({
              success: false,
              error: `移动文件失败: ${error.message}`,
            })
          }
        })
        .run()
    })
  }
}

export default new VideoAudioConverService()
