import FooterBox from '@/business/format/FooterBox'
import MediaItem from '@/business/format/MediaItem'
import { useFormatStore } from '@/store/format'
import { useCallback, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { Virtuoso } from 'react-virtuoso'
import SelectMedia from '../business/format/SelectMedia'

function Item({ item, ...props }) {
  return <div {...props} className="border-b-1 border-gray-300 last:border-0 last:[&>div]:rounded-b-lg" />
}

function FormatPage() {
  const { t } = useTranslation()
  const videoInputRef = useRef<HTMLInputElement>(null)
  const audioInputRef = useRef<HTMLInputElement>(null)
  const imageInputRef = useRef<HTMLInputElement>(null)

  const { fileData, format, convertStatus, repeatDataItemKey, handleUploadFile, deleteDataItem, clearAllData } = useFormatStore()

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    if (convertStatus === 'converting') {
      e.preventDefault()
      return
    }
    e.preventDefault()
    const files = Array.from(e.dataTransfer.files || [])
    if (convertStatus === 'completed') {
      clearAllData()
    }
    handleUploadFile(files)
  }, [convertStatus, handleUploadFile])

  const handleVideo = () => {
    videoInputRef.current?.click()
  }
  const handleAudio = () => {
    audioInputRef.current?.click()
  }
  const handleImage = () => {
    imageInputRef.current?.click()
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (convertStatus === 'completed') {
      clearAllData()
    }
    handleUploadFile(files)
    // 清空文件选择框
    e.target.value = ''
  }

  const handleDeleteFile = (filePath: string) => {
    deleteDataItem(filePath)
  }

  const handleAddFile = () => {
    switch (format) {
      case 'video':
        videoInputRef.current?.click()
        break
      case 'audio':
        audioInputRef.current?.click()
        break
      case 'image':
        imageInputRef.current?.click()
        break
      default:
        break
    }
  }

  const headerComponent = useMemo(() => () => (
    <div className="flex items-center justify-between border-b border-gray-200 bg-white py-3 px-4 text-xs text-gray-500 font-semibold rounded-t-lg">
      <span className="flex-1 truncate">{t('format.fileName')}</span>
      <div className="grid grid-cols-7 gap-2 min-w-[220px] flex-[0_0_320px]">
        <span className="text-center col-span-2">{format === 'image' ? t('format.resolution') : t('format.duration')}</span>
        <span className="text-center col-span-2">{t('format.original')}</span>
        <span className="text-center col-span-2">{t('format.output')}</span>
        <span></span>
      </div>
    </div>
  ), [t, format])

  return (
    <div
      className="w-full h-full"
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <input ref={videoInputRef} type="file" multiple accept="video/*" className="hidden" onChange={handleFileChange} />
      <input ref={audioInputRef} type="file" multiple accept="audio/*" className="hidden" onChange={handleFileChange} />
      <input ref={imageInputRef} type="file" multiple accept="image/*" className="hidden" onChange={handleFileChange} />
      {
        fileData.length === 0
          ? <SelectMedia handleVideo={handleVideo} handleAudio={handleAudio} handleImage={handleImage} />
          : (
              <div className="w-full h-full flex flex-col pt-4">
                <p className="text-base font-medium text-gray-900 px-4">
                  {format === 'image' ? t('format.imageConverter') : format === 'audio' ? t('format.audioConverter') : t('format.videoConverter')}
                  {' '}
                  <span className="text-sm font-normal">
                    (
                    {convertStatus === 'converting' ? `${fileData.length - fileData.filter(item => item.status === 'waiting' || item.status === 'converting').length} / ${fileData.length}` : fileData.length}
                    )
                  </span>
                </p>
                <div className="w-full h-full p-4 overflow-y-auto">
                  <Virtuoso
                    className="rounded-lg"
                    totalCount={fileData.length}
                    data={fileData}
                    components={{ Header: headerComponent, Item }}
                    itemContent={(_, item) => {
                      return (
                        <MediaItem key={item.filePath} isHighlight={repeatDataItemKey.has(item.filePath)} onDelete={handleDeleteFile} {...item} />
                      )
                    }}
                  />
                </div>
                <FooterBox addFile={handleAddFile} />
              </div>
            )
      }

    </div>
  )
}

export default FormatPage
