CREATE TABLE `task` (
	`id` text PRIMARY KEY NOT NULL,
	`text` text NOT NULL,
	`url` text NOT NULL,
	`thumbnail` text,
	`request_headers` text,
	`extension` text,
	`duration` integer,
	`file_size` integer,
	`file_path` text,
	`resolution_width` integer,
	`resolution_height` integer,
	`bitrate` integer,
	`task_status` text NOT NULL,
	`error_status` text,
	`error_message` text,
	`error_action` text,
	`temp_task` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
