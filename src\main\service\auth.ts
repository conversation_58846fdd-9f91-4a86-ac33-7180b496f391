import * as fs from 'node:fs/promises'
import * as path from 'node:path'
import { checkFileExists } from '@main/utils'
import { getAuthSession } from '@main/window'
import { app } from 'electron'

class AuthService {
  private cookiesFilePath: string
  constructor() {
    this.cookiesFilePath = path.join(app.getPath('userData'), 'cookies.txt')
  }

  private async getCookies(): Promise<Electron.Cookie[]> {
    const session = getAuthSession()
    return session.cookies.get({})
  }

  public async verifyLogin(
    url: string,
  ): Promise<boolean> {
    // 检查是否包含关键的登录cookie
    const cookies = await this.getCookies()
    const domain = new URL(url).hostname.replace(/^www\./, '')

    if (domain.includes('youtube.com')) {
      const youtubeCookies = cookies.filter(cookie =>
        cookie.domain && cookie.domain.includes('youtube.com'),
      )
      return youtubeCookies.some(
        cookie =>
          cookie.name === 'LOGIN_INFO'
          || cookie.name === 'APISID'
          || cookie.name === 'SID',
      )
    }

    if (domain.includes('instagram.com')) {
      const instagramCookies = cookies.filter(cookie =>
        cookie.domain && cookie.domain.includes('instagram.com'),
      )
      return instagramCookies.some(
        cookie => cookie.name === 'sessionid' || cookie.name === 'ds_user_id',
      )
    }

    if (domain.includes('x.com') || domain.includes('twitter.com')) {
      const twitterCookies = cookies.filter(cookie =>
        cookie.domain && (cookie.domain.includes('twitter.com') || cookie.domain.includes('x.com')),
      )
      return twitterCookies.some(
        cookie => cookie.name === 'auth_token' || cookie.name === 'twid',
      )
    }

    return cookies.length > 0
  }

  public async getCookiesFilePath(): Promise<string> {
    // 检查文件是否存在
    const exists = await checkFileExists(this.cookiesFilePath)
    if (!exists) {
      return ''
    }
    return this.cookiesFilePath
  }

  public async saveCookieFile(): Promise<string> {
    // 获取cookies
    const cookies = await this.getCookies()
    // 格式化cookies
    const cookieStr = this.formatCookies(cookies)

    // 在文件头部添加注释
    const fileContent = this.updateCookiesContent(
      cookieStr,
    )
    await fs.writeFile(this.cookiesFilePath, fileContent, 'utf8')
    return this.cookiesFilePath
  }

  public async deleteCookieFile(domain: string): Promise<void> {
    try {
      const session = getAuthSession()
      const cookies = await session.cookies.get({})

      // 从 electron 中清除所有相关的域名的 cookies
      // 移除可能的 www. 前缀和http/https前缀
      const domainWithoutWWW = domain
        .replace(/^https?:\/\//, '') // 先移除http://或https://前缀
        .replace(/^www\./, '') // 再移除www.前缀

      // 特殊处理 x.com 和 twitter.com
      const isTwitterDomain = domainWithoutWWW.includes('x.com') || domainWithoutWWW.includes('twitter.com')

      for (const cookie of cookies) {
        if (
          (isTwitterDomain && (cookie.domain?.includes('x.com') || cookie.domain?.includes('twitter.com')))
          || (cookie.domain && cookie.domain.includes(domainWithoutWWW))
        ) {
          // 构建 cookie URL
          const cookieUrl = `http${cookie.secure ? 's' : ''}://${
            cookie.domain.startsWith('.') ? cookie.domain.slice(1) : cookie.domain
          }${cookie.path || '/'}`

          // 从会话中移除 cookie
          console.log('移除 cookie', cookieUrl, cookie.name)
          await session.cookies.remove(cookieUrl, cookie.name)
        }
      }
      await this.saveCookieFile()
    }
    catch (error) {
      console.error('移除 Cookie 失败', error)
      throw new Error('移除 Cookie 失败')
    }
  }

  private updateCookiesContent(
    cookieStr: string,
  ): string {
    const header
      = '# Netscape HTTP Cookie File\n# This file is generated by yt-dlp.  Do not edit.\n'

    // 如果文件为空，直接返回头部信息和新的cookies
    return header + cookieStr
  }

  private formatCookies(cookies: Electron.Cookie[]): string {
    return cookies
      .map((cookie) => {
        const domain = cookie.domain
          ? cookie.domain.startsWith('.')
            ? cookie.domain
            : `.${cookie.domain}`
          : '.localhost'
        // 将时间戳转换为整数
        const expirationDate = cookie.expirationDate
          ? Math.floor(cookie.expirationDate)
          : Math.floor(Date.now() / 1000 + 365 * 24 * 60 * 60) // 默认一年后过期

        return `${domain}\tTRUE\t${cookie.path || '/'}\t${cookie.secure}\t${expirationDate}\t${cookie.name}\t${cookie.value}`
      })
      .join('\n')
  }
}

export default new AuthService()
