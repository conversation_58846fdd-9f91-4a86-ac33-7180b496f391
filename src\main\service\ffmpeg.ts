import type { FFmpegProgress } from '@common/types/download'
import type { RendererHandlers } from '@main/renderer-handlers'
import type {
  DownloadResult,
  FFprobeStream,
  MediaInfo,
  ProcessMediaInfo,
  SettingStoreType,
  StreamInfo,
} from '@main/types'
import type { FfprobeData } from 'fluent-ffmpeg'
import fs from 'node:fs/promises'
import * as path from 'node:path'
import { getRendererHandlers } from '@egoist/tipc/main'
import { SUBTITLE_LANGUAGES } from '@main/constants/language'
import { errorMessageEnum, errorStatusEnum, taskStatus } from '@main/constants/status'
import { logError, logInfo, logWarn } from '@main/lib/logger'
import { settingStore } from '@main/lib/store'
import {
  checkFileExists,
  ensureDirectoryExists,
  getDirectoryPath,
  getISOLanguageCode,
  getLanguageName,
  getNewPngPath,
  isImageFile,
  sanitizeFileName,
  updateFilePath,
} from '@main/utils'
import { getMainWindow } from '@main/window'
import { app } from 'electron'
import ffmpeg from 'fluent-ffmpeg'
import taskService, { tempTaskProgressMap } from './task'

class FFmpegService {
  private ffmpegProcesses: Map<string, ffmpeg.FfmpegCommand[]> = new Map()

  constructor() { }

  // 开始合并转换
  async startMergeConvert(
    downloadedInfoList: DownloadResult[],
    setting: SettingStoreType,
    taskId: string,
  ): Promise<DownloadResult[]> {
    const mergeOutputDir = path.join(
      setting.downloadPath,
      `.${app.getName()}`,
      taskId,
      `merged`,
    )
    const task = await taskService.getTaskById(taskId)
    return await new Promise<DownloadResult[]>((resolve, reject) => {
      let lastPercent = 0
      const mainWindow = getMainWindow()
      const handlers = getRendererHandlers<RendererHandlers>(mainWindow.webContents)

      this.mergeMediaFiles(
        downloadedInfoList,
        mergeOutputDir,
        setting,
        // true,
        setting.embedSubtitle,
        (progress) => {
          console.log('merge progress:', progress.percent)
          const precent = progress.percent > lastPercent ? progress.percent : lastPercent
          handlers.onDownloadProgress.send({
            ...task,
            taskId,
            taskStatus: taskStatus.converting,
            totalSize: null,
            downloadedSize: null,
            speed: null,
            eta: null,
            percent: precent,
            isLive: task.isLive || false,
          })
          const tempProgress = tempTaskProgressMap.get(taskId)
          if (progress) {
            tempTaskProgressMap.set(taskId, {
              ...tempProgress,
              percent: precent,
            })
          }
          lastPercent = progress.percent
        },
        (completeList) => {
          handlers.onDownloadProgress.send({
            ...task,
            taskId,
            taskStatus: taskStatus.converting,
            totalSize: null,
            downloadedSize: null,
            speed: null,
            eta: null,
            percent: 100,
            isLive: task.isLive || false,
          })
          let list = completeList.map(item => ({
            ...item,
            url: downloadedInfoList.find(info => info.filePath === item.filePath)?.url,
          }))
          // 根据filePath去重
          list = list.filter((item, index, self) =>
            index === self.findIndex(t => t.filePath === item.filePath),
          )
          resolve(list)
        },
        (error) => {
          reject(error)
        },
        taskId,
      )
    })
  }

  async moveFiles(
    completeList: DownloadResult[],
    setting: SettingStoreType,
    title: string,
  ): Promise<MediaInfo> {
    try {
      console.log('completeList:', completeList)
      // 移动文件
      let taskMediaInfo: MediaInfo = {
        mediaType: 'other',
      }

      // 用于收集所有移动后的文件路径
      const movedFilePaths: string[] = []

      for (const [index, item] of completeList.entries()) {
        // 直接使用item.filePath作为源文件路径
        const filePath = item.filePath
        const language = item.language
        const targetDir = setting.downloadPath

        // const targetDir = path.join(
        //   setting.downloadPath,
        //   ...(language ? [language] : []),
        // )
        await fs.mkdir(targetDir, { recursive: true })

        // 获取文件最终的信息
        const fileExt = path.extname(filePath)
        const extension = fileExt.split('.').pop()
        // 获取文件信息
        let mediaInfo: MediaInfo = {
          mediaType: 'other',
        }
        try {
          mediaInfo = await this.getLocalMediaInfo(item.filePath)
        }
        catch (error) {
          console.warn('获取文件信息时出错:', error)
        }
        // 优先使用用户配置中的类型
        if (index === 0) {
          // if (mediaInfo.mediaType === setting.downloadType ||
          //   mediaInfo.mediaType === 'other') {
          const stats = await fs.stat(filePath)
          taskMediaInfo = {
            ...mediaInfo,
            extension,
            filePath,
            fileSize: stats.size,
          }
        }
        // }

        // 规范化文件名
        const sanitizedTitle = sanitizeFileName(title)

        // 构建新文件名：rawInfo.title + 可选的语言标识 + 原扩展名
        let newFileName = language
          ? `${sanitizedTitle}.${getLanguageName(language)}${fileExt}`
          : `${sanitizedTitle}${fileExt}`

        // 检查目标文件是否已存在
        let targetFilePath = path.join(targetDir, newFileName)
        const fileExists = await checkFileExists(targetFilePath)

        if (fileExists) {
          const ext = path.extname(newFileName)
          const baseFilename = newFileName.slice(0, -ext.length)
          let counter = 1

          while (true) {
            const newFileNameWithCounter = `${baseFilename}(${counter})${ext}`
            targetFilePath = path.join(targetDir, newFileNameWithCounter)

            try {
              const exists = await checkFileExists(targetFilePath)
              if (!exists) {
                // 文件不存在，可以使用这个名称
                newFileName = newFileNameWithCounter
                break
              }
              // 文件存在，增加计数器继续尝试
              counter++
            }
            catch (error) {
              console.error('检查文件是否存在时出错:', error)
              // 出错时使用当前尝试的文件名
              newFileName = newFileNameWithCounter
              break
            }
          }
        }

        // 检查源文件是否存在
        try {
          await fs.access(filePath, fs.constants.F_OK)
          await fs.rename(filePath, targetFilePath)
          console.log(
            'Downloader: 移动文件成功，文件名:',
            newFileName,
            '源文件路径:',
            filePath,
            '目标文件路径:',
            targetFilePath,
          )

          // 添加到已移动文件路径列表
          movedFilePaths.push(targetFilePath)

          // 只在第一次设置时更新 taskMediaInfo 的 filePath
          if (taskMediaInfo.filePath === filePath) {
            taskMediaInfo.filePath = targetFilePath
          }
        }
        catch (error) {
          console.log('Downloader: 源文件不存在或移动失败:', filePath, error)
        }
      }

      // 将所有移动后的文件路径合并为一个逗号分隔的字符串
      if (movedFilePaths.length > 0) {
        taskMediaInfo.filePath = movedFilePaths.join(',')
      }

      return taskMediaInfo
    }
    catch (error) {
      console.error('移动文件失败:', error)
      throw error
    }
  }

  /**
   * 获取本地视频文件信息
   * @param filePath 视频文件路径
   * @returns 视频信息对象，包含宽度、高度、时长、帧率和音频比特率
   */
  async getLocalMediaInfo(filePath: string): Promise<MediaInfo> {
    try {
      // 使用 fluent-ffmpeg 获取视频信息
      return new Promise((resolve, reject) => {
        const mediaInfo: MediaInfo = {
          mediaType: 'other',
        }

        // 获取视频信息
        ffmpeg.ffprobe(filePath, (err, metadata) => {
          if (err) {
            console.error('获取视频信息时发生错误:', err)
            reject(err)
            return
          }

          // 从视频流中获取信息
          const videoStream = metadata.streams?.find(
            stream => stream.codec_type === 'video'
              && stream.codec_name !== 'mjpeg',
          )
          if (videoStream) {
            mediaInfo.mediaType = 'video'
            mediaInfo.resolutionWidth = videoStream.width
            mediaInfo.resolutionHeight = videoStream.height

            // 设置持续时间
            if (videoStream.duration) {
              mediaInfo.duration = Math.ceil(Number.parseFloat(videoStream.duration))
            }
          }

          // 如果视频流中没有持续时间，则使用格式中的持续时间
          if (
            !mediaInfo.duration
            && metadata.format
            && metadata.format.duration
          ) {
            mediaInfo.duration = Math.ceil(metadata.format.duration)
          }

          // 获取音频比特率
          const audioStream = metadata.streams?.find(
            stream => stream.codec_type === 'audio',
          )
          if (audioStream && audioStream.bit_rate) {
            const bitrate = Math.ceil(Number.parseFloat(audioStream.bit_rate))
            mediaInfo.bitrate = bitrate
            mediaInfo.mediaType = videoStream ? 'video' : 'audio'
          }

          const subtitleStream = metadata.streams?.find(
            stream => stream.codec_type === 'subtitle',
          )
          if (subtitleStream && !audioStream && !videoStream) {
            mediaInfo.mediaType = 'subtitle'
          }

          console.log('解析结果:', mediaInfo)

          if (!mediaInfo.resolutionWidth || !mediaInfo.resolutionHeight) {
            console.warn('无法解析视频尺寸')
          }

          resolve(mediaInfo)
        })
      })
    }
    catch {
      return {
        mediaType: 'other',
      }
    }
  }

  /**
   * 根据合并目录合并所有媒体流
   */
  public async mergeMediaFiles(
    inputDirs: DownloadResult[],
    outputFilename: string,
    setting: SettingStoreType,
    embedSubtitles: boolean,
    onProgress?: (progress: FFmpegProgress) => void,
    onComplete?: (result: { filePath: string, language?: string }[]) => void,
    onError?: (error: Error) => void,
    taskId?: string,
  ): Promise<void> {
    try {
      const {
        mediaInfo,
        imageFiles,
        allInputFiles,
        updatedInputDirs,
        originalImageFiles,
      } = await this.prepareMediaFiles(inputDirs, taskId)

      if (this.checkNoProcessableStreams(mediaInfo, embedSubtitles)) {
        const result = this.createUnusedFilesResult(allInputFiles, updatedInputDirs, setting.isDownloadThumbnail)
        onComplete?.(result)
        return
      }

      const { command, isAudioFormat } = this.prepareFFmpegCommand(
        mediaInfo,
        setting.downloadType === 'audio'
          ? setting.audioConfig.format.format
          : setting.videoConfig.format.format,
        taskId,
      )

      const outputFilenames = this.configureOutputFormat(command, mediaInfo, imageFiles, embedSubtitles, isAudioFormat, outputFilename, setting)

      await this.executeFFmpegCommand(
        command,
        isAudioFormat,
        inputDirs,
        outputFilenames,
        mediaInfo,
        embedSubtitles,
        allInputFiles,
        updatedInputDirs,
        originalImageFiles,
        setting,
        onProgress,
        onComplete,
        onError,
      )
    }
    catch (error) {
      this.handleError(error as Error, '合并媒体文件失败', onError)
    }
  }

  private async prepareMediaFiles(
    inputDirs: DownloadResult[],
    taskId?: string,
    outputFilename?: string,
  ) {
    const { updatedInputDirs, originalImageFiles } = await this.convertImagesToPng(inputDirs, taskId)
    if (outputFilename) {
      const data = await ensureDirectoryExists(outputFilename)
      if (!data.success) {
        logError('创建输出目录失败', { taskId, error: data.error })
        if (data.error.includes('not permitted') || data.error.includes('permission denied')) {
          // 更新任务状态为失败
          await taskService.updateTask(taskId, {
            taskStatus: taskStatus.failed,
            errorStatus: errorStatusEnum.extractError,
            errorMessage: errorMessageEnum.permissionDenied,
          })
        }
        else {
          logError('创建输出目录失败', { taskId, error: data.error })
          await taskService.updateTask(taskId, {
            taskStatus: taskStatus.failed,
            errorStatus: errorStatusEnum.extractError,
            errorMessage: errorMessageEnum.extractError,
          })
        }
        return
      }
    }
    const { mediaInfo, imageFiles, allInputFiles } = await this.analyzeMediaStreams(updatedInputDirs)

    return { mediaInfo, imageFiles, allInputFiles, updatedInputDirs, originalImageFiles }
  }

  private async convertImagesToPng(
    inputDirs: DownloadResult[],
    taskId?: string,
  ): Promise<{
      updatedInputDirs: DownloadResult[]
      originalImageFiles: string[]
    }> {
    const updatedInputDirs = [...inputDirs]

    const imageFiles = await Promise.all(
      updatedInputDirs.map(async item => ({
        item,
        isImage: await isImageFile(item.filePath),
      })),
    )
    const originalImageFiles = imageFiles.filter(({ isImage }) => isImage).map(({ item }) => item.filePath)

    const conversionPromises = imageFiles
      .filter(({ isImage }) => isImage)
      .map(({ item }) => this.handleImageConversion(
        item.filePath,
        updatedInputDirs,
        taskId,
      ))

    await Promise.all(conversionPromises)
    return { updatedInputDirs, originalImageFiles }
  }

  private async handleImageConversion(
    imgFile: string,
    updatedInputDirs: DownloadResult[],
    taskId?: string,
  ): Promise<void> {
    const newImgPath = getNewPngPath(imgFile)

    if (await checkFileExists(newImgPath)) {
      updateFilePath(updatedInputDirs, imgFile, newImgPath)
      return
    }

    const isAnimated = await this.isAnimatedImage(imgFile)
    if (isAnimated) {
      console.log(`检测到动图，保留原始格式: ${imgFile}`)
      return
    }

    try {
      await this.convertImageToPng(imgFile, newImgPath, updatedInputDirs, taskId)
    }
    catch (error) {
      console.error(`图片转换失败，继续使用原始图片: ${imgFile}`, error)
      // 转换失败时，确保仍使用原始图片路径
    }
  }

  private configureOutputFormat(
    command: ffmpeg.FfmpegCommand,
    mediaInfo: ProcessMediaInfo,
    imageFiles: string[],
    embedSubtitles: boolean,
    isAudioFormat: boolean,
    outputFilename: string,
    setting: SettingStoreType,
  ): string[] {
    if (isAudioFormat) {
      const outputFormat = setting.audioConfig.format.format
      return this.configureAudioOutput(command, mediaInfo, imageFiles, outputFormat, outputFilename)
    }
    else {
      const outputFormat = setting.videoConfig.format.format
      return this.configureVideoOutput(command, mediaInfo, embedSubtitles, outputFormat, outputFilename)
    }
  }

  private async executeFFmpegCommand(
    command: ffmpeg.FfmpegCommand,
    isAudioFormat: boolean,
    inputDirs: DownloadResult[],
    outputFilenames: string[],
    mediaInfo: ProcessMediaInfo,
    embedSubtitles: boolean,
    allInputFiles: string[],
    updatedInputDirs: DownloadResult[],
    imageFiles: string[],
    setting: SettingStoreType,
    onProgress?: (progress: FFmpegProgress) => void,
    onComplete?: (result: { filePath: string, language?: string }[]) => void,
    onError?: (error: Error) => void,
  ): Promise<void> {
    if (outputFilenames.length === 0) {
      return
    }

    try {
      await ensureDirectoryExists(getDirectoryPath(outputFilenames[0]))
    }
    catch (error) {
      this.handleError(error as Error, '创建输出目录失败', onError)
      return
    }

    command
      .addOption('-allowed_extensions ALL')
      .on('start', commandLine => console.log('FFmpeg 命令:', commandLine))
      .on('progress', progress => onProgress?.(progress))
      .on('end', () => this.handleFFmpegComplete(
        isAudioFormat,
        inputDirs,
        outputFilenames,
        mediaInfo,
        embedSubtitles,
        allInputFiles,
        updatedInputDirs,
        imageFiles,
        setting,
        onComplete,
        onError,
      ))
      .on('error', error => this.handleFFmpegError(
        error,
        allInputFiles,
        updatedInputDirs,
        setting,
        onComplete,
        onError,
      ))
      .run()
  }

  private handleError(error: Error, context: string, onError?: (error: Error) => void): void {
    console.error(`${context}:`, error)
    onError?.(error)
  }

  private handleFFmpegComplete(
    isAudioFormat: boolean,
    inputDirs: DownloadResult[],
    outputFilenames: string[],
    mediaInfo: ProcessMediaInfo,
    embedSubtitles: boolean,
    allInputFiles: string[],
    updatedInputDirs: DownloadResult[],
    imageFiles: string[],
    setting: SettingStoreType,
    onComplete?: (result: { filePath: string, language?: string }[]) => void,
    onError?: (error: Error) => void,
  ): void {
    try {
      this.buildResultArray(
        isAudioFormat,
        inputDirs,
        outputFilenames,
        mediaInfo,
        embedSubtitles,
        allInputFiles,
        updatedInputDirs,
        imageFiles,
        setting,
      ).then((result) => {
        onComplete?.(result)
      }).catch((error) => {
        this.handleError(error as Error, '处理FFmpeg结果时出错', onError)
        const fallbackResult = outputFilenames.map(filename => ({ filePath: filename }))
        onComplete?.(fallbackResult)
      })
    }
    catch (error) {
      this.handleError(error as Error, '处理FFmpeg结果时出错', onError)
      const fallbackResult = outputFilenames.map(filename => ({ filePath: filename }))
      onComplete?.(fallbackResult)
    }
  }

  private handleFFmpegError(
    error: Error,
    allInputFiles: string[],
    updatedInputDirs: DownloadResult[],
    setting: SettingStoreType,
    onComplete?: (result: { filePath: string, language?: string }[]) => void,
    onError?: (error: Error) => void,
  ): void {
    let fallbackResult = [] as { filePath: string, language?: string }[]
    if (error.message.includes('SIGKILL')) {
      fallbackResult = this.createUnusedFilesResult(allInputFiles, updatedInputDirs, setting.isDownloadThumbnail)
    }
    console.warn('FFmpeg执行出错:', error)
    onComplete?.(fallbackResult)
    onError?.(error)
  }

  createUnusedFilesResult(
    allInputFiles: string[],
    updatedInputDirs: DownloadResult[],
    isDownloadThumbnail: boolean,
  ): { filePath: string, language?: string }[] {
    console.log('\n没有找到任何可处理的媒体流，返回所有文件作为未使用的文件')
    return allInputFiles
      .map((file) => {
        const fileInfo = updatedInputDirs.find(info => info.filePath === file)
        return {
          filePath: file,
          ...(fileInfo?.language ? { language: fileInfo.language } : {}),
        }
      })
      .filter((item) => {
        // 如果设置了不保留图片，则过滤掉图片文件
        if (/\.(?:jpg|jpeg|png|gif|bmp|webp)$/i.test(item.filePath) && !isDownloadThumbnail) {
          return false
        }
        return true
      })
  }

  checkNoProcessableStreams(
    mediaInfo: ProcessMediaInfo,
    embedSubtitles: boolean,
  ): boolean {
    return (
      mediaInfo.video.length === 0
      && mediaInfo.audio.length === 0
      && (!embedSubtitles || mediaInfo.subtitle.length === 0)
    )
  }

  async analyzeMediaStreams(
    updatedInputDirs: DownloadResult[],
  ): Promise<{
      mediaInfo: ProcessMediaInfo
      imageFiles: string[]
      allInputFiles: string[]
    }> {
    // 从 updatedInputDirs 中提取文件信息
    const mediaInfo: ProcessMediaInfo = {
      video: [],
      audio: [],
      subtitle: [],
      image: [],
    }

    // 存储图片文件列表
    const imageFiles: string[] = []

    console.log('\n开始分析媒体流...')

    // 记录所有输入文件的路径，用于后续判断哪些文件未被使用
    const allInputFiles: string[] = []

    for (const fileInfo of updatedInputDirs) {
      const filePath = fileInfo.filePath
      allInputFiles.push(filePath)

      // 检查文件是否存在
      const isFileExists = await checkFileExists(filePath)
      if (!isFileExists) {
        console.warn(`文件不存在: ${filePath}`)
        continue
      }

      // 检查是否为目录
      const stat = await fs.stat(filePath)
      if (stat.isDirectory())
        continue

      try {
        // 检查是否为图片文件
        if (await isImageFile(filePath)) {
          console.log(`  检测到图片文件: ${filePath}`)
          imageFiles.push(filePath)
          continue
        }

        const info = await new Promise<FfprobeData>((resolve, reject) => {
          ffmpeg.ffprobe(filePath, (err, data) => {
            if (err)
              reject(err)
            else resolve(data)
          })
        })

        if (info.streams) {
          info.streams.forEach((stream: FFprobeStream) => {
            const streamInfo: StreamInfo = {
              file: filePath, // 使用完整路径
              index: stream.index,
              codec: stream.codec_name || '',
              language: fileInfo.language || stream.tags?.language || 'und',
              duration: info.format?.duration
                ? Number(info.format.duration).toString()
                : undefined,
            }

            switch (stream.codec_type) {
              case 'video':
                streamInfo.resolution = `${stream.width}x${stream.height}`
                // streamInfo.fps = eval(stream.r_frame_rate).toFixed(2)
                mediaInfo.video.push(streamInfo)
                console.log(
                  `  视频流: ${streamInfo.codec} (${streamInfo.resolution}, ${streamInfo.fps}fps)`,
                )
                break

              case 'audio':
                streamInfo.channels = stream.channels
                streamInfo.sample_rate = stream.sample_rate
                mediaInfo.audio.push(streamInfo)
                console.log(
                  `  音频流: ${streamInfo.codec} (${streamInfo.language}, ${streamInfo.channels}ch, ${streamInfo.sample_rate}Hz)`,
                )
                break

              case 'subtitle':
                mediaInfo.subtitle.push(streamInfo)
                console.log(
                  `  字幕流: ${streamInfo.codec} (${streamInfo.language})`,
                )
                break
            }
          })
        }
        else if (path.extname(filePath).match(/\.(srt|ass|ssa|vtt)$/i)) {
          mediaInfo.subtitle.push({
            file: filePath, // 使用完整路径
            type: 'external',
            language:
              fileInfo.language
              || path
                .basename(filePath)
                .match(/\.(zh-Hans|zh-Hant|en|ja|ko)/i)?.[1]
                || 'und',
            codec: '',
            index: 0,
          })
          console.log(
            `  外部字幕文件: ${filePath} (${fileInfo.language || 'und'})`,
          )
        }
      }
      catch (error) {
        console.error(`  分析失败: ${filePath}`, (error as Error).message)
      }
    }

    console.log('媒体流统计：')
    console.log(`视频流: ${mediaInfo.video.length} 个`)
    console.log(`音频流: ${mediaInfo.audio.length} 个`)
    console.log(`字幕流: ${mediaInfo.subtitle.length} 个`)

    return { mediaInfo, imageFiles, allInputFiles }
  }

  filterRealVideoStreams(videoStreams: StreamInfo[]): StreamInfo[] {
    return videoStreams.filter((stream) => {
      // 排除被识别为视频的图片文件
      const ext = path.extname(stream.file).toLowerCase()
      const isImageFile = [
        '.jpg',
        '.jpeg',
        '.png',
        '.gif',
        '.bmp',
        '.webp',
      ].includes(ext)

      // 检查编解码器是否为图像格式
      const isImageCodec
        = stream.codec?.toLowerCase().includes('mjpeg')
          || stream.codec?.toLowerCase().includes('png')
          || stream.codec?.toLowerCase().includes('jpeg')

      // 排除分辨率为0x0的视频（通常是静态图片）
      const isZeroResolution = stream.resolution === '0x0'

      const isRealVideo = !isImageFile && !isImageCodec && !isZeroResolution

      if (!isRealVideo) {
        console.log(
          `排除非真实视频流: ${stream.file} (${stream.codec}, ${stream.resolution})`,
        )
      }

      return isRealVideo
    })
  }

  prepareFFmpegCommand(
    mediaInfo: ProcessMediaInfo,
    outputFormat: string,
    taskId: string,
  ): {
      command: ffmpeg.FfmpegCommand
      isAudioFormat: boolean
    } {
    const command = ffmpeg()

    console.log(`为媒体处理注册主要FFmpeg进程, taskId: ${taskId}`)
    this.registerFFmpegProcess(taskId, command)

    const isAudioFormat
      = ['m4a', 'mp3', 'ogg'].includes(outputFormat.toLowerCase())
        || mediaInfo.video.length === 0

    if (
      isAudioFormat
      && outputFormat !== 'mp3'
      && outputFormat !== 'm4a'
      && outputFormat !== 'ogg'
    ) {
      outputFormat = settingStore.get('audioConfig').format.format
    }

    return { command, isAudioFormat }
  }

  async convertImageToPng(
    imgFile: string,
    newImgPath: string,
    updatedInputDirs: DownloadResult[],
    taskId?: string,
  ): Promise<void> {
    const command = ffmpeg(imgFile)

    // 如果提供了taskId，则注册该ffmpeg进程
    if (taskId) {
      console.log(
        `为图片转换注册FFmpeg进程: ${imgFile} -> ${newImgPath}, taskId: ${taskId}`,
      )
      this.registerFFmpegProcess(taskId, command)
    }
    else {
      console.log(
        `图片转换未提供taskId，无法注册进程: ${imgFile} -> ${newImgPath}`,
      )
    }

    // 返回一个Promise，当FFmpeg操作完成时才解析
    return new Promise<void>((resolve, reject) => {
      command
        .outputOptions(['-c:v', 'png'])
        .on('end', () => {
          // 更新inputDirs中的图片路径
          const index = updatedInputDirs.findIndex(
            item => item.filePath === imgFile,
          )
          if (index !== -1) {
            updatedInputDirs[index].filePath = newImgPath
            updatedInputDirs[index].type = 'thumbnail'
            console.log(`图片已转换: ${imgFile} -> ${newImgPath}`)
          }
          resolve() // 转换完成时解析Promise
        })
        .on('error', (err: Error) => {
          console.error(`图片转换失败: ${imgFile}`, err)
          // 转换失败时不更新路径，继续使用原始图片
          reject(err) // 转换失败时拒绝Promise
        })
        .save(newImgPath)
    })
  }

  /**
   * 取消指定任务ID关联的所有FFmpeg进程
   * @param {string} taskId - 要取消的任务ID
   * @description
   * 该方法会终止所有与指定taskId关联的FFmpeg进程，并从进程管理器中移除相关记录。
   * 如果找不到对应的进程记录，将输出提示信息。
   */
  public cancelFFmpegProcess(taskId: string, signal: string = 'SIGKILL'): void {
    console.log('取消合并任务:', taskId)
    const processes = this.ffmpegProcesses.get(taskId)

    if (processes && processes.length > 0) {
      // 终止所有与该taskId关联的ffmpeg进程
      for (const process of processes) {
        try {
          process.kill(signal)
          console.log(`已终止taskId ${taskId}的一个FFmpeg进程`)
        }
        catch (error) {
          console.error('终止FFmpeg进程失败:', error)
        }
      }
      // 清除该taskId的所有进程记录
      this.ffmpegProcesses.delete(taskId)
      console.log(`已清除taskId ${taskId}的所有FFmpeg进程记录`)
    }
    else {
      console.log(`没有找到taskId ${taskId}的FFmpeg进程记录，无法取消`)
    }
  }

  /**
   * 注册FFmpeg进程到任务管理器
   * @param {string} taskId - 任务ID
   * @param {ffmpeg.FfmpegCommand} process - FFmpeg进程实例
   * @description
   * 该方法将FFmpeg进程实例注册到指定taskId的进程队列中。
   * 如果taskId不存在，会先创建一个新的进程队列。
   * 每次注册都会更新进程计数并输出日志信息。
   * @private
   */
  private registerFFmpegProcess(
    taskId: string,
    process: ffmpeg.FfmpegCommand,
  ): void {
    if (!this.ffmpegProcesses.has(taskId)) {
      this.ffmpegProcesses.set(taskId, [])
      console.log(`已为任务 ${taskId} 创建新的FFmpeg进程队列`)
    }
    this.ffmpegProcesses.get(taskId)?.push(process)
    console.log(
      `任务 ${taskId} 已注册FFmpeg进程，当前进程数: ${this.ffmpegProcesses.get(taskId)?.length}`,
    )
  }

  private isAnimatedImage(filePath: string): Promise<boolean> {
    return new Promise<boolean>((resolve) => {
      try {
        ffmpeg.ffprobe(filePath, (err, data) => {
          if (err) {
            console.error('检查图片是否为动图时发生错误:', err)
            resolve(false) // 如果出错，默认返回静态图
            return
          }

          const ext = path.extname(filePath).toLowerCase()

          if (ext === '.gif') {
            // GIF 始终视为动图
            resolve(true)
            return
          }

          // 获取视频流
          const videoStream = data.streams.find(
            stream => stream.codec_type === 'video',
          )

          if (!videoStream) {
            resolve(false) // 如果没有视频流，则认为是静态图片
            return
          }

          // 检查帧数
          const frameCount = videoStream.nb_frames

          // 对于非 GIF 格式，帧数为空时视为静态图
          resolve(frameCount !== undefined && Number.parseInt(frameCount as string) > 1)
        })
      }
      catch (error) {
        console.error('判断是否为动图时发生未知错误:', error)
        resolve(false) // 出错时默认返回静态图
      }
    })
  }

  private configureAudioOutput(
    command: ffmpeg.FfmpegCommand,
    mediaInfo: ProcessMediaInfo,
    imageFiles: string[],
    outputFormat: string,
    outputFilename: string,
  ): string[] {
    if (mediaInfo.video.length === 0 && mediaInfo.audio.length === 0) {
      console.log('没有找到任何音视频流，无法处理')
      return []
    }

    // const realVideoStreams = this.filterRealVideoStreams(mediaInfo.video)

    // const mediaInfos = [...realVideoStreams, ...mediaInfo.audio]

    // 优先使用音频文件作为源文件，特别是对于音频输出格式
    // 只有在没有音频流且有真实视频流时才使用视频文件
    const sourceFiles = mediaInfo.audio.map(item => item.file)
    console.log(
      `选择 ${mediaInfo.audio.length > 0 ? '音频文件' : '视频文件'} 作为源：${sourceFiles}`,
    )
    // console.log(
    //   `${realVideoStreams.length > 0 ? '检测到真实视频流，将提取音频' : '没有真实视频流，将处理音频'}并转换为 ${outputFormat} 格式`,
    // )

    const finalOutputFilenames: string[] = []
    for (const [index, sourceFile] of sourceFiles.entries()) {
      const outputOptions: string[] = []
      // 输入文件
      command.input(sourceFile)
      this.configureAudioCodec(index, outputFormat, outputOptions)
      const finalOutputFilename = index === 0 ? `${outputFilename}.${outputFormat}` : `${outputFilename}(${index + 1}).${outputFormat}`
      finalOutputFilenames.push(finalOutputFilename)
      this.handleAudioCover(command, imageFiles, index, sourceFiles.length, outputFormat, outputOptions, finalOutputFilename)
    }

    if (imageFiles.length > 0) {
      console.log(`添加图片作为音频封面: ${imageFiles[0]}`)
      command.input(imageFiles[0])
    }
    return finalOutputFilenames
  }

  private configureVideoOutput(
    command: ffmpeg.FfmpegCommand,
    mediaInfo: ProcessMediaInfo,
    embedSubtitles: boolean,
    outputFormat: string,
    outputFilename: string,
  ): string[] {
    const realVideoStreams = this.filterRealVideoStreams(mediaInfo.video)
    const outputOptions: string[] = []

    this.addVideoInputs(command, realVideoStreams, mediaInfo)
    this.addAudioInputs(command, mediaInfo)
    this.addSubtitleInputs(command, mediaInfo, embedSubtitles)

    const { videoInputIndices, audioInputIndices, subtitleInputIndices }
      = this.calculateInputIndices(realVideoStreams, mediaInfo, embedSubtitles)

    this.configureVideoCodec(outputFormat, outputOptions, videoInputIndices, mediaInfo)
    this.configureAudioStreams(outputOptions, audioInputIndices, mediaInfo)
    this.configureSubtitleStreams(outputOptions, subtitleInputIndices, mediaInfo, embedSubtitles)

    command.outputOptions(outputOptions)
    const finalOutputFilename = `${outputFilename}.${outputFormat}`
    command.output(finalOutputFilename)
    return [finalOutputFilename]
  }

  private async buildResultArray(
    isAudioFormat: boolean,
    inputDirs: DownloadResult[],
    outputFilenames: string[],
    mediaInfo: ProcessMediaInfo,
    embedSubtitles: boolean,
    allInputFiles: string[],
    updatedInputDirs: DownloadResult[],
    imageFiles: string[],
    setting: SettingStoreType,
  ): Promise<{ filePath: string, language?: string }[]> {
    const result: { filePath: string, language?: string }[] = []
    const outputLanguages: string[] = []
    inputDirs.forEach((dir) => {
      if (dir.language) {
        outputLanguages.push(dir.language)
      }
    })
    for (const [index, outputFilename] of outputFilenames.entries()) {
      result.push({ filePath: outputFilename, language: isAudioFormat ? outputLanguages[index] : undefined })
    }

    const usedFiles = [ // 参与合并的文件列表
      ...mediaInfo.video.map(stream => stream.file),
      ...mediaInfo.audio.map(stream => stream.file),
      ...mediaInfo.subtitle.map(stream => stream.file),
      ...mediaInfo.image,
    ]

    // 处理非嵌入的字幕文件
    if (!embedSubtitles && mediaInfo.subtitle.length > 0) {
      const subtitleFiles = await this.processStandaloneSubtitles(mediaInfo.subtitle, outputFilenames[0])
      result.push(...subtitleFiles)
    }

    if (setting.isDownloadThumbnail && imageFiles.length > 0) {
      result.push({ filePath: imageFiles[0] })
    }

    const unusedFiles = allInputFiles.filter(file => !usedFiles.includes(file))
    console.log('未使用的文件:', unusedFiles)

    unusedFiles.forEach((file) => {
      const fileInfo = updatedInputDirs.find(info => info.filePath === file)
      if (fileInfo?.type === 'thumbnail') {
        return
      }
      result.push({
        filePath: file,
        ...(fileInfo?.language ? { language: fileInfo.language } : {}),
      })
    })

    return result
  }

  /**
   * 处理独立的字幕文件（当不嵌入字幕时）
   *
   * @param subtitles - 字幕流信息数组
   * @param outputVideoPath - 输出视频文件的路径
   * @returns 处理后的字幕文件信息数组
   */
  private async processStandaloneSubtitles(
    subtitles: StreamInfo[],
    outputVideoPath: string,
  ): Promise<{ filePath: string, language: string }[]> {
    const result: { filePath: string, language: string }[] = []
    const outputDir = path.dirname(outputVideoPath)

    for (const subtitle of subtitles) {
      try {
        const outputSubtitleName = path.basename(subtitle.file, path.extname(subtitle.file))
        // 获取语言名称
        const language = subtitle.language || 'und'
        const languageName = getLanguageName(language)

        // 创建新的字幕文件名
        const subtitleExt = '.srt'
        const newSubtitleFileName = `[${languageName}]${outputSubtitleName}${subtitleExt}`
        const newSubtitlePath = path.join(outputDir, newSubtitleFileName)

        // 检查源字幕文件扩展名
        const sourceExt = path.extname(subtitle.file).toLowerCase()

        if (sourceExt === '.srt') {
          // 如果已经是SRT格式，直接复制
          try {
            await fs.copyFile(subtitle.file, newSubtitlePath)
            console.log(`字幕文件已保存: ${newSubtitlePath}`)

            result.push({
              filePath: newSubtitlePath,
              language,
            })
          }
          catch (err) {
            console.error(`复制字幕文件失败: ${subtitle.file} -> ${newSubtitlePath}`, err)
          }
        }
        else {
          // 如果不是SRT格式，使用FFmpeg转换
          try {
            await this.convertSubtitleToSrt(subtitle.file, newSubtitlePath)
            console.log(`字幕文件已转换并保存: ${newSubtitlePath}`)

            result.push({
              filePath: newSubtitlePath,
              language,
            })
          }
          catch (err) {
            console.error(`转换字幕文件失败: ${subtitle.file} -> ${newSubtitlePath}`, err)

            // 转换失败时尝试直接复制
            try {
              await fs.copyFile(subtitle.file, newSubtitlePath.replace(subtitleExt, sourceExt))
              const originalFormatPath = newSubtitlePath.replace(subtitleExt, sourceExt)
              console.log(`字幕文件已以原始格式保存: ${originalFormatPath}`)

              result.push({
                filePath: originalFormatPath,
                language,
              })
            }
            catch (copyErr) {
              console.error(`复制原始字幕文件也失败: ${subtitle.file}`, copyErr)
            }
          }
        }
      }
      catch (error) {
        console.error(`处理字幕文件时出错: ${subtitle.file}`, error)
      }
    }

    return result
  }

  /**
   * 将字幕文件转换为SRT格式
   *
   * @param inputPath - 输入字幕文件路径
   * @param outputPath - 输出SRT文件路径
   * @returns 转换成功的Promise
   */
  private convertSubtitleToSrt(inputPath: string, outputPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .outputOptions(['-c:s', 'srt'])
        .on('end', () => {
          console.log(`字幕转换成功: ${inputPath} -> ${outputPath}`)
          resolve()
        })
        .on('error', (err) => {
          console.error(`字幕转换失败: ${inputPath}`, err)
          reject(err)
        })
        .save(outputPath)
    })
  }

  private handleAudioCover(
    command: ffmpeg.FfmpegCommand,
    imageFiles: string[],
    index: number,
    total: number,
    outputFormat: string,
    outputOptions: string[],
    outputFilename: string,
  ): void {
    command.output(outputFilename)
    // if (outputFormat.toLowerCase() === 'ogg') {
    //   outputOptions.push(
    //     '-map',
    //     `${index}:a:0`,
    //     '-map',
    //     `${total}:v`,
    //     '-qscale:v',
    //     '10',
    //     '-disposition:v',
    //     'attached_pic',
    //   )
    // }
    // else {
    //   outputOptions.push(
    //     '-map',
    //     `${index}:a:0`,
    //     '-map',
    //     `${total}:v`,
    //     '-c:v',
    //     'mjpeg',
    //     '-disposition:v',
    //     'attached_pic',
    //   )
    // }

    if (imageFiles.length > 0) {
      // 添加封面
      // -map 2:v:0 -c:v mjpeg -disposition:v attached_pic
      let coverOptions = [
        '-map',
        `${total}:v:0`,
      ]
      if (outputFormat.toLowerCase() === 'ogg') {
        coverOptions = []
      }
      coverOptions = [
        ...coverOptions,
        '-c:v',
        'mjpeg',
        '-disposition:v',
        'attached_pic',
      ]
      outputOptions.push(...coverOptions)
    }
    command.outputOptions(outputOptions)
  }

  private configureAudioCodec(index: number, outputFormat: string, outputOptions: string[]): void {
    // 编码器
    let codec = ''
    let options = []
    switch (outputFormat.toLowerCase()) {
      case 'mp3':
        codec = 'libmp3lame'
        break
      case 'm4a':
        codec = 'aac'
        break
      case 'ogg':
        codec = 'libvorbis'
        options = ['-map_metadata', '-1']
        break
    }
    outputOptions.push('-map', `${index}:a:0`, '-c:a', codec, ...options)
  }

  /**
   * 添加视频输入
   *
   * @param command - FFmpeg 命令实例
   * @param realVideoStreams - 经过过滤的真实视频流数组（排除了可能被误识别为视频的图片）
   * @param mediaInfo - 包含所有媒体流信息的对象（视频、音频、字幕）
   */
  private addVideoInputs(
    command: ffmpeg.FfmpegCommand,
    realVideoStreams: StreamInfo[],
    mediaInfo: ProcessMediaInfo,
  ): void {
    if (realVideoStreams.length > 0) {
      for (const stream of realVideoStreams) {
        console.log('添加真实视频输入:', stream.file)
        command.input(stream.file)
      }
    }
    else if (mediaInfo.video.length > 0) {
      for (const stream of mediaInfo.video) {
        console.log('添加视频输入 (可能是图片):', stream.file)
        command.input(stream.file)
      }
    }
  }

  /**
   * 添加音频输入
   *
   * @param command - FFmpeg 命令实例
   * @param mediaInfo - 包含所有媒体流信息的对象（视频、音频、字幕）
   */
  private addAudioInputs(
    command: ffmpeg.FfmpegCommand,
    mediaInfo: ProcessMediaInfo,
  ): void {
    for (const stream of mediaInfo.audio) {
      console.log('添加音频输入:', stream.file)
      command.input(stream.file)
    }
  }

  /**
   * 添加字幕输入
   *
   * @param command - FFmpeg 命令实例
   * @param mediaInfo - 包含所有媒体流信息的对象（视频、音频、字幕）
   * @param embedSubtitles - 是否需要嵌入字幕的标志
   */
  private addSubtitleInputs(
    command: ffmpeg.FfmpegCommand,
    mediaInfo: ProcessMediaInfo,
    embedSubtitles: boolean,
  ): void {
    if (embedSubtitles) {
      for (const stream of mediaInfo.subtitle) {
        console.log('添加字幕输入:', stream.file)
        command.input(stream.file)
      }
    }
  }

  /**
   * 计算 FFmpeg 命令中不同媒体流（视频、音频、字幕）的输入索引
   * 这些索引用于在 FFmpeg 命令中通过 -map 参数正确映射各个媒体流
   *
   * @param realVideoStreams - 经过过滤的真实视频流数组（排除了可能被误识别为视频的图片）
   * @param mediaInfo - 包含所有媒体流信息的对象（视频、音频、字幕）
   * @param embedSubtitles - 是否需要嵌入字幕的标志
   *
   * @returns 包含三种媒体流索引数组的对象
   *          - videoInputIndices: 视频流的输入索引数组
   *          - audioInputIndices: 音频流的输入索引数组
   *          - subtitleInputIndices: 字幕流的输入索引数组
   */
  private calculateInputIndices(
    realVideoStreams: StreamInfo[],
    mediaInfo: ProcessMediaInfo,
    embedSubtitles: boolean,
  ): {
      videoInputIndices: number[]
      audioInputIndices: number[]
      subtitleInputIndices: number[]
    } {
    let inputIndex = 0
    const videoInputIndices = (realVideoStreams.length > 0 ? realVideoStreams : mediaInfo.video)
      .map(() => inputIndex++)
    const audioInputIndices = mediaInfo.audio.map(() => inputIndex++)
    const subtitleInputIndices = embedSubtitles ? mediaInfo.subtitle.map(() => inputIndex++) : []

    return { videoInputIndices, audioInputIndices, subtitleInputIndices }
  }

  private configureVideoCodec(
    outputFormat: string,
    outputOptions: string[],
    videoInputIndices: number[],
    mediaInfo: ProcessMediaInfo,
  ): void {
    if (videoInputIndices.length > 0) {
      switch (outputFormat) {
        case 'mp4': {
          const videoStream = mediaInfo.video[0]

          const supportedCodecs = ['h264', 'h256', 'avc1', 'hevc', 'vp09', 'vp9', 'av1', 'av01']

          if (videoStream.codec && supportedCodecs.some(codec => videoStream.codec.toLowerCase().includes(codec))) {
            outputOptions.push('-c:v', 'copy')
          }
          else {
            outputOptions.push('-c:v', 'libx264')
          }

          // mp4支持的音频编码
          const audioStreamList = mediaInfo.audio
          const audioCodecs = ['aac'] // 此处配置下载MP4时，哪些音频不进行转码

          // 检查是否所有音频流都可以直接复制
          const canCopyAllAudio = audioStreamList.every(
            audioStream => audioStream.codec && audioCodecs.some(
              codec => audioStream.codec?.toLowerCase().includes(codec),
            ),
          )

          if (canCopyAllAudio) {
            outputOptions.push('-c:a', 'copy')
          }
          else {
            outputOptions.push('-c:a', 'aac')
          }

          outputOptions.push('-c:s', 'mov_text')
          break
        }
        case 'mkv':
          outputOptions.push('-c:v', 'copy')
          outputOptions.push('-c:a', 'copy')
          outputOptions.push('-c:s', 'copy')
          break
        default:
          outputOptions.push('-c:v', 'copy')
          outputOptions.push('-c:a', 'copy')
          outputOptions.push('-c:s', 'copy')
          break
      }
      outputOptions.push('-map', `${videoInputIndices[0]}:v:0`)
    }
  }

  private configureAudioStreams(
    outputOptions: string[],
    audioInputIndices: number[],
    mediaInfo: ProcessMediaInfo,
  ): void {
    audioInputIndices.forEach((inputIdx, index) => {
      outputOptions.push('-map', `${inputIdx}:a:0`)
      const audioStream = mediaInfo.audio[index]
      if (audioStream.language) {
        this.addLanguageMetadata(outputOptions, 'a', index, audioStream.language)
      }
    })
  }

  private configureSubtitleStreams(
    outputOptions: string[],
    subtitleInputIndices: number[],
    mediaInfo: ProcessMediaInfo,
    embedSubtitles: boolean,
  ): void {
    if (embedSubtitles) {
      subtitleInputIndices.forEach((inputIdx, index) => {
        outputOptions.push('-map', `${inputIdx}:s?`)
        const stream = mediaInfo.subtitle[index]
        if (stream.language) {
          this.addLanguageMetadata(outputOptions, 's', index, stream.language)
        }
      })
    }
  }

  private addLanguageMetadata(
    outputOptions: string[],
    streamType: string,
    index: number,
    language: string,
  ): void {
    const langCode = getISOLanguageCode(language)
    console.log(`设置${streamType === 'a' ? '音频' : '字幕'}流 ${index} 的语言为: ${language} -> ${langCode}`)

    outputOptions.push(`-metadata:s:${streamType}:${index}`, `language=${langCode}`)

    const langKey = language.toLowerCase()
    const simpleLangKey = langKey.split('-')[0]
    let title = SUBTITLE_LANGUAGES[langKey] || SUBTITLE_LANGUAGES[simpleLangKey] || ''

    if (!title) {
      const capitalizedKey = langKey
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join('-')
      title = SUBTITLE_LANGUAGES[capitalizedKey] || ''
    }

    if (title) {
      const escapedTitle = title.replace(/'/g, '\'\\\'\'')
      outputOptions.push(`-metadata:s:${streamType}:${index}`, `title=${escapedTitle}`)
    }
  }
}

export default new FFmpegService()
