import {
  SnapfileErrorMapping,
  SnapfileErrorStatusCodes,
  SnapfileStatusMapping,
} from '../constants/snapfile'
import { errorStatusEnum } from '../constants/status'

/**
 * Snapfile 相关的工具函数
 */

/**
 * 判断状态码是否为错误状态码
 * @param statusCode 状态码
 * @returns 是否为错误状态码
 */
export function isSnapfileErrorCode(statusCode: string): boolean {
  return SnapfileErrorStatusCodes.has(statusCode as any)
}

/**
 * 将snapfile状态映射到项目TaskStatus
 * @param snapfileStatus snapfile状态
 * @returns 对应的项目TaskStatus
 */
export function mapSnapfileStatusToTaskStatus(snapfileStatus: string): 'readyDownload' | 'downloading' | 'pendingConversion' | 'converting' {
  if (SnapfileStatusMapping.READY_DOWNLOAD_STATUSES.includes(snapfileStatus as any)) {
    return 'readyDownload'
  }
  if (SnapfileStatusMapping.DOWNLOADING_STATUSES.includes(snapfileStatus as any)) {
    return 'downloading'
  }
  if (SnapfileStatusMapping.PENDING_CONVERSION_STATUSES.includes(snapfileStatus as any)) {
    return 'pendingConversion'
  }
  if (SnapfileStatusMapping.CONVERTING_STATUSES.includes(snapfileStatus as any)) {
    return 'converting'
  }
  // 默认返回下载中状态
  return 'downloading'
}

/**
 * 将snapfile错误状态码映射到项目错误状态
 * @param errorCode snapfile错误状态码
 * @returns 对应的项目ErrorStatus
 */
export function mapSnapfileErrorToErrorStatus(errorCode: string): string {
  return SnapfileErrorMapping[errorCode as keyof typeof SnapfileErrorMapping] || errorStatusEnum.downloadError
}
