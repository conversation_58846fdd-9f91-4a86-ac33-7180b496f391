import path from 'node:path'
import process from 'node:process'

import { __dirname, copyFileHelper } from './helper.mjs'

function main() {
  try {
    const binDir = path.join(__dirname, '..', 'public', 'bin')

    // 复制 ffmpeg
    copyFileHelper(
      path.join(binDir, 'ffmpeg-x64'),
      path.join(binDir, 'ffmpeg'),
      'ffmpeg',
      'x64',
    )

    // 复制 ffprobe
    copyFileHelper(
      path.join(binDir, 'ffprobe-x64'),
      path.join(binDir, 'ffprobe'),
      'ffprobe',
      'x64',
    )

    // 复制 snapfile
    copyFileHelper(
      path.join(binDir, 'snapfile-x64'),
      path.join(binDir, 'snapfile'),
      'snapfile',
      'x64',
    )
  }
  catch (error) {
    console.error('❌ 复制失败:', error.message)
    process.exit(1)
  }
}

main()
