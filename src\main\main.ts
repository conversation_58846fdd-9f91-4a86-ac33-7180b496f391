import type { BrowserWindow } from 'electron'
import { isMac } from '@main/constants/env'
import { deviceId } from '@main/constants/system'
import { logError, logInfo } from '@main/lib/logger'
import ProxyService from '@main/service/proxy'
import snapfileService from '@main/service/snapfile'
import {
  createMainWindow,
  destroyMainWindow,
  getMainWindowOrCreate,
} from '@main/window'
import { app } from 'electron'
import { initializeLibs } from './init'

logInfo('应用启动，设备ID', { deviceId })
// 初始化所有依赖
initializeLibs()

let mainWindow: BrowserWindow | null

app.whenReady().then(async () => {
  // 设置初始代理
  await ProxyService.setupProxy()
  // 创建主窗口
  createMainWindow()
})

app.on('window-all-closed', async () => {
  if (!isMac) {
    // 停止snapfile服务
    try {
      await snapfileService.stop()
      logInfo('Snapfile服务已停止', { reason: 'window-all-closed' })
    }
    catch (error) {
      logError('停止Snapfile服务失败', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        reason: 'window-all-closed',
      })
    }
    destroyMainWindow()
    app.quit()
  }
})

app.on('activate', () => {
  mainWindow = getMainWindowOrCreate()
  mainWindow.show()
})
