import type { UpdateInfo } from '@main/types'

export function compareSoftwareVersions(v1: string, v2: string): number {
  const v1Parts = v1.split('.').map(Number)
  const v2Parts = v2.split('.').map(Number)

  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1Part = v1Parts[i] || 0
    const v2Part = v2Parts[i] || 0
    if (v1Part > v2Part)
      return 1
    if (v1Part < v2Part)
      return -1
  }
  return 0
}

/**
 * 获取软件信息
 * @returns 软件信息
 */
export async function getSoftwareInfo(): Promise<UpdateInfo> {
  const response = await fetch('https://api.snapany.com/desktop/info')
  const data = await response.json()
  return data as UpdateInfo
}
