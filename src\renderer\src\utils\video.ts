// 格式化时长
export function formatDuration(duration: number | string) {
  if (typeof duration === 'string' || Object.prototype.toString.call(duration) === '[object Null]')
    return '--'
  const totalSeconds = Math.floor(duration)

  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

// 格式化文件大小
export function formatFileSize(bytes: number) {
  if (!bytes)
    return 'Unknown'
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  switch (unitIndex) {
    case 3:
      return `${size.toFixed(2)} ${units[unitIndex]}`
    case 2:
      return `${size.toFixed(1)} ${units[unitIndex]}`
    default:
      return `${size.toFixed()} ${units[unitIndex]}`
  }
}
