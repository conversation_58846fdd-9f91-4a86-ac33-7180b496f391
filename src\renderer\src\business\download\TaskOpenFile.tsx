import type { TaskItemProps } from '@renderer/business/download/TaskItem'
import { isMac } from '@/utils/environment'
import Popconfirm from '@renderer/components/Popconfirm'
import { Tooltip } from 'flowbite-react'

import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { HiOutlineFolderOpen } from 'react-icons/hi'

type TaskOpenFileProps = Pick<TaskItemProps, 'onOpen' | 'onDelete' | 'task'> & {
  className?: string
}

function TaskOpenFile({
  onDelete,
  onOpen,
  task,
  className,
}: TaskOpenFileProps) {
  const [isOpenFailed, setIsOpenFailed] = useState(false)
  const { t } = useTranslation()

  return (
    <Popconfirm
      title={(
        <div className="max-w-[181px] text-center">
          {t('dialogs.fileDeleted.fileHasBeenDeletedOrMoved')}
        </div>
      )}
      okText={t('dialogs.fileDeleted.remove')}
      placement="top"
      disabled={!isOpenFailed}
      onConfirm={() => onDelete?.(task)}
    >
      <Tooltip
        content={
          isMac ? t('taskActions.showInFinder') : t('taskActions.showInFolder')
        }
      >
        <HiOutlineFolderOpen
          className={className}
          onClick={(e) => {
            if (isOpenFailed) {
              return
            }

            onOpen?.(task).then(({ success }) => {
              if (!success) {
                setIsOpenFailed(true)
                setTimeout(() => {
                  // 再模拟一次点击已达到非受控 Popconfirm 效果
                  e.target?.dispatchEvent(
                    new MouseEvent('click', { bubbles: true }),
                  )
                }, 0)
              }
            })
          }}
        />
      </Tooltip>
    </Popconfirm>
  )
}

export default TaskOpenFile
