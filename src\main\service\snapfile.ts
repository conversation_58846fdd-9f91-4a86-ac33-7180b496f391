import type {
  Snap<PERSON>leCompleteData,
  SnapfileErrorData,
  SnapfileEvent,
  SnapfileProgressData,
  SnapfileResponse,
  SnapfileStatusData,
  SnapfileTaskStatus,
  StartTaskPayload,
} from '@main/types/snapfile'
import type { ChildProcess } from 'node:child_process'
import { spawn } from 'node:child_process'
import { EventEmitter } from 'node:events'
import fs from 'node:fs/promises'
import { SnapfileCallbackEvent, SnapfileCallbackType, SnapfileEventType, SnapfileStatusCode, SnapfileStatusHandlers } from '@main/constants/snapfile'
import { logError, logInfo, logWarn } from '@main/lib/logger'
import { getBinPath } from '@main/utils'
import { isSnapfileErrorCode, mapSnapfileErrorToErrorStatus } from '@main/utils/snapfile'

export class SnapfileService extends EventEmitter {
  private process: ChildProcess | null = null
  private isRunning = false
  private readonly executablePath: string
  private readonly maxDownloadingTasks: number
  private readonly activeTasks = new Map<string, StartTaskPayload>()
  private readonly taskCallbacks = new Map<string, {
    onProgress?: (progress: SnapfileProgressData) => void
    onStatusChange?: (status: SnapfileTaskStatus, data: SnapfileStatusData) => void
    onComplete?: (data: SnapfileCompleteData) => void
    onError?: (error: SnapfileErrorData) => void
  }>()

  // 进程恢复机制
  private restartAttempts = 0
  private readonly maxRestartAttempts = 3
  private readonly pendingTasks: StartTaskPayload[] = []
  // 标记是否正在主动关闭服务（用于禁用自动重启）
  private isShuttingDown = false

  constructor(options?: {
    maxDownloadingTasks?: number
  }) {
    super()

    // 设置snapfile可执行文件路径
    this.executablePath = getBinPath('snapfile')
    // this.executablePath = path.join(process.cwd(), 'public', 'bin', 'snapfile.exe')
    this.maxDownloadingTasks = options?.maxDownloadingTasks || 5
  }

  /**
   * 检查snapfile可执行文件是否存在
   */
  async checkExecutable(): Promise<boolean> {
    try {
      await fs.access(this.executablePath)
      return true
    }
    catch {
      return false
    }
  }

  /**
   * 启动snapfile进程
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      logInfo('Snapfile进程已在运行', {
        executablePath: this.executablePath,
        maxDownloadingTasks: this.maxDownloadingTasks,
      })
      return
    }

    // 检查可执行文件是否存在
    const executableExists = await this.checkExecutable()
    if (!executableExists) {
      throw new Error(`Snapfile可执行文件不存在: ${this.executablePath}`)
    }

    return new Promise((resolve, reject) => {
      const args = [
        '--ffmpeg-path',
        getBinPath('ffmpeg'),
        '--ffprobe-path',
        getBinPath('ffprobe'),
        '--max-downloading-task',
        this.maxDownloadingTasks.toString(),
        '--log-level',
        'debug',
      ]

      logInfo('启动Snapfile进程', {
        executablePath: this.executablePath,
        args: args.join(' '),
        maxDownloadingTasks: this.maxDownloadingTasks,
      })

      this.process = spawn(this.executablePath, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
      })

      this.process.on('spawn', () => {
        logInfo('Snapfile进程启动成功', {
          executablePath: this.executablePath,
          pid: this.process?.pid,
          activeTasks: this.activeTasks.size,
          pendingTasks: this.pendingTasks.length,
        })
        this.isRunning = true
        this.isShuttingDown = false // 重置关闭标志
        this.setupEventHandlers()
        this.resetRestartAttempts() // 重置重启计数器
        resolve()
      })

      this.process.on('error', (error) => {
        logError('Snapfile进程启动失败', {
          executablePath: this.executablePath,
          error: error.message,
          stack: error.stack,
          activeTasks: this.activeTasks.size,
          pendingTasks: this.pendingTasks.length,
        })
        this.isRunning = false
        reject(error)
      })

      this.process.on('exit', (code, signal) => {
        logInfo('Snapfile进程退出', {
          exitCode: code,
          signal,
          activeTasks: this.activeTasks.size,
          pendingTasks: this.pendingTasks.length,
          isShuttingDown: this.isShuttingDown,
          restartAttempts: this.restartAttempts,
        })
        this.emit('process-exit', { code, signal })

        // 如果正在主动关闭，跳过自动重启逻辑
        if (this.isShuttingDown) {
          logInfo('正在主动关闭snapfile服务，跳过自动重启', {
            activeTasks: this.activeTasks.size,
            pendingTasks: this.pendingTasks.length,
          })
          return
        }

        // 只有在非主动关闭时才更新状态（避免与stop()方法中的once监听器冲突）
        this.isRunning = false
        this.process = null

        // 自动重启机制
        if (this.restartAttempts < this.maxRestartAttempts) {
          logInfo('尝试重启snapfile进程', {
            currentAttempt: this.restartAttempts + 1,
            maxAttempts: this.maxRestartAttempts,
            activeTasks: this.activeTasks.size,
            pendingTasks: this.pendingTasks.length,
          })
          // 将所有活跃任务重新加入待处理队列
          this.moveActiveTasksToPending()
          setTimeout(async () => {
            this.restartAttempts += 1
            try {
              await this.start()
              this.resendPendingTasks()
            }
            catch (error) {
              logError('重启snapfile进程失败', {
                attempt: this.restartAttempts,
                maxAttempts: this.maxRestartAttempts,
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined,
              })
            }
          }, 2000) // 2秒后重试
        }
        else {
          logError('达到最大重启次数，放弃重启snapfile进程', {
            maxAttempts: this.maxRestartAttempts,
            activeTasks: this.activeTasks.size,
            pendingTasks: this.pendingTasks.length,
          })
          this.emit('max-restart-reached')
        }
      })
    })
  }

  /**
   * 停止snapfile进程
   */
  async stop(): Promise<void> {
    if (!this.process || !this.isRunning) {
      return
    }

    // 设置关闭标志，禁用自动重启
    this.isShuttingDown = true

    return new Promise((resolve) => {
      // 创建一个一次性的exit监听器用于resolve Promise
      const onExit = () => {
        this.isRunning = false
        this.process = null
        resolve()
      }

      this.process!.once('exit', onExit)

      // 发送终止信号
      this.process!.kill('SIGTERM')
      // SIGKILL
    })
  }

  /**
   * 重启snapfile进程
   */
  async restart(): Promise<void> {
    await this.stop()
    // 重置关闭标志，允许重新启动
    this.isShuttingDown = false
    await this.start()
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.process)
      return

    // 处理stdout输出（snapfile的响应）
    this.process.stdout?.on('data', (data) => {
      const lines = data.toString().trim().split('\n')
      for (const line of lines) {
        const trimmedLine = line.trim()
        if (trimmedLine) {
          // 只解析JSON格式的响应，忽略Go程序的日志输出
          if (trimmedLine.startsWith('{') && trimmedLine.endsWith('}')) {
            try {
              const response: SnapfileResponse = JSON.parse(trimmedLine)
              this.handleSnapfileResponse(response)
            }
            catch (error) {
              logError('解析Snapfile响应失败', {
                rawResponse: trimmedLine,
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined,
              })
            }
          }
          else {
            // 非JSON格式的输出，可能是Go程序的日志，记录为调试信息
            logInfo('Snapfile原始日志', {
              message: trimmedLine,
            })
          }
        }
      }
    })

    // 处理stderr输出
    this.process.stderr?.on('data', (data) => {
      logError('Snapfile stderr输出', {
        message: data.toString().trim(),
        activeTasks: this.activeTasks.size,
      })
    })
  }

  /**
   * 清理任务相关信息
   */
  private cleanupTask(taskID: string): void {
    this.activeTasks.delete(taskID)
    this.taskCallbacks.delete(taskID)
  }

  /**
   * 处理错误响应
   */
  private handleErrorResponse(response: SnapfileResponse, callbacks: any, taskID?: string): void {
    // 特殊处理单个文件下载失败 - 记录到日志但不中断任务
    if (response.code === SnapfileStatusCode.file_download_error) {
      logWarn('单个文件下载失败', {
        taskID: taskID || 'unknown',
        errorCode: response.code,
        errorMessage: response.message,
        errorData: JSON.stringify(response.data),
      })
      // 对于单个文件下载失败，不触发错误回调，让任务继续执行
      return
    }

    // 映射错误状态
    const errorStatus = mapSnapfileErrorToErrorStatus(response.code)

    // 如果没有taskID，尝试从活跃任务中找到可能的任务
    let finalTaskID = taskID
    if (!finalTaskID && this.activeTasks.size > 0) {
      // 如果只有一个活跃任务，假设错误属于该任务
      if (this.activeTasks.size === 1) {
        finalTaskID = Array.from(this.activeTasks.keys())[0]
        logWarn('错误响应缺少taskID，假设错误属于唯一活跃任务', {
          assumedTaskID: finalTaskID,
          errorCode: response.code,
          errorMessage: response.message,
        })
      }
      else {
        logWarn('错误响应缺少taskID，且有多个活跃任务，无法确定错误归属', {
          activeTasksCount: this.activeTasks.size,
          activeTaskIDs: Array.from(this.activeTasks.keys()),
          errorCode: response.code,
          errorMessage: response.message,
        })
      }
    }

    // 构建增强的错误数据
    const enhancedErrorData: SnapfileErrorData = {
      ...response,
      errorStatus,
      taskID: finalTaskID,
    }

    this.emit(SnapfileCallbackEvent.ERROR, enhancedErrorData)

    // 如果找到了对应的任务，调用其错误回调
    if (finalTaskID) {
      const taskCallbacks = this.taskCallbacks.get(finalTaskID)
      taskCallbacks?.onError?.(enhancedErrorData)
      this.cleanupTask(finalTaskID)
    }
    else {
      // 如果没有找到对应任务，调用传入的回调（如果有）
      callbacks?.onError?.(enhancedErrorData)
    }
  }

  /**
   * 处理snapfile响应
   */
  private handleSnapfileResponse(response: SnapfileResponse): void {
    // 排除进度响应，避免日志过多
    if (response.code !== SnapfileStatusCode.task_download_progress && response.code !== SnapfileStatusCode.task_conversion_progress) {
      logInfo('收到Snapfile响应', {
        responseContent: JSON.stringify(response), // 记录完整的响应内容
      })
    }

    // 发出通用响应事件
    this.emit(SnapfileCallbackEvent.RESPONSE, response)

    // 获取任务ID和回调函数
    const taskID = response.data?.taskID
    const callbacks = taskID ? this.taskCallbacks.get(taskID) : null

    // 处理错误码 - 使用isSnapfileErrorCode函数判断是否为错误状态码
    const isErrorCode = isSnapfileErrorCode(response.code)

    if (isErrorCode) {
      this.handleErrorResponse(response, callbacks, taskID)
      return
    }

    // 查找对应的处理器
    const handler = SnapfileStatusHandlers[response.code as keyof typeof SnapfileStatusHandlers]
    if (!handler) {
      logWarn('未知的状态码', {
        code: response.code,
        message: response.message,
        taskID: (response.data as any)?.taskID,
      })
      return
    }

    // 发出事件
    this.emit(handler.event, response.data)

    // 调用相应的回调函数
    switch (handler.callbackType) {
      case SnapfileCallbackType.ON_STATUS_CHANGE:
        // 直接使用response.code作为状态
        callbacks?.onStatusChange?.(response.code as SnapfileTaskStatus, response.data as SnapfileStatusData)
        break
      case SnapfileCallbackType.ON_PROGRESS: {
        // 根据响应码确定进度类型
        const progressData = response.data as SnapfileProgressData
        let progressType: 'download' | 'conversion' = 'download'

        if (response.code === SnapfileStatusCode.task_download_progress) {
          progressType = 'download'
        }
        else if (response.code === SnapfileStatusCode.task_conversion_progress) {
          progressType = 'conversion'
        }

        // 添加进度类型信息
        const enhancedProgressData = {
          ...progressData,
          progressType,
        }

        callbacks?.onProgress?.(enhancedProgressData)
        break
      }
      case SnapfileCallbackType.ON_COMPLETE:
        callbacks?.onComplete?.(response.data as SnapfileCompleteData)
        break
      case SnapfileCallbackType.ON_RESPONSE:
        // 对于特殊响应事件，只发出事件通知，不调用特定回调
        logInfo('收到特殊响应事件', {
          event: handler.event,
          code: response.code,
          taskID: (response.data as any)?.taskID,
        })
        break
    }

    // 清理任务信息（如果需要）
    if ('shouldCleanup' in handler && handler.shouldCleanup && taskID) {
      this.cleanupTask(taskID)
    }
  }

  /**
   * 发送事件到snapfile进程
   */
  private sendEvent(event: SnapfileEvent): boolean {
    if (!this.process || !this.isRunning) {
      logError('Snapfile进程未运行，无法发送事件', {
        eventType: event.type,
        taskID: (event.payload as any)?.taskID,
        isRunning: this.isRunning,
        hasProcess: !!this.process,
      })
      return false
    }

    try {
      const eventJson = `${JSON.stringify(event)}\n`
      logInfo('发送事件到Snapfile', {
        eventType: event.type,
        taskID: (event.payload as any)?.taskID,
        payloadSize: eventJson.length,
        eventContent: eventJson.trim(), // 记录完整的事件内容
      })
      return this.process.stdin?.write(eventJson) || false
    }
    catch (error) {
      logError('发送事件到Snapfile失败', {
        eventType: event.type,
        taskID: (event.payload as any)?.taskID,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      })
      return false
    }
  }

  /**
   * 开始任务（带回调）
   */
  startTask(payload: StartTaskPayload, callbacks?: {
    onProgress?: (progress: SnapfileProgressData) => void
    onStatusChange?: (status: SnapfileTaskStatus, data: SnapfileStatusData) => void
    onComplete?: (data: SnapfileCompleteData) => void
    onError?: (error: SnapfileErrorData) => void
  }): boolean {
    // 保存任务信息和回调
    this.activeTasks.set(payload.taskID, payload)
    if (callbacks) {
      this.taskCallbacks.set(payload.taskID, callbacks)
    }

    // 如果进程未运行，将任务加入待处理队列
    if (!this.isProcessRunning()) {
      logWarn('Snapfile进程未运行，任务已加入待处理队列', {
        taskID: payload.taskID,
        pendingTasksCount: this.pendingTasks.length + 1,
        activeTasks: this.activeTasks.size,
      })
      this.pendingTasks.push(payload)
      return false
    }

    // 添加到待处理队列（用于进程重启时恢复）
    this.pendingTasks.push(payload)

    const success = this.sendEvent({
      type: SnapfileEventType.START_TASK,
      payload,
    })

    // 如果发送成功，从待处理队列中移除
    if (success) {
      const index = this.pendingTasks.findIndex(task => task.taskID === payload.taskID)
      if (index > -1) {
        this.pendingTasks.splice(index, 1)
      }
      logInfo('Snapfile任务启动成功', {
        taskID: payload.taskID,
        filesCount: payload.files?.length || 0,
        activeTasks: this.activeTasks.size,
        pendingTasks: this.pendingTasks.length,
      })
    }
    else {
      logError('Snapfile任务启动失败', {
        taskID: payload.taskID,
        filesCount: payload.files?.length || 0,
        activeTasks: this.activeTasks.size,
        pendingTasks: this.pendingTasks.length,
      })
    }

    return success
  }

  /**
   * 删除任务
   */
  deleteTask(taskIDs: string[]): boolean {
    logInfo('删除Snapfile任务', {
      taskIDs,
      taskCount: taskIDs.length,
      activeTasks: this.activeTasks.size,
    })

    // 清理任务信息和回调
    taskIDs.forEach((taskID) => {
      this.activeTasks.delete(taskID)
      this.taskCallbacks.delete(taskID)
    })

    const success = this.sendEvent({
      type: SnapfileEventType.DELETE_TASK,
      payload: { taskIDs },
    })

    if (success) {
      logInfo('Snapfile任务删除成功', {
        taskIDs,
        remainingActiveTasks: this.activeTasks.size,
      })
    }
    else {
      logError('Snapfile任务删除失败', {
        taskIDs,
        activeTasks: this.activeTasks.size,
      })
    }

    return success
  }

  /**
   * 更新最大下载任务数
   */
  updateDownloadTaskLimit(limit: number): boolean {
    logInfo('更新Snapfile最大下载任务数', {
      newLimit: limit,
      oldLimit: this.maxDownloadingTasks,
      activeTasks: this.activeTasks.size,
    })

    return this.sendEvent({
      type: SnapfileEventType.UPDATE_MAX_DOWNLOAD_TASK,
      payload: { limit },
    })
  }

  /**
   * 停止录制直播
   */
  stopRecordingLive(taskID: string): boolean {
    logInfo('停止Snapfile直播录制', {
      taskID,
      activeTasks: this.activeTasks.size,
    })

    const success = this.sendEvent({
      type: SnapfileEventType.STOP_RECORDING_LIVE,
      payload: { taskID },
    })

    if (success) {
      logInfo('停止直播录制命令发送成功', { taskID })
    }
    else {
      logError('停止直播录制命令发送失败', { taskID })
    }

    return success
  }

  /**
   * 检查进程是否运行
   */
  isProcessRunning(): boolean {
    return this.isRunning && this.process !== null
  }

  /**
   * 获取活跃任务列表
   */
  getActiveTasks(): Map<string, StartTaskPayload> {
    return new Map(this.activeTasks)
  }

  /**
   * 获取任务信息
   */
  getTask(taskID: string): StartTaskPayload | undefined {
    return this.activeTasks.get(taskID)
  }

  /**
   * 取消任务（删除单个任务的便捷方法）
   */
  cancelTask(taskID: string): boolean {
    return this.deleteTask([taskID])
  }

  /**
   * 清理所有任务
   */
  clearAllTasks(): void {
    const taskIDs = Array.from(this.activeTasks.keys())
    if (taskIDs.length > 0) {
      this.deleteTask(taskIDs)
    }
  }

  /**
   * 将活跃任务移动到待处理队列（进程异常退出时调用）
   */
  private moveActiveTasksToPending(): void {
    if (this.activeTasks.size === 0) {
      return
    }

    logInfo('将活跃任务移动到待处理队列', {
      activeTasksCount: this.activeTasks.size,
      currentPendingCount: this.pendingTasks.length,
    })

    // 将所有活跃任务添加到待处理队列
    for (const [taskID, payload] of this.activeTasks) {
      // 检查是否已经在待处理队列中，避免重复
      const existsInPending = this.pendingTasks.some(task => task.taskID === taskID)
      if (!existsInPending) {
        this.pendingTasks.push(payload)
        logInfo('任务已移动到待处理队列', {
          taskID,
          pendingTasksCount: this.pendingTasks.length,
        })
      }
    }
  }

  /**
   * 重新发送待处理任务（进程重启后调用）
   */
  private resendPendingTasks(): void {
    if (this.pendingTasks.length === 0) {
      return
    }

    logInfo('重新发送待处理任务', {
      pendingTasksCount: this.pendingTasks.length,
      activeTasks: this.activeTasks.size,
    })
    const tasksToResend = [...this.pendingTasks]
    this.pendingTasks.length = 0 // 清空队列

    for (const task of tasksToResend) {
      const callbacks = this.taskCallbacks.get(task.taskID)
      const success = this.startTask(task, callbacks)
      logInfo('重新发送任务', {
        taskID: task.taskID,
        success,
      })
    }
  }

  /**
   * 重置重启计数器（成功启动后调用）
   */
  private resetRestartAttempts(): void {
    this.restartAttempts = 0
  }
}

// 创建单例实例

export default new SnapfileService()
