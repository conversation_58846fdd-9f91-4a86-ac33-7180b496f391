import { logError, logInfo } from '@main/lib/logger'
import snapfileService from '@main/service/snapfile'
import { checkFileExists, getBinPath, setFilePermissions } from '@main/utils'

/**
 * 初始化snapfile服务
 */
export async function initSnapfile() {
  try {
    const snapfilePath = getBinPath('snapfile')
    // 在 Mac 平台上设置执行权限
    await setFilePermissions(snapfilePath)
    // 检查文件是否存在
    const exists = await checkFileExists(snapfilePath)
    if (!exists) {
      throw new Error('snapfile 可执行文件不存在，请重新安装应用')
    }

    // 添加错误事件监听器，防止未处理的错误异常
    snapfileService.on('error', (error) => {
      logError('Snapfile服务错误', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      })
    })

    await snapfileService.start()
    logInfo('Snapfile服务启动成功', {
      executablePath: snapfilePath,
    })
  }
  catch (error) {
    logError('Snapfile服务启动失败', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      executablePath: getBinPath('snapfile'),
    })
    throw error // 重新抛出错误，让Promise.all能够捕获
  }
}
