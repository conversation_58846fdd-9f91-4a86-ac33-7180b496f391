import type { ReactElement } from 'react'
import { ApplicationSideItem } from '@renderer/client/application-compose'

import { useMatch, useNavigate, useResolvedPath } from 'react-router-dom'

interface LayoutItemProps {
  children: string
  to: string
  icon: ReactElement
}
function LayoutItem({ children, icon, to }: LayoutItemProps) {
  const navigate = useNavigate()

  const resolved = useResolvedPath(to)
  const match = useMatch({ path: resolved.pathname, end: true })

  return (
    <ApplicationSideItem
      text={children}
      icon={icon}
      active={!!match}
      to={to}
      onClick={() => navigate(to)}
    />
  )
}

export default LayoutItem
