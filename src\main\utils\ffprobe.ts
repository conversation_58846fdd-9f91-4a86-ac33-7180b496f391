import type { FfprobeData } from 'fluent-ffmpeg'
import ffmpeg from 'fluent-ffmpeg'

/**
 * 通过 ffprobe 获取媒体文件信息
 * @param filePath 媒体文件路径
 * @returns 媒体文件信息对象
 */
export async function getFilePathMediaInfo(filePath: string): Promise<FfprobeData> {
  try {
    return await new Promise<FfprobeData>((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, data) => {
        if (err)
          reject(err)
        else
          resolve(data)
      })
    })
  }
  catch {
    // console.error(`获取文件信息失败: ${filePath}`, error)
    // 错误情况下返回空的 FFprobeData 对象
    return {
      streams: [],
      format: {},
      chapters: [],
    }
  }
}
