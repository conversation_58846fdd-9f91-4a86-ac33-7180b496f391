{"compilerOptions": {"composite": true, "target": "ES2020", "jsx": "react-jsx", "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@/*": ["src/renderer/src/*"], "@main/*": ["src/main/*"], "@renderer/*": ["src/renderer/src/*"], "@preload/*": ["src/preload/*"], "@common/*": ["src/common/*"]}, "allowSyntheticDefaultImports": true, "skipLibCheck": true}, "include": ["vite.config.mts", "src/main", "src/common", "src/preload", "src/renderer/src"]}