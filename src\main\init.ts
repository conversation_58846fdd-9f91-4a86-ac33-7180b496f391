import {
  initAptabase,
  initFFmpeg,
  initLogger,
  initSentry,
  initSnapfile,
  initYtDlp,
} from '@main/lib'
import { initDatabase } from '@main/lib/db'
import YtDlpService from '@main/service/yt-dlp'
import { initTipc } from '@main/tipc'
import { app } from 'electron'

/**
 * 初始化所有依赖
 */
export async function initializeLibs() {
  // 首先初始化日志系统
  initLogger()

  // 确保在应用准备好之前初始化其他依赖
  if (!app.isReady()) {
    await Promise.all([
      initSentry(),
      initAptabase(),
      initYtDlp(),
      initFFmpeg(),
      initDatabase(),
      initTipc(),
      YtDlpService.checkYtDlpUpdate(),
      initSnapfile(),
    ])
  }
}
