import type { DOWNLOAD_STATUS_ENUM } from '@common/constants/download'

export interface FileInfo {
  title: string
  resolutionWidth: number
  resolutionHeight: number
  filesize: number
  format: string
  fps: string
  duration: number
  audioBitrate: string
}

export type DownloadStatus =
  | 'pending'
  | 'downloading_audio'
  | 'downloading_video'
  | 'merging'
  | 'completed'
  | 'download_error'
  | 'parse_error'
  | 'cancelled'
  | 'parse_sign_error'
  | 'download_sign_error'

export interface StoredDownloadTask {
  title?: string
  id: string
  url: string
  tempFilePath: string
  finalFilename: string
  finalFilePath: string
  format: string
  quality: string
  status: DOWNLOAD_STATUS_ENUM
  progress: number
  downloadSize?: number
  downloadedSize?: number
  speed?: number
  eta?: string
  error?: string
  command?: string[]
  videoInfo: VideoInfo
  createdAt: number
  updatedAt: number
  thumbnail: string
  thumbnailCache?: string
  thumbnailHeaders?: HeadersInit
}

export interface VideoInfo {
  id: string
  webpage_url?: string
  title: string
  thumbnail: string
  thumbnailHeaders?: HeadersInit
  duration: number
  description: string
  uploader: string
  formats: VideoFormat[]
  isPlaylist: boolean
  audioLanguages: string[]
}

export interface VideoFormat {
  format_id: string
  ext: string
  resolutionWidth: number
  resolutionHeight: number
  filesize: number
  duration: number
  vcodec: string
  acodec: string
  fps: number
  audioBitrate: string
}

export interface StartDownloadDTO {
  url: string
  finalFilename: string
  title: string
  format: string
  quality: string
  taskId: string
  thumbnail?: string
  thumbnailHeaders?: HeadersInit
  audioLanguages?: string[]
}

export interface FFmpegProgress {
  percent?: number // 进度
  frames?: number
  currentFps?: number
  currentKbps?: number
  targetSize?: number
  timemark?: string
  [key: string]: unknown
}

export interface DownloadProgress {
  totalPercent: number
  downloadedBytes: number
  totalBytes: number
  speed: number
  normalDownloaded?: number
  m3u8Downloaded?: number
}
