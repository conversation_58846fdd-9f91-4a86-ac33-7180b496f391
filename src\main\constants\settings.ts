import type { AuthSite } from '@common/types/setting'

// 预定义的授权网站
export const DEFAULT_AUTH_SITES: AuthSite[] = [
  {
    name: 'YouTube',
    url: 'https://www.youtube.com',
    authUrl: 'https://www.youtube.com/signin',
    isAuthorized: false,
    enableDelete: false,
  },
  {
    name: 'Instagram',
    url: 'https://www.instagram.com',
    authUrl: 'https://www.instagram.com',
    isAuthorized: false,
    enableDelete: false,
  },
  {
    name: 'Twitter',
    url: 'https://x.com',
    authUrl: 'https://x.com',
    isAuthorized: false,
    enableDelete: false,
  },
]
