import type { SnifferResource } from '@/store/network'
import EmptySvgIcon from '@/assets/empty.svg?react'
import { client } from '@/client'
import { Notice } from '@/components/Notice'
import { useNetworkStore } from '@/store/network'
import { useTaskStore } from '@/store/task'
import { addOrRemoveUniqueItemOfArray } from '@/utils/array'
import { getResourceSize, isAudio } from '@/utils/network'
import { Button, Checkbox, Label, Radio, Tooltip } from 'flowbite-react'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { GrClearOption } from 'react-icons/gr'
import { TbArrowBarDown, TbArrowBarToUp, TbCheck, TbDownload, TbExternalLink, TbLink, TbQuestionMark, TbX } from 'react-icons/tb'
import { Virtuoso } from 'react-virtuoso'

function SniffingBtn({ data, handleClick }: { data: any, handleClick: () => void }) {
  return (
    <div className="absolute bottom-4 right-4 p-4.5 bg-blue-700 rounded-full flex items-center justify-center cursor-pointer" onClick={handleClick}>
      {data.length > 0
        ? (
            <>
              <TbDownload className="text-white text-2xl" />
              <span className="absolute top-3 right-3 text-white text-xs font-medium">{data.length}</span>
            </>
          )
        : (
            <TbQuestionMark className="text-white text-2xl" />
          )}
    </div>
  )
}

const SniffingItem = React.memo(({ item, selectedList, handleSelect, handleDownload, handleShare, handleCopyLink }: { item: SnifferResource, selectedList: string[], handleSelect: (id: string) => void, handleDownload: (resource: SnifferResource) => void, handleShare: (resource: SnifferResource) => void, handleCopyLink: (url: string) => void }) => {
  const { t } = useTranslation()

  return (
    <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 last:border-0">
      <div className="flex items-center gap-2">
        <Checkbox
          id={`multipleResource/${item.id}`}
          disabled={item.isDownloaded}
          checked={selectedList.includes(item.id) || item.isDownloaded}
          onChange={() => handleSelect(item.id)}
        />
        <Tooltip content={item.title}>
          <Label htmlFor={`multipleResource/${item.id}`} className="text-xs text-gray-700">
            {item.fileExt ? item.fileExt.toLocaleUpperCase() : ''}
            {' * '}
            {getResourceSize(item) !== 'Unknown' ? getResourceSize(item) : !isAudio(item) ? t('network.live') : ''}
          </Label>
        </Tooltip>
      </div>
      <div className="flex items-center gap-3">
        <Tooltip content={item.isDownloaded ? t('network.downloadedTip') : t('network.downloadTip')}>
          {item.isDownloaded
            ? <TbCheck className="text-gray-500 text-xl" />
            : <TbDownload className="text-blue-700 text-xl cursor-pointer" onClick={() => handleDownload(item)} />}
        </Tooltip>
        <Tooltip content={t('network.previewTip')}>
          <TbExternalLink className="text-blue-700 text-xl cursor-pointer" onClick={() => handleShare(item)} />
        </Tooltip>
        <Tooltip content={t('network.copyLinkTip')}>
          <TbLink className="text-blue-700 text-xl cursor-pointer" onClick={() => handleCopyLink(item.url)} />
        </Tooltip>
      </div>
    </div>
  )
})

function SniffingFooter({ filterData, selectedList, handleSelectAll, handleClearSniffedResource, handleDownloadAll }: { filterData: SnifferResource[], selectedList: string[], handleSelectAll: () => void, handleClearSniffedResource: () => void, handleDownloadAll: () => void }) {
  const { t } = useTranslation()

  return (
    <div className="py-2 px-4 flex items-center justify-between bg-white">
      <div className="flex items-center gap-2">
        <Checkbox
          id="allResource"
          disabled={filterData.filter(item => item.isDownloaded).length === filterData.length || filterData.length === 0}
          checked={(selectedList.length + filterData.filter(item => item.isDownloaded).length) === filterData.length}
          onChange={handleSelectAll}
        />
        <Label htmlFor="allResource" className="text-xs text-gray-900">{(selectedList.length + filterData.filter(item => item.isDownloaded).length) === filterData.length ? t('common.cancelAll') : t('common.selectAll')}</Label>
      </div>
      <div className="flex items-center gap-4">
        <GrClearOption className="text-blue-700 text-xl cursor-pointer" onClick={handleClearSniffedResource} />
        <Button size="xs" className="p-1" disabled={selectedList.length === 0} onClick={handleDownloadAll}>
          {t('network.download')}
          {selectedList.length > 0 && ` (${selectedList.length})`}
        </Button>
      </div>
    </div>
  )
}

function SniffingModal({
  data,
  type,
  filterData,
  selectedList,
  handleTop,
  handleClose,
  handleTypeChange,
  handleSelect,
  handleDownload,
  handleShare,
  handleCopyLink,
  handleSelectAll,
  handleClearSniffedResource,
  handleDownloadAll,
}: {
  data: SnifferResource[]
  type: 'all' | 'video' | 'audio'
  filterData: SnifferResource[]
  selectedList: string[]
  handleTop: () => void
  handleClose: () => void
  handleTypeChange: (type: 'all' | 'video' | 'audio') => void
  handleSelect: (id: string) => void
  handleDownload: (resource: SnifferResource) => void
  handleShare: (resource: SnifferResource) => void
  handleCopyLink: (url: string) => void
  handleSelectAll: () => void
  handleClearSniffedResource: () => void
  handleDownloadAll: () => void
}) {
  const { t } = useTranslation()
  const videoCount = useMemo(() => data.filter(item => !isAudio(item)).length, [data])
  const audioCount = useMemo(() => data.filter(isAudio).length, [data])

  return (
    <div className="min-w-75 h-1/2 flex flex-col absolute bottom-0 right-0 rounded-t-lg border border-gray-200 bg-gray-100 shadow">
      <div className="flex flex-col gap-2 pt-3 pb-2 px-4 bg-white rounded-t-lg">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-900">{data.length === 0 ? t('network.noFilesFound') : t('network.resourceTitle', { number: data.length })}</span>
          <div className="flex items-center gap-4 text-xl">
            <TbArrowBarToUp className="cursor-pointer" onClick={handleTop} />
            <TbX className="cursor-pointer" onClick={handleClose} />
          </div>
        </div>
        {data.length > 0 && (
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1">
              <Radio id="all" name="sniffing" checked={type === 'all'} onChange={() => handleTypeChange('all')} />
              <Label htmlFor="all" className="text-xs text-gray-900">{t('network.all', { number: data.length })}</Label>
            </div>
            <div className="flex items-center gap-1">
              <Radio id="video" name="sniffing" disabled={videoCount === 0} checked={type === 'video'} onChange={() => handleTypeChange('video')} />
              <Label htmlFor="video" className="text-xs text-gray-900">{t('network.video', { number: videoCount })}</Label>
            </div>
            <div className="flex items-center gap-1">
              <Radio id="audio" name="sniffing" disabled={audioCount === 0} checked={type === 'audio'} onChange={() => handleTypeChange('audio')} />
              <Label htmlFor="audio" className="text-xs text-gray-900">{t('network.audio', { number: audioCount })}</Label>
            </div>
          </div>
        )}
      </div>
      <div className="h-full overflow-hidden">
        {data.length === 0
          ? (
              <div className="h-full px-4 flex flex-col items-center justify-center gap-2">
                <EmptySvgIcon />
                <p className="text-xs text-gray-500">{t('network.emptyTip1')}</p>
                <p className="text-xs text-gray-500">{t('network.emptyTip2')}</p>
              </div>
            )
          : (
              <div className="h-full flex flex-col">
                <div className="grow overflow-y-auto">
                  <Virtuoso
                    totalCount={filterData.length}
                    data={filterData}
                    itemContent={(_, item) => (
                      <SniffingItem
                        key={`${item.id}-${selectedList.includes(item.id)}`}
                        item={item}
                        selectedList={selectedList}
                        handleSelect={handleSelect}
                        handleDownload={handleDownload}
                        handleShare={handleShare}
                        handleCopyLink={handleCopyLink}
                      />
                    )}
                  />
                </div>
                <SniffingFooter
                  filterData={filterData}
                  selectedList={selectedList}
                  handleSelectAll={handleSelectAll}
                  handleClearSniffedResource={handleClearSniffedResource}
                  handleDownloadAll={handleDownloadAll}
                />
              </div>
            )}
      </div>
    </div>
  )
}

function SniffingDrawer({
  data,
  type,
  filterData,
  selectedList,
  handleDown,
  handleClose,
  handleTypeChange,
  handleSelect,
  handleDownload,
  handleShare,
  handleCopyLink,
  handleSelectAll,
  handleClearSniffedResource,
  handleDownloadAll,
}:
{
  data: SnifferResource[]
  type: 'all' | 'video' | 'audio'
  filterData: SnifferResource[]
  selectedList: string[]
  handleDown: () => void
  handleClose: () => void
  handleTypeChange: (type: 'all' | 'video' | 'audio') => void
  handleSelect: (id: string) => void
  handleDownload: (resource: SnifferResource) => void
  handleShare: (resource: SnifferResource) => void
  handleCopyLink: (url: string) => void
  handleSelectAll: () => void
  handleClearSniffedResource: () => void
  handleDownloadAll: () => void
}) {
  const { t } = useTranslation()
  const videoCount = useMemo(() => data.filter(item => !isAudio(item)).length, [data])
  const audioCount = useMemo(() => data.filter(isAudio).length, [data])

  return (
    <div className="min-w-75 h-full flex flex-col bg-gray-100 shadow">
      <div className="flex flex-col gap-2 pt-3 pb-2 px-4 bg-white">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-900">{data.length === 0 ? t('network.noFilesFound') : t('network.resourceTitle', { number: data.length })}</span>
          <div className="flex items-center gap-4 text-xl">
            <TbArrowBarDown className="cursor-pointer" onClick={handleDown} />
            <TbX className="cursor-pointer" onClick={handleClose} />
          </div>
        </div>
        {data.length > 0
          && (
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1">
                <Radio id="all" name="sniffing" checked={type === 'all'} onChange={() => handleTypeChange('all')} />
                <Label htmlFor="all" className="text-xs text-gray-900">{t('network.all', { number: data.length })}</Label>
              </div>
              <div className="flex items-center gap-1">
                <Radio id="video" name="sniffing" disabled={videoCount === 0} checked={type === 'video'} onChange={() => handleTypeChange('video')} />
                <Label htmlFor="video" className="text-xs text-gray-900">{t('network.video', { number: videoCount })}</Label>
              </div>
              <div className="flex items-center gap-1">
                <Radio id="audio" name="sniffing" disabled={audioCount === 0} checked={type === 'audio'} onChange={() => handleTypeChange('audio')} />
                <Label htmlFor="audio" className="text-xs text-gray-900">{t('network.audio', { number: audioCount })}</Label>
              </div>
            </div>
          )}
      </div>
      <div className="grow relative">
        {data.length === 0
          ? (
              <div className="h-full px-4 flex flex-col items-center justify-center gap-2">
                <EmptySvgIcon />
                <p className="text-xs text-gray-500">{t('network.emptyTip1')}</p>
                <p className="text-xs text-gray-500">{t('network.emptyTip2')}</p>
              </div>
            )
          : (
              <div className="absolute inset-0 flex flex-col">
                <div className="grow overflow-y-auto">
                  <Virtuoso
                    totalCount={filterData.length}
                    data={filterData}
                    itemContent={(_, item) => (
                      <SniffingItem
                        key={`${item.id}-${selectedList.includes(item.id)}`}
                        item={item}
                        selectedList={selectedList}
                        handleSelect={handleSelect}
                        handleDownload={handleDownload}
                        handleShare={handleShare}
                        handleCopyLink={handleCopyLink}
                      />
                    )}
                  />
                </div>
                <SniffingFooter
                  filterData={filterData}
                  selectedList={selectedList}
                  handleSelectAll={handleSelectAll}
                  handleClearSniffedResource={handleClearSniffedResource}
                  handleDownloadAll={handleDownloadAll}
                />
              </div>
            )}
      </div>
    </div>
  )
}

function Sniffing() {
  const { t } = useTranslation()
  const [type, setType] = useState<'all' | 'video' | 'audio'>('all')
  const [selectedList, setSelectedList] = useState<string[]>([])

  const { isSniffingBtn, isSniffingDrawer, resourceSnifferList, setIsSniffingBtn, setIsSniffingDrawer, downloadResource, clearSniffedResource } = useNetworkStore()
  const { fetchTask } = useTaskStore()

  const handleBtnClick = () => {
    setIsSniffingBtn(false)
  }

  const handleTopModal = () => {
    setIsSniffingDrawer(true)
  }

  const handleClose = () => {
    setIsSniffingBtn(true)
  }

  const handleDownDrawer = () => {
    setIsSniffingDrawer(false)
  }

  const handleTypeChange = (type: 'all' | 'video' | 'audio') => {
    setType(type)
  }

  /** 根据类型过滤数据 */
  const filterData = useMemo(() => {
    if (type === 'all') {
      return resourceSnifferList
    }

    if (type === 'video') {
      return resourceSnifferList.filter(item => !isAudio(item))
    }
    if (type === 'audio') {
      return resourceSnifferList.filter(isAudio)
    }
  }, [resourceSnifferList, type])

  useEffect(() => {
    if (resourceSnifferList.length === 0) {
      setType('all')
    }
  }, [resourceSnifferList])

  useEffect(() => {
    setSelectedList([])
  }, [type])

  const handleSelect = useCallback((id: string) => {
    const newSelectedUrls = addOrRemoveUniqueItemOfArray(id, selectedList)
    setSelectedList(newSelectedUrls)
  }, [selectedList])

  const handleCopyLink = (url: string) => {
    navigator.clipboard.writeText(url).then(() => {
      Notice.success(t('network.copyLinkSuccess'))
    })
  }

  const handleDownload = useCallback(async (resource: SnifferResource) => {
    await downloadResource(resource)
    fetchTask()
  }, [])

  const handleShare = (resource: SnifferResource) => {
    client.openExternalLink({ url: resource.url })
  }

  const handleSelectAll = () => {
    const allSelectedIds = filterData.filter(item => !item.isDownloaded).map(item => item.id)
    if (selectedList.length === allSelectedIds.length) {
      setSelectedList([])
    }
    else {
      setSelectedList(allSelectedIds)
    }
  }

  const handleClearSniffedResource = () => {
    clearSniffedResource()
    setSelectedList([])
  }

  const handleDownloadAll = () => {
    selectedList.forEach((id) => {
      const resource = resourceSnifferList.find(item => item.id === id)
      if (resource) {
        handleDownload(resource)
      }
    })
    setSelectedList([])
  }

  const modalProps = {
    data: resourceSnifferList,
    type,
    filterData,
    selectedList,
    handleClose,
    handleTypeChange,
    handleSelect,
    handleDownload,
    handleShare,
    handleCopyLink,
    handleSelectAll,
    handleClearSniffedResource,
    handleDownloadAll,
  }

  return (
    <>
      {isSniffingBtn && <SniffingBtn data={resourceSnifferList} handleClick={handleBtnClick} />}
      {!isSniffingBtn && (isSniffingDrawer
        ? (
            <SniffingDrawer
              handleDown={handleDownDrawer}
              {...modalProps}
            />
          )
        : (
            <SniffingModal
              handleTop={handleTopModal}
              {...modalProps}
            />
          ))}
    </>
  )
}

export default Sniffing
