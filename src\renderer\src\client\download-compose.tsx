import type { ReactNode } from 'react'
import { Dropdown } from 'flowbite-react'
import { HiCheck } from 'react-icons/hi'
import { TbChevronRight } from 'react-icons/tb'

export function DownloadConfig(props: {
  showCheck: boolean
  label: ReactNode
  onClick?: () => void
}) {
  return (
    <Dropdown.Item
      className="flex justify-between gap-4 text-nowrap"
      onClick={props.onClick}
    >
      <span>{props.label}</span>
      <HiCheck className={props.showCheck ? 'visible' : 'invisible'} />
    </Dropdown.Item>
  )
}

export function DownloadSubDropdown(props: {
  label?: string
  children: ReactNode
}) {
  const { label, children } = props
  return (
    <Dropdown.Item as="div" className="group">
      <div className="flex items-center justify-between w-full">
        <span>{label}</span>
        <TbChevronRight />
      </div>
      <div className="absolute top-0 left-[calc(100%+6px)] hidden group-hover:block bg-white border border-gray-200 rounded-lg shadow-lg min-w-[200px] z-50">
        <div className="absolute -left-4 top-0 w-4 h-full bg-transparent"></div>
        <div className="max-h-[90vh] overflow-auto">
          {children}
        </div>
      </div>
    </Dropdown.Item>
  )
}

export function DownloadConfigShowCurrent(props: {
  label: ReactNode
  content: ReactNode
}) {
  return (
    <div className="flex gap-1 cursor-pointer text-sm">
      <span className="text-gray-500">{props.label}</span>
      <span>{props.content}</span>
    </div>
  )
}
