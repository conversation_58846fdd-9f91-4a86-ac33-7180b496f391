@import 'tailwindcss';
@config "../../../tailwind.config.js";

body {
  overflow: hidden;
}

/* 默认滚动条样式 */
html.windows {
  ::-webkit-scrollbar {
    width: 8px !important;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
  }

  ::-webkit-scrollbar-thumb {
    background: #888 !important;
    border-radius: 10px !important;
  }
}

/* 隐藏滚动条 */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* 为了兼容 Firefox */
.hide-scrollbar {
  scrollbar-width: none;
}

/* 为了兼容 IE 和 Edge */
.hide-scrollbar {
  -ms-overflow-style: none;
}