# 视频下载流程说明文档

## 流程概述

本文档描述了视频下载系统的完整工作流程，从用户在渲染进程发起下载请求，到主进程完成下载、合并和文件处理的全过程。整个流程包含初始化、解析、下载、合并、移动和完成六个主要阶段，并在各个阶段设有相应的状态更新和错误处理机制。

## 流程图一：下载任务执行步骤

以下流程图展示了从开始到结束的完整下载流程：

![下载流程图](./download-flow.md)

## 流程图二：组件交互时序图

以下时序图展示了渲染进程、主进程、本地存储和文件系统等组件之间的交互：

![组件交互时序图](./download-flow-interaction.md)

## 流程图三：状态转换图

以下状态图展示了下载任务从一个状态到另一个状态的转换过程：

![状态转换图](./download-state-transition.md)

## 流程图四：组件关系图

以下类图展示了系统中各个主要组件之间的关系：

![组件关系图](./download-component-relation.md)

## 详细执行步骤说明

1. **初始化阶段**
   - 渲染进程生成唯一的taskId
   - 主进程将任务的taskId、URL写入本地存储
   - 任务状态设置为"解析中"
   - 初始化完成后，渲染进程更新界面列表

2. **解析阶段**
   - 主进程读取当前设置并在整个下载过程中保持一致
   - 创建临时目录存储下载过程中的所有临时文件
   - 调用yt-dlp解析URL并获取JSON信息
   - 更新本地存储的封面图、标题等关键信息
   - 通知渲染进程状态改为"下载中"

3. **下载阶段**
   - 解析JSON获取需要下载的URL列表
   - 计算所有下载文件的总大小
   - 开始执行下载并向渲染进程实时发送进度
   - 下载完成后发送100%进度信息

4. **合并阶段**
   - 组装下载完成的文件列表
   - 状态更新为"合并中"
   - 根据文件列表进行合并处理
   - 设置标题、语言等元数据信息
   - 向渲染进程发送合并进度

5. **移动阶段**
   - 准备将处理后的文件移动到目标目录
   - 处理文件名重复的情况（添加编号）
   - 执行文件移动操作

6. **完成阶段**
   - 向渲染进程发送下载完成信息
   - 状态更新为"已完成"
   - 将最终文件路径保存在本地下载列表中
   - 清理临时目录

## 错误处理机制

在整个下载流程的每个阶段都设有错误处理机制：
- 解析错误：如URL不存在、需要登录等
- 下载错误：如网络问题、资源不可访问等
- 合并错误：如文件损坏、格式不兼容等
- 文件系统错误：如权限问题、磁盘空间不足等

每当出现错误时，系统会：
1. 捕获并记录错误信息
2. 向渲染进程发送错误状态和消息
3. 更新任务状态为"失败"
4. 进行必要的资源清理
