import { client } from "@/client"
import { useUpdateStore } from "@/store/update"
import { <PERSON>ton, Modal } from "flowbite-react"
import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { IoCloseOutline } from "react-icons/io5"
import { TbCircleCheckFilled, TbExclamationCircleFilled } from "react-icons/tb"

const DownloadAppTipModal = () => {
  const { t } = useTranslation()
  const [showModal, setShowModal] = useState(false)
  const { isFinishDownload, isNextRemind, isDownloadError, isOverMin, setIsDownloadError, installApp, setIsLaterInstall } = useUpdateStore()

  useEffect(() => {
    if ((isNextRemind && isFinishDownload) || isDownloadError) {
      setShowModal(true)
    }
  }, [isNextRemind, isFinishDownload, isDownloadError])

  const handleCancel = () => {
    setIsDownloadError(false)
    setShowModal(false)
  }

  const handleClose = () => {
    if (!isDownloadError) {
      setIsLaterInstall(true)
    }
    handleCancel()
  }

  const handleGoWebsite = () => {
    client.openExternalLink({ url: 'https://snapany.com/app' })
  }

  // 稍后安装
  const laterInstall = () => {
    setIsLaterInstall(true)
    handleCancel()
  }

  return (
    <Modal show={showModal} size="lg">
      <Modal.Body className="relative p-5">
        {isOverMin && <IoCloseOutline className="absolute top-3.5 right-3.5 w-5 h-5 cursor-pointer" onClick={handleClose} />}
        <div className="flex flex-col items-center">
          {isDownloadError ? <TbExclamationCircleFilled className="w-5 h-5 mb-3.5 text-gray-400" /> : <TbCircleCheckFilled className="w-5 h-5 mb-3.5 text-blue-700" />}
          {isDownloadError ? <p className="text-gray-500 mb-4">{t('update.downloadErrorTip')}</p> : <p className="text-gray-500 mb-4">{t('update.installTip')}</p>}
          <div className="flex items-center gap-4">
            {isDownloadError
              ? (
                <>
                  {isOverMin && <Button color="light" onClick={handleCancel}>{t('update.nextUpdate')}</Button>}
                  <Button onClick={handleGoWebsite}>{t('update.goWebsite')}</Button>
                </>
              )
              : (
                <>
                  <Button color="light" onClick={laterInstall}>{t('update.laterBtn')}</Button>
                  <Button onClick={installApp}>{t('update.installNow')}</Button>
                </>
              )}
          </div>
        </div>
      </Modal.Body>
    </Modal>
  )
}

export default DownloadAppTipModal