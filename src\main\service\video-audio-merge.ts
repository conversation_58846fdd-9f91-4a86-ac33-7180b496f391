import type { RendererHandlers } from '@main/renderer-handlers'
import type { FileMediaInfoItem, MediaStreamSelection, MediaStreamSelectionInput, MergeResult } from '@main/types/ffmpeg'
import path from 'node:path'
import { getRendererHandlers } from '@egoist/tipc/main'
import { getFilePathMediaInfo, isImageFile } from '@main/utils'
import { getMainWindow } from '@main/window'
import ffmpeg from 'fluent-ffmpeg'
import fs from 'fs-extra'
// 跟踪当前的ffmpeg命令实例
let currentFFmpegCommand: ffmpeg.FfmpegCommand | null = null

class VideoAudioMergeService {
  constructor() { }

  /**
   * 获取媒体文件信息
   * @param filePaths 媒体文件路径列表
   * @returns 媒体文件信息对象，键为文件路径，值为对应的媒体信息
   */
  async getFilesMediaInfo(filePaths: string[]): Promise<FileMediaInfoItem[]> {
    try {
      const mediaInfoList: FileMediaInfoItem[] = []
      for (const filePath of filePaths) {
        const isImage = await isImageFile(filePath)
        if (isImage) {
          continue
        }
        const mediaInfo = await getFilePathMediaInfo(filePath)
        mediaInfoList.push({
          name: path.basename(filePath, path.extname(filePath)),
          filePath,
          children: mediaInfo.streams.map((stream) => {
            if (stream.codec_type === 'video') {
              // 如果stream.disposition.attached_pic != 1 则将其作为视频流返回
              if (stream.disposition.attached_pic !== 1) {
                return {
                  id: stream.index.toString(),
                  codec_type: stream.codec_type || '',
                  duration: Number(stream.duration) || Number(mediaInfo.format.duration) || '',
                  codec_name: stream.codec_name || '',
                }
              }
              // 视频流不满足条件时返回undefined
              return undefined
            }
            else {
              return {
                id: stream.index.toString(),
                codec_type: stream.codec_type || '',
                duration: Number(stream.duration) || Number(mediaInfo.format.duration) || '',
                codec_name: stream.codec_name || '',
              }
            }
          }).filter(item => item !== undefined), // 过滤掉undefined的项
        })
      }
      return mediaInfoList
    }
    catch (error) {
      console.error('获取媒体信息失败:', error)
      return []
    }
  }

  /**
   * 合并视频和音频流
   * @param streamSelection 选择的媒体流信息
   * @param outputDir 输出目录
   * @param outputFormat 输出格式
   * @returns 合并结果
   */
  async mergeMediaStreams(
    streamSelection: MediaStreamSelection,
    outputDir: string,
    outputFormat: string,
  ): Promise<MergeResult> {
    try {
      // 如果存在正在进行的任务，先停止它
      if (currentFFmpegCommand) {
        this.stopCurrentMergeTask()
      }

      // 获取所有输入文件
      const inputFiles = streamSelection.input.map(item => item.filePath)
      if (inputFiles.length === 0) {
        return {
          success: false,
          error: '没有选择任何文件',
        }
      }

      // 准备输出路径
      const outputPath = await this.prepareOutputPath(outputDir, streamSelection.name, `.${outputFormat}`)

      const outputTempPath = await this.prepareOutputPath(`${outputDir}/.snapany`, `${streamSelection.name}_merge`, `.${outputFormat}`)

      // 创建FFmpeg命令
      const command = ffmpeg()
      currentFFmpegCommand = command

      // 添加所有输入文件并创建映射
      const mappings = this.createInputMappings(command, streamSelection.input)

      // 设置输出选项
      const outputOptions = await this.configureOutputOptions(mappings, `.${outputFormat}`, streamSelection.input, inputFiles)

      // 应用输出选项
      command.outputOptions(outputOptions)

      // 设置输出文件
      command.output(outputTempPath)

      // 执行合并操作
      return await this.executeFFmpegCommand(command, outputPath, outputTempPath)
    }
    catch (error) {
      console.error('合并媒体流错误:', error)
      // 确保在错误情况下也清除当前命令引用
      currentFFmpegCommand = null
      return {
        success: false,
        error: `合并过程中发生错误: ${error.message}`,
      }
    }
  }

  /**
   * 停止当前正在进行的合并任务
   * @returns 停止结果，包含成功状态和可能的错误信息
   */
  stopCurrentMergeTask(): { success: boolean, error?: string } {
    try {
      if (!currentFFmpegCommand) {
        return {
          success: false,
          error: '没有正在进行的合并任务',
        }
      }

      // 终止当前的ffmpeg命令
      currentFFmpegCommand.kill('SIGKILL')
      console.log('已停止合并任务')

      // 重置当前命令
      currentFFmpegCommand = null

      return {
        success: true,
      }
    }
    catch (error) {
      console.error('停止合并任务时出错:', error)
      return {
        success: false,
        error: `停止任务失败: ${error.message}`,
      }
    }
  }

  /**
   * 准备输出文件路径
   * @private
   * @param outputDir 输出目录
   * @param fileName 文件名
   * @param outputExt 输出文件扩展名
   * @returns 准备好的输出路径
   */
  private async prepareOutputPath(outputDir: string, fileName: string, outputExt: string): Promise<string> {
    // 确保输出目录存在
    await fs.ensureDir(outputDir)

    // 确保文件名包含扩展名
    let outputPath = path.join(outputDir, `${fileName}${outputExt}`)

    // 确保文件名唯一
    let counter = 1
    const ext = outputExt
    const nameWithoutExt = path.join(outputDir, fileName)

    while (await fs.pathExists(outputPath)) {
      outputPath = `${nameWithoutExt}_${counter}${ext}`
      counter++
    }

    return outputPath
  }

  /**
   * 创建输入映射
   * @private
   * @param command FFmpeg命令实例
   * @param streamSelection 媒体流选择
   * @returns 创建的映射数组
   */
  private createInputMappings(command: ffmpeg.FfmpegCommand, streamSelection: MediaStreamSelectionInput[]): string[] {
    const mappings: string[] = []

    for (const item of streamSelection) {
      // 添加输入文件
      command.input(item.filePath)
      const inputIndex = streamSelection.indexOf(item)

      // 为每个选中的流创建映射
      for (const streamIndex of item.ids) {
        mappings.push(`${inputIndex}:${streamIndex}`)
      }
    }

    return mappings
  }

  /**
   * 配置输出选项
   * @private
   * @param mappings 流映射
   * @param outputExt 输出文件扩展名
   * @param streamSelection 媒体流选择
   * @param inputFiles 输入文件数组
   * @returns 配置好的输出选项数组
   */
  private async configureOutputOptions(
    mappings: string[],
    outputExt: string,
    streamSelection: MediaStreamSelectionInput[],
    inputFiles: string[],
  ): Promise<string[]> {
    const outputOptions: string[] = []

    // 添加映射
    mappings.forEach((mapping) => {
      outputOptions.push('-map', mapping)
    })

    // 查找视频流并获取其时长
    const videoStreamInfo = await this.findVideoStreamInfo(inputFiles, streamSelection)

    // 如果找到视频流，添加时长限制参数
    if (videoStreamInfo && !Number.isNaN(videoStreamInfo.duration)) {
      console.log(`使用视频流时长作为输出文件时长: ${videoStreamInfo.duration}秒`)
      // 添加时长参数，确保输出文件以视频流的时长为主
      outputOptions.push('-t', videoStreamInfo.duration.toString())
    }

    if (outputExt === '.mkv') {
      outputOptions.push('-c:v', 'copy')
      outputOptions.push('-c:a', 'copy')
      // 字幕设置为srt
      outputOptions.push('-c:s', 'srt')
    }
    else {
      outputOptions.push('-c:s', 'mov_text')

      // 检查是否需要转码
      const { needVideoTranscode, needAudioTranscode } = await this.checkTranscodeNeeds(inputFiles, streamSelection)

      // 根据检查结果设置编码选项
      if (needVideoTranscode) {
        outputOptions.push('-c:v', 'libx264')
      }
      else {
        outputOptions.push('-c:v', 'copy')
      }

      if (needAudioTranscode) {
        outputOptions.push('-c:a', 'aac')
      }
      else {
        outputOptions.push('-c:a', 'copy')
      }
    }

    return outputOptions
  }

  /**
   * 查找视频流并获取其时长信息
   * @private
   * @param inputFiles 输入文件数组
   * @param streamSelection 媒体流选择
   * @returns 视频流信息，包含时长
   */
  private async findVideoStreamInfo(
    inputFiles: string[],
    streamSelection: MediaStreamSelectionInput[],
  ): Promise<{ duration: number } | null> {
    // 获取所有文件的所有流信息
    const streamPromises = inputFiles.map(filePath => getFilePathMediaInfo(filePath))
    const allFileStreams = await Promise.all(streamPromises)

    // 查找所有选中的视频流
    for (let i = 0; i < inputFiles.length; i++) {
      const filePath = inputFiles[i]
      const fileStreams = allFileStreams[i].streams || []
      const selectedStreamIndices = streamSelection.find(item => item.filePath === filePath)?.ids || []

      // 遍历该文件中被选择的流
      for (const streamIndex of selectedStreamIndices) {
        // 将字符串索引转换为数字，并确保流索引有效
        const streamIdNumber = Number.parseInt(streamIndex, 10)
        if (!Number.isNaN(streamIdNumber) && streamIdNumber >= 0 && streamIdNumber < fileStreams.length) {
          const stream = fileStreams[streamIdNumber]

          // 如果是视频流且有时长信息，返回该流的时长
          if (stream.codec_type === 'video' && stream.duration) {
            return { duration: Number(stream.duration) }
          }
        }
      }
    }

    // 如果没有找到视频流，返回null
    return null
  }

  /**
   * 检查是否需要进行视频或音频转码
   * @private
   * @param inputFiles 输入文件数组
   * @param streamSelection 媒体流选择
   * @returns 检查结果，包含是否需要视频转码和音频转码
   */
  private async checkTranscodeNeeds(
    inputFiles: string[],
    streamSelection: MediaStreamSelectionInput[],
  ): Promise<{ needVideoTranscode: boolean, needAudioTranscode: boolean }> {
    let needVideoTranscode = false
    let needAudioTranscode = false

    // 获取所有文件的所有流信息
    const streamPromises = inputFiles.map(filePath => getFilePathMediaInfo(filePath))
    const allFileStreams = await Promise.all(streamPromises)

    // 只检查用户选择的流是否需要转码
    for (let i = 0; i < inputFiles.length; i++) {
      const filePath = inputFiles[i]
      const fileStreams = allFileStreams[i].streams || []
      const selectedStreamIndices = streamSelection.find(item => item.filePath === filePath)?.ids || []

      // 遍历该文件中被选择的流
      for (const streamIndex of selectedStreamIndices) {
        // 将字符串索引转换为数字，并确保流索引有效
        const streamIdNumber = Number.parseInt(streamIndex, 10)
        if (!Number.isNaN(streamIdNumber) && streamIdNumber >= 0 && streamIdNumber < fileStreams.length) {
          const stream = fileStreams[streamIdNumber]

          if (stream.codec_type === 'video') {
            // 检查视频编码是否需要转码
            if (!['h264', 'hevc'].some(codec => stream.codec_name?.includes(codec))) {
              needVideoTranscode = true
            }
          }
          else if (stream.codec_type === 'audio') {
            // 检查音频编码是否需要转码
            if (!['aac', 'mp3'].some(codec => stream.codec_name?.includes(codec))) {
              needAudioTranscode = true
            }
          }
        }
      }
    }

    return { needVideoTranscode, needAudioTranscode }
  }

  /**
   * 执行FFmpeg命令
   * @private
   * @param command FFmpeg命令实例
   * @param outputPath 输出文件路径
   * @returns 执行结果
   */
  private async executeFFmpegCommand(command: ffmpeg.FfmpegCommand, outputPath: string, outputTempPath: string): Promise<MergeResult> {
    const mainWindow = getMainWindow()
    const handlers = getRendererHandlers<RendererHandlers>(mainWindow?.webContents)
    return new Promise<MergeResult>((resolve) => {
      command
        .on('start', (commandLine) => {
          console.log('合并开始:', commandLine)
        })
        .on('progress', (progress) => {
          console.log('合并进度:', progress.percent)
          handlers.onVideoAudioMergeProgress.send({
            status: 'merging',
            progress: progress.percent,
          })
        })
        .on('error', (err) => {
          console.error('FFmpeg错误:', err)
          // 清除当前命令引用
          currentFFmpegCommand = null
          handlers.onVideoAudioMergeProgress.send({
            status: 'error',
            error: err.message,
          })
          resolve({
            success: false,
            error: err.message,
          })
        })
        .on('end', async () => {
          console.log('FFmpeg处理完成')
          // 清除当前命令引用
          currentFFmpegCommand = null
          // 检查outputPath是否存在，如果存在则添加编号
          let counter = 1
          const outputExt = path.extname(outputPath)
          while (await fs.pathExists(outputPath)) {
            outputPath = `${outputPath.slice(0, -outputExt.length)}(${counter})${outputExt}`
            counter++
          }
          fs.move(outputTempPath, outputPath)
          handlers.onVideoAudioMergeProgress.send({
            status: 'success',
            outputPath,
            outputName: path.basename(outputPath),
          })
          resolve({
            success: true,
            outputPath,
          })
        })
        .run()
    })
  }
}

export default new VideoAudioMergeService()
