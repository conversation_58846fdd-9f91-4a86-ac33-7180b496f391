import type { ReactNode } from 'react'
import React, { useEffect, useState } from 'react'
import { twMerge } from 'tailwind-merge'

interface ProgressProps {
  type?: 'line' | 'circle'
  percent?: number
  strokeWidth?: number
  showInfo?: ReactNode
  status?: 'normal' | 'success' | 'error'
  className?: string
  size?: number
}

const Progress: React.FC<ProgressProps> = ({
  type = 'line',
  percent = 0,
  strokeWidth = type === 'circle' ? 8 : 4,
  showInfo,
  status = 'normal',
  className,
  size = 80,
}) => {
  const [currentPercent, setCurrentPercent] = useState(0)

  useEffect(() => {
    setCurrentPercent(percent)
  }, [percent])

  if (type === 'line') {
    return (
      <div className={twMerge('w-full', className)}>
        <div className="relative w-full h-2 bg-gray-200 rounded-full overflow-hidden">
          <div
            className={twMerge(
              'h-full transition-all duration-300 ease-out rounded-full',
              status === 'success'
                ? 'bg-green-500'
                : status === 'error'
                  ? 'bg-red-500'
                  : 'bg-blue-500',
            )}
            style={{ width: `${currentPercent}%` }}
          />
        </div>
      </div>
    )
  }

  const radius = (size - strokeWidth * 2) / 2
  const circumference = radius * 2 * Math.PI
  const offset = circumference - (currentPercent / 100) * circumference

  return (
    <div className={twMerge('inline-flex items-center justify-center relative', className)}>
      <svg width={size} height={size} className="transform -rotate-90">
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke="#f3f4f6"
          strokeWidth={strokeWidth}
          strokeLinecap="round"
        />
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke={status === 'success' ? '#22c55e' : status === 'error' ? '#ef4444' : '#3b82f6'}
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          strokeDashoffset={offset}
          strokeLinecap="round"
          className="transition-all duration-300 ease-out"
        />
      </svg>
      <div className="absolute">
        {showInfo}
      </div>
    </div>
  )
}

export default Progress
