import type { ReactNode, Ref } from 'react'
import { getContainer } from '@/utils/container'
import { setRef } from '@/utils/ref'

import {
  cloneElement,
  forwardRef,
  isValidElement,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { createPortal } from 'react-dom'

export interface PortalProps {
  children: ReactNode
  container?: Element | (() => Element | null) | null
  disablePortal?: boolean
}

const Portal = forwardRef(
  (props: PortalProps, forwardedRef: React.ForwardedRef<Element>) => {
    const { children, container, disablePortal = false } = props
    const [mountNode, setMountNode] = useState<ReturnType<typeof getContainer>>(
      getContainer(null),
    )

    const handleRef = useMemo(() => {
      // React 19 需要从 props 拿 ref
      const childRef = isValidElement(children)
        ? // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (children as { ref?: Ref<any> })?.ref
        : null

      const refs = [childRef, forwardedRef]

      if (refs.every(ref => ref == null)) {
        return null
      }

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return (instance: any) => refs.forEach(ref => setRef(ref, instance))
    }, [children, forwardedRef])

    useEffect(() => {
      if (!disablePortal) {
        setMountNode(getContainer(container) ?? document.body)
      }
    }, [container, disablePortal])

    useEffect(() => {
      if (mountNode && !disablePortal) {
        setRef(forwardedRef, mountNode)
        return () => {
          setRef(forwardedRef, null)
        }
      }

      return undefined
    }, [forwardedRef, mountNode, disablePortal])

    if (disablePortal) {
      if (isValidElement(children)) {
        const newProps = {
          ref: handleRef,
        }
        return cloneElement(children, newProps)
      }
      return children
    }

    return mountNode ? createPortal(children, mountNode) : mountNode
  },
)

export default Portal
