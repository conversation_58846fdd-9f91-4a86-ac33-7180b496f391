import * as Sentry from '@sentry/electron/renderer'
import { contextBridge, ipcRenderer } from 'electron'

type RendererFunction =
  import('@common/types/electron-bridge').RendererFunction

console.log('Preload脚本加载...')

contextBridge.exposeInMainWorld('electronAPI', {
  // 取消下载
  cancelDownload: (taskId: string) =>
    ipcRenderer.invoke('cancel-download', taskId),
  // 获取视频信息
  startParseDownloadConvert: (url: string, taskId: string) =>
    ipcRenderer.invoke('start-parse-download-convert', url, taskId),
  // 获取设置
  getSettings: () => ipcRenderer.invoke('get-settings'),
  // 保存设置
  saveSettings: (settings: unknown) =>
    ipcRenderer.invoke('save-settings', settings),
  // 选择目录
  selectDirectory: () => ipcRenderer.invoke('select-directory'),
  // 打开文件位置
  openFileLocation: (taskId: string) =>
    ipcRenderer.invoke('open-file-location', taskId),
  // 打开路径位置
  openPathLocation: (path: string) =>
    ipcRenderer.invoke('open-path-location', path),
  // 获取文件信息
  getLocationFileInfo: (filePath: string) =>
    ipcRenderer.invoke('get-location-file-info', filePath),
  // 获取封面图片
  fetchImage: async (url: string, headers?: Record<string, string>) => {
    try {
      const safeHeaders = headers
        ? JSON.parse(JSON.stringify(headers))
        : undefined
      const result = await ipcRenderer.invoke('fetch-image', url, safeHeaders)
      return {
        success: true,
        dataBase64: result.dataBase64 as string,
        error: undefined,
      }
    }
    catch (error) {
      console.error('无法获取图片:', error)
      Sentry.captureException(`无法获取图片:${error}`)
      return {
        success: false,
        dataBase64: undefined,
        error: error instanceof Error ? error.message : String(error),
      }
    }
  },
  // 打开外部链接
  openExternal: (url: string) => ipcRenderer.invoke('open-external', url),
  // 添加错误日志 API
  logError: (error: {
    message: string
    stack?: string
    name?: string
    context?: string
    severity?: 'low' | 'medium' | 'high' | 'critical'
    category?: 'network' | 'parsing' | 'download' | 'system' | 'user'
  }) => ipcRenderer.invoke('log-error', error),
  // 添加事件日志 API
  logEvent: (eventName: string, params?: Record<string, unknown>) =>
    ipcRenderer.invoke('log-event', eventName, params),
  // 添加新的cookie相关API
  updateBrowserCookies: (browser: string) =>
    ipcRenderer.invoke('update-browser-cookies', browser),
  openAuthWindow: (url: string, siteKey: string) =>
    ipcRenderer.invoke('open-auth-window', url, siteKey),
  removeAuth: (url: string) => ipcRenderer.invoke('remove-auth', url),
  checkAuthStatus: (url: string) =>
    ipcRenderer.invoke('check-auth-status', url),
  getSavedSites: () => ipcRenderer.invoke('get-saved-sites'),
  saveSites: (sites: unknown[]) => ipcRenderer.invoke('save-sites', sites),
  saveDownloadTask: (task: unknown) =>
    ipcRenderer.invoke('save-download-task', task),
  getDownloadTasks: () => ipcRenderer.invoke('get-download-tasks'),
  clearCompletedTasks: () => ipcRenderer.invoke('clear-completed-tasks'),
  getDownloadTask: (taskId: string) =>
    ipcRenderer.invoke('get-download-task', taskId),
  clearJsonTasks: (taskId: string) =>
    ipcRenderer.invoke('clear-json-tasks', taskId),
  checkForUpdates: () => ipcRenderer.invoke('check-for-updates'),
  downloadUpdate: (url: string) => ipcRenderer.invoke('download-update', url),
  onUpdateDownloadProgress: (callback: (progress: number) => void) => {
    ipcRenderer.on('update-download-progress', (_, data) =>
      callback(data.progress))
  },
  removeUpdateProgressListener: () => {
    ipcRenderer.removeAllListeners('update-download-progress')
  },
  quitAndInstall: (filePath: string) =>
    ipcRenderer.invoke('quit-and-install', filePath),
  checkLocalInstaller: (version: string) =>
    ipcRenderer.invoke('check-local-installer', version),
  onUpdateDownloaded: (callback: (filePath: string) => void) => {
    ipcRenderer.on('update-downloaded', (_, data) => callback(data.filePath))
  },
  removeUpdateDownloadedListener: () => {
    ipcRenderer.removeAllListeners('update-downloaded')
  },
  platform: process.platform,
  changeLanguage: (lang: string) => ipcRenderer.invoke('change-language', lang),
  getSystemLanguage: () => ipcRenderer.invoke('get-system-language'),
})

contextBridge.exposeInMainWorld('electron', {
  ipcRenderer: {
    on: (channel: string, func: RendererFunction) =>
      ipcRenderer.on(channel, func),
    removeListener: (channel: string, func: RendererFunction) =>
      ipcRenderer.removeListener(channel, func),
    // 添加错误监听器
    onError: (callback: RendererFunction) =>
      ipcRenderer.on('show-error', callback),
    // 添加 invoke 方法
    invoke: (channel: string, ...args: unknown[]) =>
      ipcRenderer.invoke(channel, ...args),
  },
})

console.log('Preload脚本加载完成')
