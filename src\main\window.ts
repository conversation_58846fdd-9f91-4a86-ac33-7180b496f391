import type { RendererHandlers } from './renderer-handlers'
import path from 'node:path'
import { getRendererHandlers } from '@egoist/tipc/main'
import { isDev } from '@main/constants/env'
import { logError } from '@main/lib/logger'
import taskService from '@main/service/task'
import { app, BrowserWindow, session } from 'electron'

const windows = {
  mainWindow: null as BrowserWindow | null,
  authWindow: null as BrowserWindow | null,
}

export function createMainWindow() {
  // 修改 session 配置
  const ses = session.defaultSession
  ses.clearStorageData({
    storages: ['serviceworkers', 'localstorage', 'websql'],
  })

  const window = new BrowserWindow({
    width: 1000,
    height: 800,
    minWidth: 900,
    minHeight: 600,
    autoHideMenuBar: true,
    frame: true,
    webPreferences: {
      preload: path.join(__dirname, '../preload/preload.js'),
      devTools: isDev, // 修改为始终允许devTools
      sandbox: false,
      webSecurity: !isDev,
      webviewTag: true,
      nodeIntegration: true,
      contextIsolation: false,
    },
  })
  if (isDev) {
    window.loadURL('http://localhost:5173')
  }
  else {
    const rendererPath = path.join(__dirname, '../renderer/index.html')
    window.loadFile(rendererPath)
  }

  // 监听窗口关闭事件
  window.on('close', async (event) => {
    event.preventDefault()
    // 发送关闭窗口事件
    const handlers = getRendererHandlers<RendererHandlers>(window.webContents)
    const existUnfinishedTask = await taskService.getInterruptTasks()
    let exist = false
    for (const task of existUnfinishedTask) {
      if (!task.errorMessage) {
        exist = true
        break
      }
    }
    handlers.onAppClose.send(exist)
  })

  window.on('closed', () => {
    windows.mainWindow = null
    app.quit()
  })

  // 添加全局错误处理
  process.on('uncaughtException', async (error) => {
    if (isDev) {
      logError('未捕获异常', {
        error: error.message,
        stack: error.stack,
        name: error.name,
      })
    }
  })

  process.on('unhandledRejection', async (reason) => {
    if (isDev) {
      logError('未处理异常', {
        reason: reason instanceof Error ? reason.message : String(reason),
        stack: reason instanceof Error ? reason.stack : undefined,
      })
    }
  })

  windows.mainWindow = window
  return window
}

export const getMainWindow = () => windows.mainWindow

export function getMainWindowOrCreate() {
  if (!windows.mainWindow) {
    windows.mainWindow = createMainWindow()
  }
  return windows.mainWindow
}

export function destroyMainWindow() {
  windows.mainWindow?.destroy()
  windows.mainWindow = null
}

/**
 * 创建授权窗口
 * @param url 要打开的Auth URL
 * @returns 授权窗口实例
 */
export function createAuthWindow(url: string) {
  // 如果已经存在认证窗口，关闭它
  if (windows.authWindow) {
    windows.authWindow.close()
    windows.authWindow = null
  }

  // 创建新的认证窗口
  const window = new BrowserWindow({
    width: 800,
    height: 600,
    minWidth: 800,
    minHeight: 600,
    autoHideMenuBar: true,
    modal: true,
    parent: getMainWindowOrCreate(),
    webPreferences: {
      preload: path.join(__dirname, '../preload/preload.js'), // 使用主应用的预加载脚本
      devTools: isDev,
      webSecurity: !isDev,
      webviewTag: true,
      nodeIntegration: true,
      contextIsolation: false,
    },
  })

  // 对URL进行编码以确保它可以安全地作为查询参数传递
  const encodedUrl = encodeURIComponent(url)

  // 加载React应用并导航到Auth页面，将URL作为查询参数传递
  if (isDev) {
    window.loadURL(`http://localhost:5173/#/auth?url=${encodedUrl}`)
  }
  else {
    // 修复打包后的路径问题：分离文件路径和哈希参数
    const rendererPath = path.join(__dirname, '../renderer/index.html')
    window.loadFile(rendererPath, { hash: `auth?url=${encodedUrl}` })
  }

  window.on('closed', () => {
    windows.authWindow = null
  })

  windows.authWindow = window
  return window
}

export const getAuthWindow = () => windows.authWindow

export function destroyAuthWindow() {
  windows.authWindow?.destroy()
  windows.authWindow = null
}

/**
 * 获取授权窗口的session，用于获取和管理cookies
 * @returns 授权窗口的session
 */
export function getAuthSession() {
  return session.defaultSession
}
