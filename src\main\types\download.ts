import type { ErrorStatus, TaskStatus } from './status'

export interface DownloadProgressData extends ErrorStatus {
  taskId: string // 任务id
  text: string // 标题
  filePath: string // 文件路径
  fileSize: number | null // 文件大小
  taskStatus: TaskStatus // 状态
  thumbnail: string | null // 缩略图
  extension: string | null // 扩展名, 例如: exe, mp4, mp3...
  resolutionWidth: number | null // 分辨率宽度
  resolutionHeight: number | null // 分辨率高度
  bitrate: number | null // 比特率
  totalSize: number | null // 下载的总大小
  downloadedSize: number | null // 已下载大小
  speed: number | null // 下载速度
  eta: string | null // 预计剩余完成时间
  duration: number | null // 音/视频时长
  percent?: number | null // 转换进度
  isLive?: boolean // 是否为直播任务
}

export interface DownloadResult {
  url: string
  filePath: string
  language?: string
  type?: string
}

export interface DownloadItem {
  url: string
  acode?: string
  headers?: Record<string, string>
  language?: string
  type?: 'audio' | 'video' | 'subtitle'
  ext?: string
  tempFilePath?: string
  optionalDownload?: boolean // 标记此下载项失败是否影响整个任务，true表示失败不影响整个任务
}

export interface VideoInfo {
  id: string
  webpage_url?: string
  title: string
  thumbnail: string
  thumbnailHeaders?: HeadersInit
  duration: number
  description: string
  uploader: string
  formats: VideoFormat[]
  isPlaylist: boolean
  audioLanguages: string[]
}

export interface VideoFormat {
  format_id: string
  ext: string
  resolutionWidth: number
  resolutionHeight: number
  filesize: number
  duration: number
  vcodec: string
  acodec: string
  fps: number
  audioBitrate: string
}

/**
 * URL项接口定义
 */
export interface UrlItem {
  url: string
  headers?: Record<string, string>
  info?: Record<string, string>
}
