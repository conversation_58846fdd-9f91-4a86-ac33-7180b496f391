import path from 'node:path'
import process from 'node:process'

import { __dirname, copyFileHelper } from './helper.mjs'

function main() {
  try {
    const binDir = path.join(__dirname, '..', 'public', 'bin')

    // 复制 ffmpeg
    copyFileHelper(
      path.join(binDir, 'ffmpeg-arm64'),
      path.join(binDir, 'ffmpeg'),
      'ffmpeg',
      'arm64',
    )

    // 复制 ffprobe
    copyFileHelper(
      path.join(binDir, 'ffprobe-arm64'),
      path.join(binDir, 'ffprobe'),
      'ffprobe',
      'arm64',
    )

    // 复制 snapfile
    copyFileHelper(
      path.join(binDir, 'snapfile-arm64'),
      path.join(binDir, 'snapfile'),
      'snapfile',
      'arm64',
    )
  }
  catch (error) {
    console.error('❌ 复制失败:', error.message)
    process.exit(1)
  }
}

main()
