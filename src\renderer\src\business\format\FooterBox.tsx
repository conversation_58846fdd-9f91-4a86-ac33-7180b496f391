import Popconfirm from '@/components/Popconfirm'
import { useFormatStore } from '@/store/format'
import { AUDIO_OUTPUT_FORMATS, IMAGE_OUTPUT_FORMATS, VIDEO_OUTPUT_FORMATS } from '@common/constants/format'
import { Button, Dropdown, DropdownItem, Label, Radio, Spinner, TextInput } from 'flowbite-react'
import { Plus, Search } from 'flowbite-react-icons/outline'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { GrClearOption } from 'react-icons/gr'
import { HiCheck } from 'react-icons/hi'
import { TbCheck, TbSwitchHorizontal } from 'react-icons/tb'

function FooterBox({ addFile }: { addFile: () => void }) {
  const { t } = useTranslation()
  const [search, setSearch] = useState('')
  const [mediaType, setMediaType] = useState<'all' | 'video' | 'audio'>('all')
  const { convertStatus, format, outputFormat, setOutputFormat, stopConvertTask, clearAllData, startConvertTask } = useFormatStore()

  const AllOutputFormats = VIDEO_OUTPUT_FORMATS.concat(AUDIO_OUTPUT_FORMATS, IMAGE_OUTPUT_FORMATS)

  const formatList = useMemo(() => {
    switch (format) {
      case 'video':
        if (mediaType === 'all') {
          return VIDEO_OUTPUT_FORMATS.concat(AUDIO_OUTPUT_FORMATS).filter(f => f.label.toLowerCase().includes(search.toLowerCase()))
        }
        if (mediaType === 'video') {
          return VIDEO_OUTPUT_FORMATS.filter(f => f.label.toLowerCase().includes(search.toLowerCase()))
        }
        if (mediaType === 'audio') {
          return AUDIO_OUTPUT_FORMATS.filter(f => f.label.toLowerCase().includes(search.toLowerCase()))
        }
        return VIDEO_OUTPUT_FORMATS.concat(AUDIO_OUTPUT_FORMATS).filter(f => f.label.toLowerCase().includes(search.toLowerCase()))
      case 'audio':
        return AUDIO_OUTPUT_FORMATS
      case 'image':
        return IMAGE_OUTPUT_FORMATS
      default:
        return AllOutputFormats
    }
  }, [format, search, mediaType])

  useEffect(() => {
    setSearch('')
    setMediaType('all')
    switch (format) {
      case 'video':
        if (outputFormat) {
          const isExist = VIDEO_OUTPUT_FORMATS.concat(AUDIO_OUTPUT_FORMATS).some(f => f.key === outputFormat.key)
          if (!isExist) {
            setOutputFormat(VIDEO_OUTPUT_FORMATS[0])
          }
        }
        else {
          setOutputFormat(VIDEO_OUTPUT_FORMATS[0])
        }
        break
      case 'audio':
        if (outputFormat) {
          const isExist = AUDIO_OUTPUT_FORMATS.some(f => f.key === outputFormat.key)
          if (!isExist) {
            setOutputFormat(AUDIO_OUTPUT_FORMATS[0])
          }
        }
        else {
          setOutputFormat(AUDIO_OUTPUT_FORMATS[0])
        }
        break
      case 'image':
        if (outputFormat) {
          const isExist = IMAGE_OUTPUT_FORMATS.some(f => f.key === outputFormat.key)
          if (!isExist) {
            setOutputFormat(IMAGE_OUTPUT_FORMATS[0])
          }
        }
        else {
          setOutputFormat(IMAGE_OUTPUT_FORMATS[0])
        }
        break
      default:
        break
    }
  }, [format])

  return (
    <footer className="shrink-0 w-full flex items-center justify-between p-4 bg-white">
      <div className="flex items-center gap-4">
        <Button outline={convertStatus !== 'completed'} color="blue" disabled={convertStatus === 'converting'} onClick={addFile}>
          <span className="flex items-center gap-2 text-sm font-medium">
            <Plus className="w-4 h-4" />
            {t('format.addFiles')}
          </span>
        </Button>
        <Popconfirm
          disabled={convertStatus !== 'converting'}
          placement="top"
          title={t('format.clearModal.title')}
          onConfirm={async () => {
            await stopConvertTask()
            clearAllData()
          }}
          okButtonProps={{
            color: 'failure',
          }}
          okText={t('format.clearModal.clear')}
        >
          <Button outline color="black" onClick={() => convertStatus !== 'converting' && clearAllData()}>
            <span className="flex items-center gap-2 text-sm font-medium">
              <GrClearOption className="w-4 h-4" />
              {t('format.clear')}
            </span>
          </Button>
        </Popconfirm>
      </div>
      <div className="flex items-center gap-4">
        <Dropdown
          label={(
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">
                {t('format.outputFormat')}
              </span>
              <span className="text-sm">
                {AllOutputFormats.find(f => f.key === outputFormat?.key)?.label}
              </span>
            </div>
          )}
          disabled={convertStatus === 'converting'}
          inline
          theme={{
            arrowIcon: 'ml-1 w-5 h-5',
            inlineWrapper: 'flex items-center p-1 rounded cursor-pointer focus:outline-none disabled:cursor-not-allowed hover:bg-gray-100',
            content: 'focus:outline-none',
          }}
        >
          <div>
            {format === 'video' && (
              <>
                <div className="p-4 border-b border-gray-100">
                  <TextInput
                    icon={() => <Search className="w-4 h-4" />}
                    placeholder={t('format.searchPlaceholder')}
                    className="[&_input]:py-2"
                    value={search}
                    onChange={e => setSearch(e.target.value)}
                    type="text"
                    sizing="sm"
                    onKeyDown={e => e.stopPropagation()}
                  />
                </div>
                <div className="flex items-center gap-4 p-4 border-b border-gray-100">
                  <div className="flex items-center gap-2">
                    <Radio id="all" name="media" checked={mediaType === 'all'} onChange={() => setMediaType('all')} />
                    <Label htmlFor="all">{t('format.all')}</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Radio id="video" name="media" checked={mediaType === 'video'} onChange={() => setMediaType('video')} />
                    <Label htmlFor="video">{t('format.video')}</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Radio id="audio" name="media" checked={mediaType === 'audio'} onChange={() => setMediaType('audio')} />
                    <Label htmlFor="audio">{t('format.audio')}</Label>
                  </div>
                </div>
              </>
            )}
            <div className="max-h-[200px] overflow-y-auto">
              {
                formatList.map(item => (
                  <DropdownItem key={item.label + item.value} className="flex items-center justify-between text-sm text-gray-900" onClick={() => setOutputFormat(item)}>
                    <span>{item.label}</span>
                    <HiCheck className={item.key === outputFormat?.key ? 'visible' : 'invisible'} />
                  </DropdownItem>
                ))
              }
            </div>
          </div>
        </Dropdown>
        {convertStatus === 'completed'
          ? (
              <Button color="light" disabled>
                <span className="flex items-center gap-2 text-sm font-medium">
                  <TbCheck className="w-5 h-5" />
                  {t('format.completed')}
                </span>
              </Button>
            )
          : (
              <Button disabled={convertStatus === 'converting'} onClick={startConvertTask}>
                <span className="flex items-center gap-2 text-sm font-medium">
                  {convertStatus === 'converting' && <Spinner className="fill-blue-700" size="sm" />}
                  {convertStatus === 'waiting' && <TbSwitchHorizontal className="w-5 h-5" />}
                  {convertStatus === 'converting' ? t('format.converting') : t('format.convert')}
                </span>
              </Button>
            )}
      </div>
    </footer>
  )
}

export default FooterBox
