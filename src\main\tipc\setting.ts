import type { SettingStoreType } from '@main/types/setting'
import SettingService from '@main/service/setting'
import { t } from './instance'

export const settingRoute = {
  // 保存设置
  saveSetting: t.procedure
    .input<SettingStoreType>()
    .action(async ({ input }) => {
      await SettingService.saveSetting(input)
    }),
  // 获取设置
  getSetting: t.procedure
    .action(async () => {
      return SettingService.getSetting()
    }),
}
