/**
 * 媒体资源信息接口
 */
export interface MediaResource {
  url: string
  contentType: string
  contentLength: number
  fileName: string
  fileExt: string
  timestamp: number
  requestHeaders: Record<string, string>
  responseHeaders: Record<string, string[]>
  method: string
  resourceType: string
  referrer: string
  tabId?: number
  id: string
}

export interface SnifferMediaDownloadInfo {
  url: string
  title: string
  fileExt: string
  httpHeaders: Record<string, string>
}

/**
 * 文件扩展名过滤规则
 */
export interface ExtFilter {
  ext: string
  minSize: number // KB为单位
  enabled: boolean
}

/**
 * MIME类型过滤规则
 */
export interface TypeFilter {
  mime: string
  minSize: number // KB为单位
  enabled: boolean
}

/**
 * 正则表达式过滤规则
 */
export interface RegexFilter {
  pattern: string
  flags: string
  specifiedExt: string
  isBlocking: boolean
  enabled: boolean
}

/**
 * 资源嗅探存储类型
 */
export interface SnifferStoreType {
  resourceSnifferFilters?: {
    extFilters: ExtFilter[]
    typeFilters: TypeFilter[]
    regexFilters: RegexFilter[]
  }
  // 可以在这里添加其他资源嗅探相关的配置
}

/**
 * url书签存储类型
 */
export interface UrlBookmarkStoreType {
  bookmarks: {
    url: string
    title: string
    mainDomain: string
  }[]
}
