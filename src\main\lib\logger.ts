import type { LogParam } from '@common/types/logger'
import { isDev } from '@main/constants/env'
import { app } from 'electron'
import log from 'electron-log/main'

/**
 * 初始化日志系统 - 仅主进程
 */
export function initLogger() {
  // 配置文件日志 - 只记录重要信息
  log.transports.file.level = isDev ? 'info' : 'warn' // 减少debug级别日志
  log.transports.file.maxSize = 10 * 1024 * 1024 // 10MB，保留的单个日志文件大小
  log.transports.file.format = '[{y}-{m}-{d} {h}:{i}:{s}] [{level}] {text}' // 移除毫秒

  // 控制台日志配置 - 开发环境启用，生产环境禁用
  if (isDev) {
    log.transports.console.level = 'info' // 开发时启用控制台输出
    log.transports.console.format = '[{h}:{i}:{s}] [{level}] {text}' // 简化控制台格式
  } else {
    log.transports.console.level = false // 生产环境禁用控制台
  }

  // 禁用IPC传输（不需要与渲染进程通信）
  log.transports.ipc.level = false

  // 开启错误捕获
  log.errorHandler.startCatching({
    showDialog: false, // 不显示错误对话框
    onError: ({ error, processType, versions }) => {
      log.error('未捕获的错误:', {
        message: error.message,
        stack: error.stack,
        processType,
        versions,
      })
    },
  })

  // 记录应用启动信息
  log.info('应用启动', {
    version: app.getVersion(),
    platform: process.platform,
    arch: process.arch,
    isDev,
    logPath: log.transports.file.getFile().path,
  })

  return log
}

// 导出默认日志实例
export default log

// 导出便捷方法
export const logInfo = (message: string, data?: LogParam) => log.info(message, data)
export const logWarn = (message: string, data?: LogParam) => log.warn(message, data)
export const logError = (message: string, data?: LogParam) => log.error(message, data)
export const logDebug = (message: string, data?: LogParam) => log.debug(message, data)
