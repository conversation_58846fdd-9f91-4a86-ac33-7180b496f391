/**
 * yt-dlp命令行解析后的JSON数据
 */
export interface YtDlpResponse {
  title?: string
  fulltitle?: string
  description?: string
  channel?: string
  uploader?: string
  thumbnail?: string
  direct?: boolean
  url: string
  formats?: YtDlpFormat[]
  is_live?: boolean
  http_headers?: Record<string, string>
  cookies?: string
  subtitles?: Record<string, SubtitleFormat[]>
  automatic_captions?: Record<string, SubtitleFormat[]>
}

export interface SubtitleFormat {
  ext: string
  url: string
}

export interface YtDlpFormat {
  acodec?: string
  vcodec?: string
  resolution?: string
  language?: string
  ext?: string
  url: string
  width?: number
  height?: number
  fps?: number
  filesize?: number
  filesize_approx?: number
  http_headers?: Record<string, string>
  format_id?: string
  abr?: number
  cookies?: string
  video_ext?: string
  audio_ext?: string
}

/**
 * yt-dlp 更新状态
 */
export type YtDlpStatus = 'idle' | 'updating' | 'failed'
