import FaviconImg from '@/components/FaviconImg'
import { useNetworkStore } from '@/store/network'
import { isValidUrl, normalizeUrl } from '@/utils/url'
import { Button, Label, Modal, TextInput } from 'flowbite-react'
import { Close, Plus } from 'flowbite-react-icons/outline'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

function AddSiteModal({ showModal, setShowModal, onAdd }: { showModal: boolean, setShowModal: (showModal: boolean) => void, onAdd: (url: string, title?: string) => void }) {
  const { t } = useTranslation()
  const [siteError, setSiteError] = useState('')

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.target as HTMLFormElement)
    const site = (formData.get('site') as string).trim()
    const title = (formData.get('name') as string).trim()
    if (!site || !isValidUrl(site)) {
      setSiteError(t('messages.validUrlPrompt'))
      return
    }
    onAdd(normalizeUrl(site), title)
    setSiteError('')
  }

  return (
    <Modal size="lg" show={showModal}>
      <Modal.Body className="p-4">
        <div className="flex items-center justify-between mb-1.5">
          <p className="text-lg font-semibold text-gray-900">{t('network.addSiteModal.title')}</p>
          <Close className="w-5 h-5 text-gray-400 cursor-pointer" onClick={() => setShowModal(false)} />
        </div>
        <form className="space-y-4" onSubmit={handleSubmit}>
          <div className="flex flex-col gap-2">
            <Label htmlFor="site" aria-required className='w-fit relative text-sm text-gray-900 font-medium after:content-["*"] after:absolute after:-right-2.5 after:top-1/2 after:-translate-y-1/2 after:text-red-700'>{t('network.addSiteModal.site')}</Label>
            <TextInput id="site" name="site" placeholder={t('network.addSiteModal.sitePlaceholder')} className="w-full" color={siteError ? 'failure' : 'gray'} />
            <span className="text-red-700 text-sm">{siteError}</span>
          </div>
          <div className="flex flex-col gap-2">
            <Label htmlFor="name" className="text-sm text-gray-900 font-medium">{t('network.addSiteModal.name')}</Label>
            <TextInput id="name" name="name" placeholder={t('network.addSiteModal.namePlaceholder')} className="w-full" />
          </div>
          <div className="flex items-center justify-center">
            <Button type="submit" className="text-center px-6">{t('network.addSiteModal.add')}</Button>
          </div>
        </form>
      </Modal.Body>
    </Modal>
  )
}

function SiteItem({ site, name, mainDomain, onDelete, onGo }: { site: string, name: string, mainDomain: string, onDelete: (url: string) => Promise<void>, onGo: (url: string) => void }) {
  const [showCloseIcon, setShowCloseIcon] = useState(false)
  const [showLogoError, setShowLogoError] = useState(false)

  const handleMouseEnter = () => {
    setShowCloseIcon(true)
  }

  const handleMouseLeave = () => {
    setShowCloseIcon(false)
  }

  const handleClose = (e: React.MouseEvent<SVGSVGElement>) => {
    e.stopPropagation()
    onDelete(site)
  }

  return (
    <div className="flex flex-col gap-2">
      <div
        className="h-full relative flex items-center justify-center aspect-4/3 rounded-2xl border border-gray-200 bg-white py-9.5 cursor-pointer"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={() => onGo(site)}
      >
        <FaviconImg domain={site} onError={() => setShowLogoError(true)} />
        {showLogoError && <p className="text-[32px] font-medium">{mainDomain}</p>}
        {showCloseIcon && <Close className="absolute top-2.5 right-2.5 w-4 h-4 text-gray-400" onClick={handleClose} />}
      </div>
      <p className="text-sm font-semibold text-center">{name}</p>
    </div>
  )
}

function NetworkHome({ onSiteItemClick }: { onSiteItemClick: (url: string) => void }) {
  const { t } = useTranslation()
  const [showAddSiteModal, setShowAddSiteModal] = useState(false)
  const { siteList, addSite, deleteSite } = useNetworkStore()

  const handleAddSite = (url: string, title?: string) => {
    addSite(url, title)
    setShowAddSiteModal(false)
  }

  return (
    <div className="w-full h-full flex flex-col overflow-auto">
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="grid grid-cols-4 gap-4">
          <div className="flex flex-col gap-2" onClick={() => setShowAddSiteModal(true)}>
            <div className="min-w-41 flex items-center justify-center aspect-4/3 rounded-2xl border border-gray-200 bg-white py-9.5 cursor-pointer">
              <Plus className="w-8 h-8 text-blue-700" />
            </div>
            <p className="text-sm font-semibold text-center">{t('network.addSite')}</p>
          </div>
          {siteList.map(item => <SiteItem key={item.url} site={item.url} name={item.title} mainDomain={item.mainDomain} onDelete={deleteSite} onGo={() => onSiteItemClick(item.url)} />)}
          <AddSiteModal showModal={showAddSiteModal} setShowModal={setShowAddSiteModal} onAdd={handleAddSite} />
        </div>
      </div>
    </div>
  )
}

export default NetworkHome
