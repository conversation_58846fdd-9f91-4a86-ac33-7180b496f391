import { useSnapany } from '@/hooks/snapany'
import { Button, Modal, Progress } from 'flowbite-react'
import { Dispatch, ForwardedRef, forwardRef, SetStateAction, useEffect, useImperativeHandle, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useUpdateStore } from '@/store/update'

export interface DownloadAppModalRef {
  setShowModal: Dispatch<SetStateAction<boolean>>
}

const DownloadAppModal = (props: any, ref: ForwardedRef<DownloadAppModalRef>) => {
  const { t } = useTranslation()
  const [showModal, setShowModal] = useState(false)
  const { updateInfo, isOverMin, hasUpdatePackage, progressData, isDownloadError, setIsNextRemind, downloadApp, installApp } = useUpdateStore()
  const { settings } = useSnapany()
  const langue = ['system', 'zh'].includes(settings?.language || 'zh') ? 'zh' : 'en'

  const progress = useMemo(() => {
    if (progressData?.downloadedSize && progressData?.totalSize) {
      return Math.floor(progressData?.downloadedSize / progressData?.totalSize * 100)
    }
    return undefined
  }, [progressData])

  // 关闭弹窗
  const handleCancel = () => {
    setShowModal(false)
  }

  useEffect(() => {
    if (isDownloadError) {
      setShowModal(false)
    }
  }, [isDownloadError])

  // 稍后提醒
  const handleNext = () => {
    setIsNextRemind(true)
    handleCancel()
  }

  useImperativeHandle(ref, () => ({
    setShowModal
  }))

  return (
    <>
      <Modal
        onClose={() => setShowModal(false)}
        popup
        show={showModal}
        size="lg"
      >
        <Modal.Body className="p-5">
          <div>
            <div>
              <p className="text-lg font-semibold text-gray-900 mb-1">
                {t('update.newVersion')}
                {' '}
                v
                {updateInfo?.latestVersion}
              </p>
              <div className="text-gray-500 mb-5">
                <p className="mb-2">{t('update.whatsNew')}</p>
                <p className="indent-4">{updateInfo?.upgradeContent?.[langue]}</p>
                {progress && (
                  <div className="mt-4">
                    <div className="flex justify-between">
                      <span>{t('update.downloading')}</span>
                      <span>
                        {progress}
                        %
                      </span>
                    </div>
                    <Progress color="blue" className="mt-1" progress={progress} />
                  </div>
                )}
              </div>
              <div className="flex gap-4 justify-end">
                {!progress && (
                  <>
                    {isOverMin && <Button color="light" onClick={handleCancel}>{t('update.remindLater')}</Button>}
                    {hasUpdatePackage
                      ? <Button onClick={installApp}>{t('update.installNow')}</Button>
                      : <Button onClick={downloadApp}>{t('update.upgradeNow')}</Button>}
                  </>
                )}
                {progress && isOverMin && <Button color="light" onClick={handleNext}>{t('update.remindAfterDownload')}</Button>}
              </div>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </>
  )
}

export default forwardRef(DownloadAppModal)
