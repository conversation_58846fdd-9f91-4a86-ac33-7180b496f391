import type { SnifferMediaDownloadInfo } from '@main/types'
import { urlBookmarkStore } from '@main/lib/store/sniffer'
import resourceSnifferService from '@main/service/resource-sniffer'
import { getTopLevelMainDomain } from '@main/utils/urls'
import { t } from './instance'

export const snifferRoute = {
  // 为特定WebContents设置资源嗅探
  setupResourceSniffer: t.procedure.input<{ id: number }>().action(async ({ input }) => {
    try {
      // 初始化资源嗅探服务
      resourceSnifferService.init()

      // 为特定WebContents设置资源嗅探
      if (input.id) {
        resourceSnifferService.setupForWebContents(input.id)
      }

      return { success: true, message: '资源嗅探已启动' }
    }
    catch (error) {
      console.error('设置资源嗅探失败:', error)
      return { success: false, message: '资源嗅探启动失败' }
    }
  }),

  // 获取所有嗅探到的媒体资源
  getSniffedResources: t.procedure.action(async () => {
    try {
      const resources = resourceSnifferService.getAllMediaResources()
      return { success: true, resources }
    }
    catch (error) {
      console.error('获取嗅探资源失败:', error)
      return { success: false, resources: [] }
    }
  }),

  // 清除所有嗅探到的媒体资源
  clearSniffedResources: t.procedure.action(async () => {
    try {
      resourceSnifferService.clearAllMediaResources()
      return { success: true }
    }
    catch (error) {
      console.error('清除嗅探资源失败:', error)
      return { success: false }
    }
  }),

  // 下载嗅探资源
  downloadSniffedResources: t.procedure.input<{ snifferMediaDownloadInfo: SnifferMediaDownloadInfo }>().action(async ({ input }) => {
    try {
      const { snifferMediaDownloadInfo } = input
      // 下载嗅探资源
      const result = await resourceSnifferService.downloadSniffedResource(snifferMediaDownloadInfo)
      return { success: true, taskId: result }
    }
    catch (error) {
      console.error('下载嗅探资源失败:', error)
      return { success: false }
    }
  }),

  // 获取所有url书签
  getUrlBookmarks: t.procedure.action(async () => {
    return urlBookmarkStore.get('bookmarks')
  }),

  // 添加url书签
  addUrlBookmark: t.procedure.input<{ url: string, title?: string }>().action(async ({ input }) => {
    const { url, title } = input

    const rawMainDomain = getTopLevelMainDomain(url)
    const mainDomain = rawMainDomain.charAt(0).toUpperCase() + rawMainDomain.slice(1)
    // 如果title为空，则提取主域名并首字母大写作为title
    if (!title) {
      urlBookmarkStore.set('bookmarks', [{ url, title: mainDomain, mainDomain }, ...urlBookmarkStore.get('bookmarks')])
    }
    else {
      urlBookmarkStore.set('bookmarks', [{ url, title, mainDomain }, ...urlBookmarkStore.get('bookmarks')])
    }
  }),

  // 删除url书签
  deleteUrlBookmark: t.procedure.input<{ url: string }>().action(async ({ input }) => {
    const { url } = input
    urlBookmarkStore.set('bookmarks', urlBookmarkStore.get('bookmarks').filter(bookmark => bookmark.url !== url))
  }),
}
