import type { SnifferResource } from '@/store/network'
import { formatFileSize } from './video'

/**
 * 判断是否是音频
 * @param item 嗅探资源
 * @returns 是否是音频
 */
export function isAudio(item: SnifferResource) {
  const isAudioExt = ['MP3', 'WMA', 'WAV', 'M4A', 'OGG', 'ACC', 'AAC', 'WEBA', 'OPUS'].includes(item.fileExt?.toUpperCase() || '')
  const isAudioContentType = item.contentType?.startsWith('audio/') || item.contentType === 'application/ogg'
  return isAudioExt || isAudioContentType
}

/**
 * 获取资源大小
 * @param item 嗅探资源
 * @returns 资源大小
 */
export function getResourceSize(item: SnifferResource) {
  const headers = item.responseHeaders
    ? Object.fromEntries(
        Object.entries(item.responseHeaders).map(([key, value]) => [key.toLowerCase(), value]),
      )
    : null

  if (headers?.['content-range']) {
    const range = headers['content-range'][0].split('/')
    return formatFileSize(Number(range[1]))
  }
  return formatFileSize(item.contentLength)
}
