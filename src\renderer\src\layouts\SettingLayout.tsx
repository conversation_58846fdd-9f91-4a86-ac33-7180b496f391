import type { ReactNode } from 'react'
import SettingAboutPanel from '@renderer/business/setting/AboutPanel'
import SettingAuthorizationPanel from '@renderer/business/setting/AuthorizationPanel'
import SettingGeneralPanel from '@renderer/business/setting/GeneralPanel'

import SettingProxyPanel from '@renderer/business/setting/ProxyPanel'
import { SettingMenuItem } from '@renderer/client/setting-compose'
import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { HiX } from 'react-icons/hi'

type ActiveTab = 'general' | 'auth' | 'proxy' | 'about'
interface ActiveTabItem {
  key: ActiveTab
  label: string
  element: ReactNode
}

interface SettingLayoutProps {
  onClose?: () => void
}

function SettingLayout({ onClose }: SettingLayoutProps) {
  const { t } = useTranslation()

  const [activeTab, setActiveTab]
    = useState<(typeof SETTING_TAB_ITEMS)[number]['key']>('general')

  const SETTING_TAB_ITEMS: ActiveTabItem[] = useMemo(
    () => [
      {
        key: 'general',
        label: t('settings.general'),
        element: <SettingGeneralPanel />,
      },
      {
        key: 'auth',
        label: t('settings.authorization'),
        element: <SettingAuthorizationPanel />,
      },
      {
        key: 'proxy',
        label: t('settings.proxy'),
        element: <SettingProxyPanel />,
      },
      {
        key: 'about',
        label: t('settings.about'),
        element: <SettingAboutPanel />,
      },
    ],
    [t],
  )

  const title = useMemo(
    () => SETTING_TAB_ITEMS.find(item => item.key === activeTab)!.label,
    [SETTING_TAB_ITEMS, activeTab],
  )

  const content = useMemo(
    () => SETTING_TAB_ITEMS.find(item => item.key === activeTab)!.element,
    [SETTING_TAB_ITEMS, activeTab],
  )

  return (
    <div className="flex h-[70vh] w-[70vw] max-w-[1025px] max-h-[657px]">
      <aside className="py-4 bg-[#E5E7EB] min-w-[185px]">
        {SETTING_TAB_ITEMS.map(({ key, label }) => (
          <SettingMenuItem
            key={key}
            text={label}
            onClick={() => setActiveTab(key)}
            active={activeTab === key}
          />
        ))}
      </aside>

      <main className="flex grow bg-white flex-col justify-center p-5 pb-0">
        <section className="flex items-center justify-between">
          <p className="text-lg font-semibold text-gray-900">{title}</p>
          <HiX className="text-xl cursor-pointer text-gray-400" onClick={onClose} />
        </section>
        <section className="flex flex-col grow mt-5 overflow-auto hide-scrollbar">
          {content}
        </section>
      </main>
    </div>
  )
}

export default SettingLayout
