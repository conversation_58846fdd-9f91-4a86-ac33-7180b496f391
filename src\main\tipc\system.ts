import path from 'node:path'
import { SYSTEM_TO_APP_LANGUAGE_MAP } from '@main/constants/language'
import log, { logError } from '@main/lib/logger'
import { settingStore } from '@main/lib/store'
import { ytDlpStore } from '@main/lib/store/yt-dlp'
import authService from '@main/service/auth'
import snapfileService from '@main/service/snapfile'
import systemService from '@main/service/system'
import YtDlpService from '@main/service/yt-dlp'
import { checkFileExists, getTopLevelMainDomain } from '@main/utils'
import { testHttpProxyConnection, testSocks5ProxyConnection } from '@main/utils/proxy'
import { getMainWindow } from '@main/window'
import { app, dialog, shell } from 'electron'
import { t } from './instance'

export const systemRoute = {
  // 获取关于信息
  getAboutInfo: t.procedure.action(async () => {
    return {
      componentVersion: {
        version: '2025.03.26',
        latestVersion: '2025.04.14',
      },
      softwareVersion: {
        version: app.getVersion(),
        latestVersion: '0.4.0',
      },
      website: 'https://snapany.com',
    }
  }),
  // 打开文件
  openFile: t.procedure
    .input<{ filePath: string }>()
    .action(async ({ input }) => {
      // 判断文件夹或文件是否存在
      if (await checkFileExists(input.filePath)) {
        try {
          shell.openPath(input.filePath)
          return { success: true }
        }
        catch {
          return { success: false }
        }
      }
      else {
        console.log('文件不存在', { filePath: input.filePath })
        return { success: false }
      }
    }),
  // 打开文件所在位置
  openFileDir: t.procedure
    .input<{ filePath: string }>()
    .action(async ({ input }) => {
      // 判断文件夹或文件是否存在
      if (await checkFileExists(input.filePath)) {
        shell.showItemInFolder(input.filePath)
        return { success: true }
      }
      else {
        return { success: false, error: 'fileNotFound' }
      }
    }),
  // 选择文件存储的目录
  selectFileDir: t.procedure
    .action(async () => {
      const mainWindow = getMainWindow()
      const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openDirectory'],
      })
      if (!result.canceled && result.filePaths.length > 0) {
        return { success: true, path: result.filePaths[0] }
      }
      return { success: false, error: 'canceled' }
    }),
  // 获取系统语言
  getSystemLanguage: t.procedure.action(async () => {
    const langs = app.getPreferredSystemLanguages()
    let locale: string
    for (const lang of langs) {
      locale = lang.split('-')[0]
      // 特殊处理中文
      if (locale.startsWith('zh')) {
        locale = lang.includes('Hant') ? 'zh-Hant' : 'zh-Hans'
      }
      const mappedLocale = SYSTEM_TO_APP_LANGUAGE_MAP[locale]
      if (mappedLocale) {
        return mappedLocale
      }
    }
    // 兜底
    return 'en'
  }),
  // 打开外部链接
  openExternalLink: t.procedure
    .input<{ url: string }>()
    .action(async ({ input }) => {
      shell.openExternal(input.url)
    }),
  // 获取授权站点列表
  getAuthSites: t.procedure.action(async () => {
    return settingStore.get('authSites')
  }),
  // 添加授权站点
  addAuthSite: t.procedure
    .input<{ authUrl: string }>()
    .action(async ({ input }) => {
      const { authUrl } = input
      let newAuthUrl = authUrl
      if (newAuthUrl.includes('youtu.be') || newAuthUrl.includes('youtube.com')) {
        newAuthUrl = 'https://www.youtube.com'
      }
      else if (newAuthUrl.includes('twitter.com') || newAuthUrl.includes('.x.com')) {
        newAuthUrl = 'https://x.com'
      }
      else if (newAuthUrl.includes('instagram.com')) {
        newAuthUrl = 'https://www.instagram.com'
      }
      if (!newAuthUrl.startsWith('http')) {
        newAuthUrl = `https://${newAuthUrl}`
      }
      const url = new URL(newAuthUrl)
      const originUrl = url.origin
      const mainDomain = getTopLevelMainDomain(originUrl)
      const savedTitle = mainDomain.charAt(0).toUpperCase() + mainDomain.slice(1)
      // 去重
      const authSites = settingStore.get('authSites')
      const exist = authSites.find(site => site.url === originUrl)
      if (exist) {
        return { success: false, authUrl: exist.authUrl }
      }
      const authSite = {
        name: savedTitle,
        url: originUrl,
        authUrl: originUrl,
        isAuthorized: false,
        enableDelete: true,
      }
      settingStore.set('authSites', [...settingStore.get('authSites'), authSite])
      return { success: true, authUrl: authSite.authUrl }
    }),
  // 登出授权站点
  logoutAuthSite: t.procedure
    .input<{ url: string }>()
    .action(async ({ input }) => {
      const { url } = input
      const urlObj = new URL(url)
      const domain = urlObj.origin.replace('www.', '')
      settingStore.set('authSites', settingStore.get('authSites')
        .map(site =>
          site.url === urlObj.origin
            ? { ...site, isAuthorized: false }
            : site))
      await authService.deleteCookieFile(domain)
    }),
  // 删除授权站点
  deleteAuthSite: t.procedure
    .input<{ name: string }>()
    .action(async ({ input }) => {
      const { name } = input
      const authSites = settingStore.get('authSites')
      const authSite = authSites.find(site => site.name === name)
      if (authSite) {
        if (authSite.enableDelete) {
          settingStore.set('authSites', authSites.filter(site => site.name !== name))
        }
        const urlObj = new URL(authSite.url)
        const domain = urlObj.origin.replace('www.', '')
        await authService.deleteCookieFile(domain)
      }
    }),
  // 检查软件更新
  checkSoftwareLatestVersion: t.procedure.action(async () => {
    const latestVersion = await systemService.getSoftwareLatestVersion()
    return { success: true, version: latestVersion }
  }),
  // 下载软件更新
  downloadSoftwareUpdate: t.procedure
    .input<{ url: string }>()
    .action(async ({ input }) => {
      const { url } = input
      const result = await systemService.downloadSoftware(url)
      return { success: true, result }
    }),
  // 检查软件包是否存在
  checkSoftwarePackageExists: t.procedure
    .input<{ version: string }>()
    .action(async ({ input }) => {
      const { version } = input
      const result = await systemService.checkSoftwarePackageExists(version)
      return { success: result.exists, filePath: result.filePath }
    }),
  // 安装软件更新
  installSoftwareUpdate: t.procedure
    .input<{ filePath: string }>()
    .action(async ({ input }) => {
      const { filePath } = input
      await systemService.installSoftware(filePath)
    }),

  // 获取ytdlp组件版本号
  getLocalYtDlpVersion: t.procedure.action(async () => {
    // const ytDlpVersion = await YtDlpService.getYtDlpLocalVersion()
    // const ytDlpVersion = ytDlpStore.get('version')
    return {
      version: ytDlpStore.get('version'),
      status: ytDlpStore.get('status'), // 'idle' | 'updating' | 'failed'
    }
  }),

  updateYtDlp: t.procedure.action(async () => {
    // 设置更新状态为更新中
    ytDlpStore.set('status', 'updating')
    YtDlpService.updateYtDlp()
  }),

  // 关闭窗口
  closeWindow: t.procedure.action(async () => {
    // 停止snapfile服务
    try {
      await snapfileService.stop()
      console.log('Snapfile服务已停止')
    }
    catch (error) {
      console.error('停止Snapfile服务失败:', error)
    }

    const mainWindow = getMainWindow()
    mainWindow?.destroy()
    app.quit()
  }),

  // 代理连通性测试
  testProxyConnection: t.procedure
    .input<{ url: string, port: number, username?: string, password?: string, type?: 'socks5' | 'http' }>()
    .action(async ({ input }) => {
      try {
        if (input.type === 'socks5') {
          return { success: await testSocks5ProxyConnection(input) }
        }
        else {
          return { success: await testHttpProxyConnection(input) }
        }
      }
      catch {
        return { success: false }
      }
    }),

  // 打开日志目录
  openLogDirectory: t.procedure.action(async () => {
    try {
      const logFilePath = log.transports.file.getFile().path
      const logDirectory = path.dirname(logFilePath)
      shell.showItemInFolder(logFilePath)
      return { success: true, path: logDirectory }
    }
    catch (error) {
      logError('打开日志目录失败', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      })
      return { success: false, error: 'openLogDirectoryFailed' }
    }
  }),
}
