import { useDownloadStore } from '@/store/download'
import {
  DownloadConfig,
  DownloadConfigShowCurrent,
} from '@renderer/client/download-compose'
import { AUDIO_FORMATS, PLATFORMS, VIDEO_FORMATS } from '@renderer/constants/media'
import { useSnapany } from '@renderer/hooks/snapany'
import { Dropdown } from 'flowbite-react'
import { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

function DownloadConfigPlatform() {
  const { t } = useTranslation()

  const {
    platform,
    downloadType,
    videoFormat,
    changePlatform,
    changeVideoFormat,
    audioFormat,
    changeAudioFormat,
  } = useDownloadStore()

  const { updateDownloadConfig } = useSnapany()

  const isVideo = useMemo(() => downloadType === 'video', [downloadType])

  const bindAfter = useCallback(
    (fn: () => void) => async () => {
      await fn()
      const latestDownloadConfig = useDownloadStore.getState()
      await updateDownloadConfig(latestDownloadConfig)
    },
    [updateDownloadConfig],
  )

  const renderVideoFormatList = () =>
    VIDEO_FORMATS.map(({ label, value }) => (
      <DownloadConfig
        key={value}
        label={label}
        showCheck={value === videoFormat}
        onClick={bindAfter(() => changeVideoFormat(value))}
      />
    ))

  const renderAudioFormatList = () =>
    AUDIO_FORMATS.map(({ label, value }) => (
      <DownloadConfig
        key={value}
        label={label}
        showCheck={value === audioFormat}
        onClick={bindAfter(() => changeAudioFormat(value))}
      />
    ))

  return (
    <Dropdown
      inline
      label={(
        <DownloadConfigShowCurrent
          content={
            platform
              ? PLATFORMS.find(p => p.value === platform)?.label
              : isVideo
                ? VIDEO_FORMATS.find(f => f.value === videoFormat)?.label
                : AUDIO_FORMATS.find(f => f.value === audioFormat)?.label
          }
          label={platform ? t('download.for') : t('download.format')}
        />
      )}
      theme={{
        arrowIcon: 'ml-1 text-gray-500',
      }}
    >
      {isVideo ? renderVideoFormatList() : renderAudioFormatList()}

      <Dropdown.Divider />

      {PLATFORMS.map(({ label, value }) => (
        <DownloadConfig
          label={label}
          key={value}
          showCheck={value === platform}
          onClick={bindAfter(() => changePlatform(value))}
        />
      ))}
    </Dropdown>
  )
}

export default DownloadConfigPlatform
