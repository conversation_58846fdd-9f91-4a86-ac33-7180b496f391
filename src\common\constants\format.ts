/** 视频输出格式 */
export const VIDEO_OUTPUT_FORMATS = [
  { label: 'MP4', value: 'mp4', key: 'mp4' },
  { label: 'MKV', value: 'mkv', key: 'mkv' },
  { label: 'MOV', value: 'mov', key: 'mov' },
  { label: 'WEBM', value: 'webm', key: 'webm' },
  { label: 'AVI', value: 'avi', key: 'avi' },
  { label: 'FLV', value: 'flv', key: 'flv' },
  { label: 'TS', value: 'ts', key: 'ts' },
  { label: 'MPG', value: 'mpg', key: 'mpg' },
  { label: 'MPEG', value: 'mpeg', key: 'mpeg' },
  { label: 'WMV', value: 'wmv', key: 'wmv' },
  { label: 'OGV', value: 'ogv', key: 'ogv' },
  { label: '3GP', value: '3gp', key: '3gp' },
  { label: 'SWF', value: 'swf', key: 'swf' },
  { label: 'VOB', value: 'vob', key: 'vob' },
  { label: '3G2', value: '3g2', key: '3g2', extra: ['-s', '1408x1152', '-ar', '8000', '-ac', '1'] },
  { label: 'GIF', value: 'gif', key: 'gif' },
]

/** 音频输出格式 */
export const AUDIO_OUTPUT_FORMATS = [
  { label: 'MP3', value: 'mp3', key: 'mp3' },
  { label: 'AAC', value: 'm4a', key: 'aac' },
  { label: 'M4A', value: 'm4a', key: 'm4a' },
  { label: 'WAV', value: 'wav', key: 'wav' },
  { label: 'FLAC', value: 'flac', key: 'flac' },
  { label: 'OPUS', value: 'opus', key: 'opus' },
  { label: 'OGG', value: 'ogg', key: 'ogg' },
  { label: 'OGA', value: 'oga', key: 'oga' },
  { label: 'AIF', value: 'aif', key: 'aif' },
  { label: 'AIFF', value: 'aiff', key: 'aiff' },
  { label: 'WMA', value: 'wma', key: 'wma' },
  { label: 'ALAC', value: 'm4a', key: 'alac', extra: ['-c:a', 'alac'] },
  { label: 'iPhone Ringtone', value: 'm4r', key: 'm4r', extra: ['-f', 'ipod'] },
  { label: 'MP2', value: 'mp2', key: 'mp2' },
  { label: 'AMR', value: 'amr', key: 'amr', extra: ['-ar', '8000', '-ac', '1'] },
  { label: 'MMF', value: 'mmf', key: 'mmf', extra: ['-ar', '8000', '-ac', '1'] },
]

/** 图片输出格式 */
export const IMAGE_OUTPUT_FORMATS = [
  { label: 'JPEG', value: 'jpeg', key: 'jpeg' },
  { label: 'JPG', value: 'jpg', key: 'jpg' },
  { label: 'PNG', value: 'png', key: 'png' },
  { label: 'BMP', value: 'bmp', key: 'bmp' },
  { label: 'TIFF', value: 'tiff', key: 'tiff' },
  { label: 'WEBP', value: 'webp', key: 'webp' },
  { label: 'GIF', value: 'gif', key: 'gif' },
  { label: 'TGA', value: 'tga', key: 'tga' },
  { label: 'AVIF', value: 'avif', key: 'avif' },
  // { label: 'HDR', value: 'exr', key: 'hdr' },
  { label: 'EXR', value: 'exr', key: 'exr' },
  { label: 'ICO', value: 'ico', key: 'ico', extra: ['-vf', 'scale=min(256,iw):min(256,ih)'] },
  // 以下内容为暂时无法支持格式
  // { label: 'ODD', value: 'odd', key: 'odd' },
  // { label: 'EPS', value: 'eps', key: 'eps' },
  // { label: 'PSD', value: 'psd', key: 'psd' },
  // { label: 'SVG', value: 'svg', key: 'svg' },
  // { label: 'PS', value: 'webm', key: 'ps' },
  // { label: 'ICNS', value: 'icns', key: 'icns' },
  // { label: 'PDF', value: 'pdf', key: 'pdf' },
  // { label: 'HEIC', value: 'heic', key: 'heic' },
]
