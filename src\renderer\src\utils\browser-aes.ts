/**
 * 加密解密错误
 */
class CryptoError extends Error { }


// 创建空的 IV
const iv = new Uint8Array(16);


/**
 * 将字符串转换为 ArrayBuffer
 */
function stringToBuffer(str: string): ArrayBuffer {
  return new TextEncoder().encode(str).buffer as ArrayBuffer;
}

/**
 * 将 ArrayBuffer 转换为字符串
 */
function bufferToString(buffer: ArrayBuffer): string {
  return new TextDecoder().decode(buffer);
}


/**
 * 将 base64url 字符串转换为 ArrayBuffer
 */
function base64UrlToBuffer(base64url: string): ArrayBuffer {
  const base64 = base64url.replace(/-/g, '+').replace(/_/g, '/');
  const binary = atob(base64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
}

/**
 * 将 ArrayBuffer 转换为 base64url 字符串
 */
function bufferToBase64Url(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  const binary = String.fromCharCode(...bytes);
  const base64 = btoa(binary);
  return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
}

/**
 * AES加密
 * @param data 待加密的数据
 * @param key 密钥,长度为16的字符串
 * @returns base64url编码的加密数据
 * @throws {CryptoError} 加密失败
 */
async function encrypt(data: string, key: string): Promise<string> {
  try {
    // 将密钥转换为 CryptoKey
    const keyBuffer = stringToBuffer(key);
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      { name: 'AES-CBC', length: 128 },
      false,
      ['encrypt']
    );
    
    // 加密数据
    const dataBuffer = stringToBuffer(data);
    const encryptedBuffer = await crypto.subtle.encrypt(
      { name: 'AES-CBC', iv },
      cryptoKey,
      dataBuffer
    );

    return bufferToBase64Url(encryptedBuffer);
  } catch (error: any) {
    throw new CryptoError(error.message || 'encrypt error');
  }
}

/**
 * AES解密
 * @param base64url base64url编码的加密数据 
 * @param key 密钥,长度为16的字符串 
 * @returns 解密后的数据 
 * @throws {CryptoError} 解密失败
 */
async function decrypt(base64url: string, key: string): Promise<string> {
  try {
    // 将密钥转换为 CryptoKey
    const keyBuffer = stringToBuffer(key);
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      { name: 'AES-CBC', length: 128 },
      false,
      ['decrypt']
    );
    
    // 解密数据
    const encryptedBuffer = base64UrlToBuffer(base64url);
    const decryptedBuffer = await crypto.subtle.decrypt(
      { name: 'AES-CBC', iv },
      cryptoKey,
      encryptedBuffer
    );

    return bufferToString(decryptedBuffer);
  } catch (error: any) {
    throw new CryptoError(error.message || 'decrypt error');
  }
}

export { encrypt, decrypt, CryptoError } 