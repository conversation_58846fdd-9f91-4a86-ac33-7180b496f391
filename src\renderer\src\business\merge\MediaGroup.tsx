import type { FileMediaInfoItem } from '@main/types/ffmpeg'
import { formatDuration } from '@/utils/video'
import { Checkbox, Tooltip } from 'flowbite-react'
import { t } from 'i18next'
import { useMemo } from 'react'
import { TbTrash } from 'react-icons/tb'

interface MediaGroupProps {
  fileMediaInfo: FileMediaInfoItem
  checked: boolean
  onChangeCheck: () => void
  onDelete?: (filePath: string, id: string) => void
  isHighlight?: boolean
}

function MediaGroup({ fileMediaInfo, checked, onChangeCheck, onDelete, isHighlight }: MediaGroupProps) {
  const { name, filePath, children: tracks } = fileMediaInfo || {}

  const hasVideo = useMemo(() => {
    return tracks.some(item => item.codec_type === 'video')
  }, [tracks])

  const typeName = (type: string) => {
    switch (type) {
      case 'video':
        return t('merge.video')
      case 'audio':
        return t('merge.audio')
      case 'subtitle':
        return t('merge.subtitle')
      default:
        break
    }
  }

  return (
    <div className={`group flex items-start gap-2 py-1 px-4 bg-white ${isHighlight ? 'animate-highlight' : ''}`}>
      {hasVideo && <Checkbox className="mt-3 rounded-full cursor-pointer border-blue-700 bg-white checked:bg-[#1C64F2] checked:border-[#1C64F2] flex-shrink-0 focus:ring-none" checked={checked} onChange={onChangeCheck} />}
      <div className="flex-1 w-0">
        {tracks.map((track, index) => (
          <div key={track.id} className="relative">
            {index > 0 && (
              <>
                <div className={`absolute left-2 top-0 w-px bg-black ${index === tracks.length - 1 ? 'h-1/2' : 'h-full'}`} />
                <div className="absolute left-2 top-1/2 w-2 h-px bg-black" />
              </>
            )}
            <div className={`w-full flex items-center justify-between py-2 ${index > 0 ? 'pl-4.5' : ''}`}>
              <div className="w-0 flex-1 flex items-center gap-2">
                <div className={`px-1.5 py-0.5 text-xs text-white flex-shrink-0 rounded-md ${track.codec_type === 'video'
                  ? checked ? 'bg-green-800' : 'text-gray-400 bg-gray-300'
                  : track.codec_type === 'audio'
                    ? 'bg-purple-800'
                    : 'bg-yellow-800'
                }`}
                >
                  {typeName(track.codec_type)}
                </div>
                <Tooltip content={name} theme={{ target: 'truncate flex items-center' }}>
                  <span className={`truncate text-xs text-gray-400 ${(track.codec_type !== 'video' || checked) && 'text-gray-900'}`}>{name}</span>
                </Tooltip>
              </div>
              <div className="flex-shrink-0 grid grid-cols-5 gap-2 min-w-[220px] flex-[0_0_260px] items-center">
                <span className="text-xs text-gray-900 text-center col-span-2">{formatDuration(track.duration)}</span>
                <span className="text-xs text-gray-900 text-center col-span-2">{track.codec_name}</span>
                <div className="flex items-center justify-center">
                  {onDelete && (
                    <Tooltip content={t('taskActions.delete')} theme={{ content: 'relative z-20 text-nowrap' }}>
                      <TbTrash
                        className="w-7 h-7 p-1 text-gray-500 cursor-pointer rounded hover:bg-gray-100"
                        onClick={() => onDelete(filePath, track.id)}
                      />
                    </Tooltip>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default MediaGroup
