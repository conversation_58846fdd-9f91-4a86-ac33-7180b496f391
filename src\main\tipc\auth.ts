import { settingStore } from '@main/lib/store'
import authService from '@main/service/auth'
import { createAuthWindow, getAuthWindow } from '@main/window'
import { t } from './instance'

// 在模块作用域中声明变量以存储最后一次的认证URL
let lastAuthUrl = ''

export const authRoute = {
  // 打开授权窗口
  openAuthWindow: t.procedure
    .input<{ url: string }>()
    .action(async ({ input }) => {
      console.log('openAuthWindow', input)
      // 保存认证URL以便后续获取
      lastAuthUrl = input.url
      createAuthWindow(input.url).show()
    }),
  // 关闭授权窗口
  closeAuthWindow: t.procedure
    .action(async () => {
      console.log('closeAuthWindow')
      getAuthWindow()?.close()
    }),
  // 完成授权
  completeAuth: t.procedure
    .input<{ url: string }>()
    .action(async ({ input }) => {
      console.log('completeAuth')
      const { url } = input
      const urlObj = new URL(url)
      console.log(urlObj)
      const isLogin = await authService.verifyLogin(url)
      console.log(isLogin)
      if (!isLogin) {
        return {
          success: false,
        }
      }
      await authService.saveCookieFile()
      // 设置授权站点为已授权
      settingStore.set('authSites', settingStore.get('authSites')
        .map(site =>
          site.url === urlObj.origin
            ? { ...site, isAuthorized: true }
            : site))
      // 关闭授权窗口
      getAuthWindow()?.close()
      return {
        success: true,
      }
    }),
  // 获取当前授权URL API
  getAuthUrl: t.procedure
    .action(async () => {
      console.log('getAuthUrl 被调用, 返回:', lastAuthUrl)
      return lastAuthUrl
    }),
}
