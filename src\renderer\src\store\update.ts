import type { SoftwareUpdateProgressData, UpgradeContent } from '@main/types/update'
import { client, handlers } from '@/client'
import { compareVersions } from '@common/utils/version'
import { create } from 'zustand'

type UpdateInfoType = {
  hasUpdate: boolean
  currentVersion?: string
  normalUpgradeVersion?: string
  latestVersion?: string
  updateType?: 'force' | 'optional'
  upgradeContent?: UpgradeContent
  downloadUrl?: string
  error?: string
}

type UpdateStoreType = {
  updateInfo: UpdateInfoType
  hasUpdatePackage: boolean
  isOverMin: boolean
  isNeedOpen: boolean
  filePath?: string
  unlisten?: () => void
  progressData: SoftwareUpdateProgressData
  isFinishDownload: boolean
  isDownloadError: boolean
  isNextRemind: boolean
  isLaterInstall: boolean
  setIsDownloadError: (v: boolean) => void
  setIsNextRemind: (v: boolean) => void
  setIsLaterInstall: (v: boolean) => void
  checkForUpdate: () => Promise<void>
  checkPackageExist: () => Promise<void>
  downloadApp: () => Promise<void>
  installApp: () => Promise<void>
  listenUpdate: () => void
}

export const useUpdateStore = create<UpdateStoreType>((set, get) => ({
  updateInfo: { hasUpdate: false },
  // 是否大于最低版本
  isOverMin: true,
  // 是否需要打开更新弹窗
  isNeedOpen: false,
  hasUpdatePackage: false,
  filePath: undefined,
  unlisten: undefined,
  progressData: { success: false },
  // 是否下载完成
  isFinishDownload: false,
  // 是否下载失败
  isDownloadError: false,
  // 是否稍后提醒
  isNextRemind: false,
  // 是否稍后安装
  isLaterInstall: false,
  // 设置是否下载失败
  setIsDownloadError(v) {
    set(() => ({ isDownloadError: v }))
  },
  // 设置稍后提醒的值
  setIsNextRemind(v) {
    set(() => ({ isNextRemind: v }))
  },
  // 设置稍后安装的值
  setIsLaterInstall(v) {
    set(() => ({ isLaterInstall: v }))
  },
  // 检查更新
  async checkForUpdate() {
    const result = await client.checkSoftwareLatestVersion()
    set(() => ({ updateInfo: result.version, isOverMin: result.version.updateType !== 'force' }))
    if (result.version.currentVersion && result.version.normalUpgradeVersion && result.version.latestVersion) {
      const notOpen = compareVersions(result.version.currentVersion, result.version.normalUpgradeVersion) > 0
      set(() => ({ isNeedOpen: !notOpen }))
    }
  },
  // 检测是否有安装包
  async checkPackageExist() {
    if (!get().updateInfo.latestVersion)
      return
    const res = await client.checkSoftwarePackageExists({ version: get().updateInfo?.latestVersion! })
    set(() => ({ hasUpdatePackage: res.success }))
    if (res.filePath) {
      set(() => ({ filePath: res.filePath }))
    }
  },
  // 下载软件
  async downloadApp() {
    if (!get().updateInfo?.downloadUrl)
      return
    await client.downloadSoftwareUpdate({ url: get().updateInfo?.downloadUrl! })
  },
  // 安装软件
  async installApp() {
    if (!get().filePath)
      return
    await client.installSoftwareUpdate({ filePath: get().filePath! })
    get().setIsNextRemind(false)
    get().setIsLaterInstall(false)
  },
  listenUpdate() {
    const { unlisten } = get()
    if (unlisten) {
      return
    }
    const unlistenFunction = handlers.onSoftwareUpdateProgress.listen((data) => {
      set(() => ({ progressData: data, isDownloadError: !data.success }))
      if (data.filePath) {
        set(() => ({ isFinishDownload: true, hasUpdatePackage: true, filePath: data.filePath }))
      }
      if (!get().isNextRemind) {
        get().installApp()
      }
    })
    set(() => ({ unlisten: unlistenFunction }))
  },
}))
