import { net, session } from 'electron'

/**
 * 封装 Electron 的 net 模块发送 HTTP 请求
 * @param {object} options - 请求选项
 * @param {string} options.url - 请求的 URL
 * @param {string} [options.method] - 请求方法
 * @param {number} [options.timeout] - 请求超时时间，默认为 10000 毫秒
 * @param {object} [options.headers] - 请求头
 * @param {AbortController} [options.abortController] - 中断控制器
 */
export async function fetch(
  {
    url,
    method = 'GET',
    timeout = 10000,
    headers = {},
    abortController = new AbortController(),
  }: {
    url: string
    method?: string
    timeout?: number
    headers?: Record<string, string>
    abortController?: AbortController
  },
): Promise<Electron.IncomingMessage> {
  return await new Promise<Electron.IncomingMessage>(
    (resolve, reject) => {
      const request = net.request({
        method,
        url,
        headers,
        session: session.defaultSession,
        referrerPolicy: 'unsafe-url',
      })

      // 设置超时
      // setTimeout(() => {
      //   request.abort()
      //   reject(new Error('Request timeout'))
      // }, timeout)

      // 如果信号已经是中断状态，直接中断请求
      if (abortController.signal.aborted) {
        request.abort()
        reject(new Error('Request aborted'))
        return
      }

      // 监听中断信号
      abortController.signal.addEventListener('abort', () => {
        request.abort()
        reject(new Error('Request aborted'))
      }, { once: true })

      request.on('response', resolve)
      request.on('error', reject)
      request.end()
    },
  )
}
