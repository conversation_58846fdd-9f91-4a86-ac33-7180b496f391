import { LANGUAGE_CODES } from '@main/constants/language'
import { FILE_RENAME_LANGUAGES } from '@renderer/constants/media'

/**
 * 获取ISO 639-2标准的三位字母语言代码
 * @param language 语言标识，可以是完整的语言代码或部分语言代码
 * @returns ISO 639-2标准的三位字母语言代码
 */
export function getISOLanguageCode(language: string): string {
  if (!language)
    return 'und'

  // 转换为小写以进行匹配
  const lowerLang = language.toLowerCase()

  // 1. 尝试完整匹配
  if (LANGUAGE_CODES[lowerLang]) {
    return LANGUAGE_CODES[lowerLang]
  }

  // 2. 尝试匹配主要语言部分（如果有连字符）
  const parts = lowerLang.split('-')
  if (parts.length > 1 && LANGUAGE_CODES[parts[0]]) {
    return LANGUAGE_CODES[parts[0]]
  }

  // 3. 尝试匹配每个部分
  for (const part of parts) {
    if (LANGUAGE_CODES[part]) {
      return LANGUAGE_CODES[part]
    }
  }

  // 4. 默认返回未定义
  return 'und'
}

/**
 * 获取语言名称
 * @param language 语言标识，可以是完整的语言代码或部分语言代码
 * @returns 语言名称
 */
export function getLanguageName(language: string): string {
  // 先尝试完整匹配
  let foundLanguage = FILE_RENAME_LANGUAGES.find(
    lang => lang.value.toLowerCase() === language.toLowerCase(),
  )

  // 如果完整匹配失败，再尝试分割匹配
  if (!foundLanguage) {
    foundLanguage = FILE_RENAME_LANGUAGES.find(
      (lang) => {
        const [audioLang] = lang.value.toLowerCase().split('-')
        const [languageTag] = language.toLowerCase().split('-')
        return audioLang === languageTag
      },
    )
  }

  if (foundLanguage) {
    return foundLanguage.label
  }

  return language
}
