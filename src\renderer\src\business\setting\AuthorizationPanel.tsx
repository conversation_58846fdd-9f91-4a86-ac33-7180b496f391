import type { AuthSite } from '@main/types/setting'
import type { PathComboBoxRef } from '@renderer/components/file/PathComboBox'
import { client } from '@/client'
import { Notice } from '@/components/Notice'
import { getUrlDomain, isValidUrl, normalizeUrl } from '@/utils/url'
import {
  SettingSiteAction,
  SettingSiteInfo,
  SettingSiteItem,
} from '@renderer/client/setting-compose'
import PathComboBox from '@renderer/components/file/PathComboBox'
import { useAuthSite } from '@renderer/hooks/auth-site'
import { List } from 'flowbite-react'
import { useRef } from 'react'
import { useTranslation } from 'react-i18next'
import FaviconImg from '../../components/FaviconImg'

function SettingAuthorizationPanel() {
  const { t } = useTranslation()
  const pathComboBoxRef = useRef<PathComboBoxRef>(null)

  const { authSites, loginSite, logoutSite, addCustomSite, deleteSite }
    = useAuthSite()

  const addWebsite = async (path: string) => {
    const trimmedPath = path.trim()
    if (!trimmedPath || !isValidUrl(trimmedPath)) {
      Notice.error(t('messages.validUrlPrompt'))
      return
    }
    const authUrl = await addCustomSite(trimmedPath)
    if (authUrl) {
      await loginSite({ authUrl } as AuthSite)
    }
    pathComboBoxRef.current.clearShowPath()
  }

  return (
    <div className="flex flex-col grow relative">
      <p className="text-gray-500 text-sm font-[400] mb-2">
        {t('settings.authorizationPanelTips')}
      </p>

      <List className="space-y-3 mb-8" unstyled>
        {authSites.map(authSite => (
          <SettingSiteItem key={authSite.url}>
            <SettingSiteInfo
              icon={(
                <FaviconImg className="w-5 h-5" domain={getUrlDomain(authSite.url)} />
              )}
              siteLink={authSite.url}
              siteName={authSite.name}
            />
            <SettingSiteAction
              isAuthorized={authSite.isAuthorized}
              enableDelete={authSite.enableDelete}
              onLogout={() => logoutSite(authSite)}
              onLogin={() => loginSite(authSite)}
              onDelete={() => deleteSite(authSite)}
              logoutText={t('settings.logOut')}
              loginText={t('settings.logIn')}
              deleteText={t('settings.delete')}
            />
          </SettingSiteItem>
        ))}
      </List>

      <div className="grow flex justify-center items-end sticky bottom-4">
        <PathComboBox
          ref={pathComboBoxRef}
          className="w-[80%]"
          actionText={t('settings.addUrl')}
          placeholder={t('messages.validUrlPrompt')}
          handleValidPath={addWebsite}
          disableBlurSubmit
        />
      </div>
    </div>
  )
}

export default SettingAuthorizationPanel
