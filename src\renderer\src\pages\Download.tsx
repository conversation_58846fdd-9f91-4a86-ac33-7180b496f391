import type { VirtuosoHandle } from 'react-virtuoso'
import DownloadVirtuoso from '@/business/download/DownloadVirtuoso'
import { ReomveAllModal } from '@/business/download/MoreActions'
import { extractUrlsFromText } from '@main/utils/urls'
import DownloadConfigDropdownGroup from '@renderer/business/download/ConfigDropdownGroup'
import MultipleLinksModal from '@renderer/business/download/MultipleLinksModal'
import { Notice } from '@renderer/components/Notice'
import { useSnapany } from '@renderer/hooks/snapany'
import { useDownloadStore } from '@renderer/store/download'
import { useTaskStore } from '@renderer/store/task'
import debounce from '@renderer/utils/debounce'
import * as Sentry from '@sentry/electron/renderer'
import { Button } from 'flowbite-react'
import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { AiOutlineLink } from 'react-icons/ai'

// 主页组件
export const DownloadPage: React.FC = () => {
  const { t } = useTranslation()

  const [multipleLinks, setMultipleLinks] = useState<string[]>([])

  const { addTask } = useTaskStore()

  const { loadSetting } = useDownloadStore()
  const { mutateSettings, settings } = useSnapany()

  const loadSettingSignalRef = useRef(false)
  const downloadVirtuosoRef = useRef<VirtuosoHandle>(null)

  useEffect(() => {
    if (loadSettingSignalRef.current || !settings) {
      return
    }

    loadSetting(settings)
    loadSettingSignalRef.current = true
  }, [loadSetting, settings])

  const handleDownload = async (url: string | string[]) => {
    if (!settings) {
      return
    }
    await addTask(url)
    await mutateSettings()
  }

  // 添加批量下载处理函数
  const handleMultipleDownload = debounce(async (selectedLinks: string[]) => {
    setMultipleLinks([])

    try {
      await handleDownload(selectedLinks)
    }
    catch (error: any) {
      Sentry.captureException(`批量下载处理失败:${error}`)
      Notice.error(error.message)
    }
  }, 300)

  // 粘贴处理函数
  const handlePaste = debounce(async () => {
    try {
      const text = await navigator.clipboard.readText()
      const links = extractUrlsFromText(text)
      if (links.length > 0) {
        // 识别到剪切板里是否有多个链接，单个链接直接下载。多个链接，弹窗提示，用户选择确认后开始批量提取-下载
        if (links.length === 1) {
          await handleDownload(links[0])
        }
        else {
          // 取前10个链接
          const selectedLinks = links.slice(0, 10)
          setMultipleLinks(selectedLinks)
        }
        // 在需要滚动时调用
        downloadVirtuosoRef.current?.scrollToIndex({
          index: 0,
          align: 'start',
          behavior: 'smooth',
        })
      }
      else {
        Notice.error(t('errors.clipboardNotContainsValidUrl'))
      }
    }
    catch (err: any) {
      Sentry.captureException(`读取剪贴板失败:${err}`)
      Notice.error(err.message)
    }
  }, 300)

  return (
    <div className="flex flex-col w-full h-full ">
      <div className="bg-white shadow-sm flex items-center shrink-0 p-4">
        <section className="flex gap-2 items-center mr-4">
          <Button
            className="py-0.5"
            onClick={handlePaste}
            size="sm"
            color="blue"
            theme={{
              inner: {
                base: 'flex items-center justify-center',
              },
            }}
          >
            <AiOutlineLink className="mr-2" />
            <span className="text-[14px]">
              {' '}
              {t('download.pasteLink')}
            </span>
          </Button>
        </section>

        <DownloadConfigDropdownGroup />
      </div>

      <article className="h-full flex flex-col overflow-auto">
        <DownloadVirtuoso ref={downloadVirtuosoRef} />
      </article>
      <ReomveAllModal />
      <MultipleLinksModal
        links={multipleLinks}
        open={multipleLinks.length > 0}
        onCancel={() => setMultipleLinks([])}
        onOk={handleMultipleDownload}
      />
    </div>
  )
}
export default DownloadPage
