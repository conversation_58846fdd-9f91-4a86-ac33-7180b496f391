```mermaid
classDiagram
    class 渲染进程 {
        生成taskId()
        发起下载请求()
        更新UI()
        显示进度()
        显示错误()
    }

    class 主进程 {
        handleStartParseDownloadConvert()
        initializeDownloadTask()
        parseDownloadConvert()
        updateTaskInfo()
    }

    class Downloader {
        -instance: Downloader
        -window: BrowserWindow
        -downloadTasks: Map
        +getInstance(window): Downloader
        +initDownloadTask(url, taskId)
        +parseDownloadConvert(url, taskId)
        -updateTaskInfo(taskId, urlInfo)
    }

    class DownloadStore {
        -instance: DownloadStore
        +getInstance(): DownloadStore
        +updateTask(taskId, taskInfo)
        +getTask(taskId)
        +getTasks()
    }

    class 下载阶段 {
        +createTempDirectory()
        +getUrlInfo()
        +parseJsonToUrlList()
        +startDownloadInBackground()
        +startMergeConvert()
        +moveFiles()
        +sendProgress()
    }

    class DOWNLOAD_STATUS_ENUM {
        <<enumeration>>
        解析中
        下载中
        合并中
        已完成
        失败
    }

    渲染进程 --> 主进程: IPC通信
    主进程 --> Downloader: 创建实例
    Downloader --> DownloadStore: 存储任务信息
    Downloader --> 下载阶段: 执行下载流程
    下载阶段 --> DownloadStore: 更新任务状态
    Downloader --> DOWNLOAD_STATUS_ENUM: 使用状态枚举
    下载阶段 --> 渲染进程: 发送进度和状态
```
