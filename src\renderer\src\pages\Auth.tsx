import CollapseImg from '@/assets/collapse.png'
import { client } from '@/client'
import { useSnapany } from '@/hooks/snapany'
import { <PERSON><PERSON>, Spinner } from 'flowbite-react'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { MdRefresh } from 'react-icons/md'
import { useLocation } from 'react-router-dom'

export type errorType = {
  code: string
  title: string
  description?: string
  isSolution: boolean
}

function Auth() {
  const { t } = useTranslation()
  const location = useLocation()
  const webviewRef = useRef<Electron.WebviewTag>(null)
  const siteRef = useRef<string>('')
  const [site, setSite] = useState('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<errorType>()
  useSnapany()

  // 从URL参数中获取授权URL
  useEffect(() => {
    // 解析URL中的查询参数
    const searchParams = new URLSearchParams(location.search)

    // 先尝试从search参数中获取url
    const url = searchParams.get('url') || ''

    if (url) {
      console.log('从URL参数中获取授权URL:', url)
      // 解码URL（因为在传递时进行了编码）
      const decodedUrl = decodeURIComponent(url)
      siteRef.current = decodedUrl
      setSite(decodedUrl)
    }
  }, [location])

  const handleDidStartLoading = () => {
    console.log('开始加载页面')
    setLoading(true)
    setError(undefined)
  }

  const handleDidStopLoading = () => {
    console.log('页面停止加载')
    setLoading(false)
  }

  const handleDidFinishLoad = () => {
    console.log('页面加载完成')
    setLoading(false)
  }

  const handleDidFailLoad = (event: Event) => {
    const webviewEvent = event as unknown as Electron.DidFailLoadEvent
    const { errorCode, errorDescription, validatedURL } = webviewEvent || {}
    console.log('页面加载失败', siteRef.current, {
      errorCode,
      errorDescription,
      validatedURL,
      isMainFrame: webviewEvent.isMainFrame,
    })
    setLoading(false)

    // 只处理主框架的错误
    if (!webviewEvent.isMainFrame) {
      console.log('忽略非主框架的加载失败')
      return
    }

    let errData
    switch (errorDescription) {
      case 'ERR_FAILED':
        errData = {
          code: errorDescription,
          title: t('auth.cannotAccess'),
          description: t('auth.errorDescription4', { domain: siteRef.current }),
          isSolution: false,
        }
        break
      case 'ERR_NETWORK_CHANGED':
        errData = {
          code: errorDescription,
          title: t('auth.connectInterrupt'),
          description: t('auth.errorDescription6'),
          isSolution: false,
        }
        break
      case 'ERR_CONNECTION_CLOSED':
        errData = {
          code: errorDescription,
          title: t('auth.cannotAccess'),
          description: t('auth.errorDescription1', { domain: siteRef.current }),
          isSolution: true,
        }
        break
      case 'ERR_CONNECTION_REFUSED':
        errData = {
          code: errorDescription,
          title: t('auth.cannotAccess'),
          description: t('auth.errorDescription3', { domain: siteRef.current }),
          isSolution: true,
        }
        break
      case 'ERR_NAME_NOT_RESOLVED':
        errData = {
          code: errorDescription,
          title: t('auth.cannotAccess'),
          description: t('auth.errorDescription5', { domain: siteRef.current }),
          isSolution: false,
        }
        break
      case 'ERR_INTERNET_DISCONNECTED':
        errData = {
          code: errorDescription,
          title: t('auth.noInternet'),
          isSolution: true,
        }
        break
      case 'ERR_CONNECTION_TIMED_OUT':
        errData = {
          code: errorDescription,
          title: t('auth.cannotAccess'),
          description: t('auth.errorDescription2', { domain: siteRef.current }),
          isSolution: true,
        }
        break
      case 'ERR_PROXY_CONNECTION_FAILED':
        errData = {
          code: errorDescription,
          title: t('auth.noInternet'),
          description: t('auth.errorDescription7'),
          isSolution: true,
        }
        break
      case 'ERR_EMPTY_RESPONSE':
        errData = {
          code: errorDescription,
          title: t('auth.noWork'),
          description: t('auth.errorDescription8', { domain: siteRef.current }),
          isSolution: false,
        }
        break
      case 'ERR_CONNECTION_RESET':
        errData = {
          code: errorDescription,
          title: t('auth.cannotAccess'),
          description: t('auth.errorDescription9'),
          isSolution: true,
        }
        break
      default:
        errData = { code: errorDescription, title: t('auth.cannotAccess'), description: t('auth.errorDescription4', { domain: siteRef.current }), isSolution: false }
    }
    setError(errData)
  }

  const handleDomReady = () => {
    console.log('DOM ready')
    const webview = webviewRef.current
    if (webview) {
      // 输出当前 webview 状态
      console.log('Webview 状态:', {
        url: webview.getURL(),
        title: webview.getTitle(),
        isLoading: webview.isLoading(),
        isWaitingForResponse: webview.isWaitingForResponse(),
      })
    }
  }

  const handleDidNavigate = (event: Event) => {
    const navEvent = event as unknown as Electron.DidNavigateEvent
    console.log('页面导航:', {
      url: navEvent.url,
    })
  }

  const handleCrashed = () => {
    console.log('页面崩溃')
    setError({
      code: 'ERR_EMPTY_RESPONSE',
      title: t('auth.noWork'),
      description: t('auth.errorDescription8', { domain: siteRef.current }),
      isSolution: false,
    })
    setLoading(false)
  }

  const handleUnresponsive = () => {
    console.log('页面无响应')
    setError({
      code: 'ERR_EMPTY_RESPONSE',
      title: t('auth.noWork'),
      description: t('auth.errorDescription8', { domain: siteRef.current }),
      isSolution: false,
    })
    setLoading(false)
  }

  const handlePluginCrashed = () => {
    console.log('插件崩溃')
    setError({
      code: 'ERR_EMPTY_RESPONSE',
      title: t('auth.noWork'),
      description: t('auth.errorDescription8', { domain: siteRef.current }),
      isSolution: false,
    })
    setLoading(false)
  }

  const handleConsoleMessage = (event: Event) => {
    const messageEvent = event as unknown as Electron.ConsoleMessageEvent
    console.log('页面控制台消息:', messageEvent.message)
  }

  useEffect(() => {
    const webview = webviewRef.current
    if (webview) {
      webview.addEventListener('did-start-loading', handleDidStartLoading)
      webview.addEventListener('did-stop-loading', handleDidStopLoading)
      webview.addEventListener('did-finish-load', handleDidFinishLoad)
      webview.addEventListener('did-fail-load', handleDidFailLoad)
      webview.addEventListener('dom-ready', handleDomReady)
      webview.addEventListener('crashed', handleCrashed)
      webview.addEventListener('unresponsive', handleUnresponsive)
      webview.addEventListener('plugin-crashed', handlePluginCrashed)
      webview.addEventListener('console-message', handleConsoleMessage)
      webview.addEventListener('did-navigate', handleDidNavigate)

      // 设置 webview 参数
      webview.setAttribute('webpreferences', 'nodeIntegration=false, webSecurity=true, allowRunningInsecureContent=false')
    }

    return () => {
      if (webview) {
        webview.removeEventListener('did-start-loading', handleDidStartLoading)
        webview.removeEventListener('did-stop-loading', handleDidStopLoading)
        webview.removeEventListener('did-finish-load', handleDidFinishLoad)
        webview.removeEventListener('did-fail-load', handleDidFailLoad)
        webview.removeEventListener('dom-ready', handleDomReady)
        webview.removeEventListener('crashed', handleCrashed)
        webview.removeEventListener('unresponsive', handleUnresponsive)
        webview.removeEventListener('plugin-crashed', handlePluginCrashed)
        webview.removeEventListener('console-message', handleConsoleMessage)
        webview.removeEventListener('did-navigate', handleDidNavigate)
      }
    }
  }, [])

  const handleRefresh = () => {
    console.log('刷新页面')
    const webview = webviewRef.current
    if (webview) {
      console.log('刷新前 Webview 状态:', {
        url: webview.getURL(),
        title: webview.getTitle(),
        isLoading: webview.isLoading(),
        isWaitingForResponse: webview.isWaitingForResponse(),
      })
    }
    setLoading(true)
    setError(undefined)
    webviewRef.current?.reload()
  }

  const handleCancel = () => {
    client.closeAuthWindow()
  }

  const handleOk = async () => {
    const result = await client.completeAuth({ url: siteRef.current })
    if (!result.success) {
      handleCancel()
    }
  }

  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex items-center justify-between px-4 py-3">
        <div className="flex items-center gap-2">
          <p className="text-base font-semibold text-gray-900">
            {t('auth.logInToX')}
            { site }
          </p>
          <MdRefresh className="text-blue-700 cursor-pointer" onClick={handleRefresh} />
        </div>
        <div className="flex items-center gap-4">
          <button className="rounded-lg box-border border border-blue-700 py-2 px-3 text-sm text-blue-700 cursor-pointer" onClick={handleCancel}>{t('auth.cancel')}</button>
          <Button disabled={loading || !!error} onClick={handleOk}>{t('auth.done')}</Button>
        </div>
      </div>
      <div className="grow relative">
        {loading && (
          <div className="absolute w-full h-full bg-white flex items-center justify-center">
            <Spinner className="w-25 h-25 fill-blue-600" />
          </div>
        )}
        {error && (
          <div className="absolute w-full h-full bg-white flex items-center justify-center flex-col gap-4">
            <div className="flex flex-col max-w-[60%]">
              <img src={CollapseImg} alt="collapse" className="w-18 h-18 mb-10" />
              <h1 className="text-2xl mb-4">{error?.title}</h1>
              {error?.description && (
                <p className="mb-4 text-gray-700 text-[15px]">
                  {error?.description}
                </p>
              )}
              {error?.isSolution && (
                <div className="mb-3 text-[15px] text-gray-700">
                  <p>{t('auth.try')}</p>
                  <ul className="list-disc pl-10">
                    <li>{t('auth.tip1')}</li>
                    <li>{t('auth.tip2')}</li>
                  </ul>
                </div>
              )}
              <p className="text-xs text-gray-700">{error?.code}</p>
            </div>
          </div>
        )}
        {/* @ts-expect-error allowpopups要求值是字符串 */}
        <webview ref={webviewRef} id="auth-webview" className="w-full h-full" src={site} allowpopups="true"></webview>
      </div>
    </div>
  )
}

export default Auth
