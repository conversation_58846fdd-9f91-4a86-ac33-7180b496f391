```mermaid
sequenceDiagram
    participant 渲染进程
    participant 本地存储
    participant 主进程
    participant yt-dlp
    participant 文件系统

    渲染进程->>渲染进程: 生成taskId
    渲染进程->>主进程: start-parse-download-convert(url, taskId)

    主进程->>本地存储: 初始化任务(taskId, url, 状态: 解析中)
    主进程->>渲染进程: 返回初始化成功
    渲染进程->>本地存储: 读取任务列表
    渲染进程->>渲染进程: 更新UI显示

    主进程->>主进程: 加载当前设置
    主进程->>文件系统: 创建临时目录

    主进程->>yt-dlp: 调用解析URL
    yt-dlp-->>主进程: 返回JSON信息

    主进程->>本地存储: 更新任务信息(封面图/标题)
    主进程->>渲染进程: 发送状态更新(下载中)
    渲染进程->>本地存储: 读取更新后的任务列表
    渲染进程->>渲染进程: 更新UI显示(封面图/标题)

    主进程->>主进程: 解析JSON获取下载URL列表
    主进程->>主进程: 计算总下载大小

    loop 下载过程
        主进程->>文件系统: 下载文件
        主进程->>渲染进程: 发送下载进度
        渲染进程->>渲染进程: 更新进度条
    end

    主进程->>渲染进程: 发送下载完成(进度100%)

    主进程->>渲染进程: 发送状态更新(合并中)

    loop 合并转换过程
        主进程->>文件系统: 处理文件(合并/转换)
        主进程->>渲染进程: 发送合并进度
        渲染进程->>渲染进程: 更新进度条
    end

    主进程->>文件系统: 移动文件到目标目录
    主进程->>渲染进程: 发送状态更新(已完成)
    主进程->>本地存储: 更新任务状态(完成)
    主进程->>文件系统: 清理临时目录

    渲染进程->>本地存储: 读取最终任务列表
    渲染进程->>渲染进程: 更新UI显示

    Note over 主进程,文件系统: 每个步骤都有错误处理机制
```
